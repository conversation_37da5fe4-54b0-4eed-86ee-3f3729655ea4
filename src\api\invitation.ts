import axios from 'axios';
import {
    Request_SubmitInvitationTicketModel,
    Response_InvitationExcelInfoModel,
    Response_MerchantValidGoods
} from "@/types/api-type/invitation";
import {getToken} from "@/utils/auth";
/**
 * @description: 查询部门树形列表 <AUTHOR>
 *    * @param {*}
 *    * @return {*}
 */


/**
 * @description:邀请票添加接口 <AUTHOR>
 */
export const InvitationAddApi = {
  /**
   * 获取商户有效商品
   * @return {Response_MerchantValidGoods[]} 商户有效商品列表
   */
   getMerchantValidGoods : ()=>{
    return axios.get<Response_MerchantValidGoods[]>( '/api/pd/v2/merchant/invitation/goodsList')
  },
  /**
   * 下载邀请票模板文件
   */
   getMerchantInvitationTicketTemplateFile : ()=>{
      // location.href =
      //     (import.meta.env.VITE_API_BASE_URL as any) +
      //     `/api/pd/v2/merchant/download/excelTemplate`;

          return axios.get(
              '/api/pd/v2/merchant/download/excelTemplate',
              {
                  baseURL: import.meta.env.VITE_API_BASE_URL,
                  responseType: 'blob', // 必须设置为 blob 才能处理文件流
                  headers: {
                      Authorization: getToken() ?? ''
                  }
              })
  },
  /**
   * 解析excel
   * @param goodsId 选择的商户下有效的商品Id
   * @param file excel文件
   */
   analysisExcel : (goodsId:string,file:File)=>{
    const formData = new FormData();
    formData.append('file',file)
    formData.append('goodsId',goodsId)
    return axios.post<Response_InvitationExcelInfoModel>(`/api/pd/v2/merchant/analysis/excel`,formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

  },
    /**
     * 提交创建商户邀请票
     * @param data {Request_SubmitInvitationTicketModel}
     */
   submitCreateMerchantInvitationTicket : (data:Request_SubmitInvitationTicketModel)=>{
    return axios.post('/api/pd/v2/merchant/invitation/provide/coupons',data)
  },
}


