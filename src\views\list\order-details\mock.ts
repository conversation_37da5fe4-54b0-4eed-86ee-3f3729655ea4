import Mock from 'mockjs';
import qs from 'query-string';
import setupMock, { successResponseWrap } from '@/utils/setup-mock';
import { GetParams } from '@/types/global';

const { Random } = Mock;

const data = Mock.mock({
  'list|55': [
    {
      'number|2-3': /[0-9]/,
      'userCN|8': /[0-9]/,
      'contact': /^1[3-9]\d{9}$/,
      'loginNumber': /^1[3-9]\d{9}$/,
      'commodityName|1': ['常州漫展', '苏州漫展', '北京漫展', '上海漫展'],
      'couponsName|1': ['优惠卷1', '优惠卷2', '优惠卷3', '优惠卷4'],
      'releaseTime': function () {
        const start = new Date('2025-01-01').getTime();
        const end = new Date('2025-06-18').getTime();
        return new Date(start + Math.random() * (end - start))
          .toISOString()
          .split('T')[0];
      },
      'sessionDate': function () {
        const start = new Date('2025-06-19').getTime();
        const end = new Date('2025-12-31').getTime();
        return new Date(start + Math.random() * (end - start))
          .toISOString()
          .split('T')[0];
      },
      'reason|1': [
        '库存不足',
        '促销活动结束',
        '商品下架',
        '系统错误',
        '其他原因',
      ],
      'operator|1': ['admin', 'operator1', 'operator2', 'system'],
      'status|1': ['online', 'offline', 'invalid'],
      'operation|1': ['作废'],
      'filterType|1': ['artificial', 'rules'],
      'createdTime': Random.datetime(),
    },
  ],
});

setupMock({
  setup() {
    Mock.mock(new RegExp('/api/list/policy'), (params: GetParams) => {
      const { current = 1, pageSize = 10 } = qs.parseUrl(params.url).query;
      const p = current as number;
      const ps = pageSize as number;
      return successResponseWrap({
        list: data.list.slice((p - 1) * ps, p * ps),
        total: 55,
      });
    });
  },
});
