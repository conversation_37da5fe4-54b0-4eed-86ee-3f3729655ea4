<template>
  <div class="radio-demo-tip-card">
    <div class="radio-demo-tip-title">单选</div>
    <div class="radio-demo-tip-desc"
      >通过单选模式高效收集用户明确选择。<br />
      使用场景：收集用户意向、投票选择、活动调研。</div
    >
    <div class="radio-demo-tip-example">
      <div class="radio-demo-tip-radio demo-first"></div>
      <div
        :class="['radio-demo-tip-radio', selectedDemo === '1' && 'selected']"
      >
        <input
          type="radio"
          name="demo-radio"
          value="1"
          v-model="selectedDemo"
        />
        <span style="flex: 1"></span>
      </div>
      <div
        :class="['radio-demo-tip-radio', selectedDemo === '2' && 'selected']"
      >
        <input
          type="radio"
          name="demo-radio"
          v-model="selectedDemo"
          value="2"
        />
        <span style="flex: 1"></span>
      </div>
      <div
        :class="['radio-demo-tip-radio', selectedDemo === '3' && 'selected']"
      >
        <input
          type="radio"
          name="demo-radio"
          v-model="selectedDemo"
          value="3"
        />
        <span style="flex: 1"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const selectedDemo = ref('2');
</script>

<style scoped lang="less">
  .radio-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .radio-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .radio-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .radio-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding:12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .radio-demo-tip-radio {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 6px;
    margin-bottom: 2px;
    padding: 0 8px;
    height: 20px;
    width: 66%;
    box-sizing: border-box;
    border: none;
  }
  .demo-first {
    width: 100%;
    background: #6aa1ff;
		margin-bottom: 8px;
  }
  .radio-demo-tip-radio.selected {
    background: #94bfff;
  }
  .radio-demo-tip-radio input[type='radio'] {
    accent-color: #4080ff;
  }
</style>
