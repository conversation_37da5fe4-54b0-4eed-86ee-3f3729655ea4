import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { DefineComponent } from 'vue';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import {
  ComponentUnifiedAttributeModel,
  KVLModel,
} from '@/components/module/type/attribute-model-unified';
import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
import { ServiceEnumModuleStructType } from '@/components/module/service/service-enum-module-struct-type';
import { ConvertUtils } from '@/utils/convert';
import { ValidateUtils } from '@/utils/validate';
import Error from '@/views/result/error/index.vue';
import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
import useComponentStore from '@/components/module/store/component';
import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
import { UploadUtils } from '@/components/module/tools/upload-utils';

export class CustomSingleMultipleModule implements ICustomComponent {
  /**  模块名称 */
  private readonly _moduleName: string = '选择框';

  /**  模块类型 */
  private _formatType: EnumComponentFormatType = EnumComponentFormatType.Radio;

  /**  模块结构类型 */
  private _structType: EnumComponentStructType = EnumComponentStructType.RADIO;

  /**  模块实例 */
  private readonly _showModuleRef: DefineComponent | undefined;

  /**  模块实例 */
  private readonly _settingModuleRef: DefineComponent | undefined;

  /**  模块属性 */
  private readonly _moduleExpandAttribute: ComponentUnifiedAttributeModel;

  /** 构造函数 */
  constructor(attribute: ComponentUnifiedAttributeModel) {
    if (
      attribute.type !== EnumComponentStructType.RADIO &&
      attribute.type !== EnumComponentStructType.CHECKBOX
    ) {
      throw new Error('模板类型错误');
    }
    this._structType = attribute.type;
    // 判断格式是否正确
    if (
      !ServiceEnumModuleFormatType.ValidateSingleMultipleType(attribute.format)
    ) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = attribute.format;

    this._showModuleRef = ServiceEnumModuleStructType.GetShowModuleComponent(
      this._structType
    );
    this._settingModuleRef =
      ServiceEnumModuleStructType.GetSettingModuleComponent(this._structType);

    this._moduleExpandAttribute = ConvertUtils.DeepCopy(attribute);
    this.validateParams();
  }

  /**
   * 验证参数
   */
  private validateParams = (): void => {
    if (this._moduleExpandAttribute.max) {
      const dMinCount = this._moduleExpandAttribute.min ?? 0;
      if (this._moduleExpandAttribute.max < dMinCount) {
        this._moduleExpandAttribute.max = dMinCount;
      }
    }
  };

  /**  获取模块名称 */
  public getComponentName(): string {
    return this._moduleName;
  }

  /**  获取模块结构类型 */
  public getComponentStructType(): EnumComponentStructType {
    return this._structType;
  }

  /**  获取模块类型 */
  public getComponentType(): EnumComponentFormatType {
    return this._formatType;
  }

  /**  获取模块实例 */
  public getShowModuleRef(): DefineComponent | undefined {
    return this._showModuleRef;
  }

  /**  获取模块实例 */
  public getSettingModuleRef(): DefineComponent | undefined {
    return this._settingModuleRef;
  }

  /**  获取模块属性 */
  public getComponentUnifiedAttribute(): ComponentUnifiedAttributeModel {
    return this._moduleExpandAttribute;
  }

  /**
   * 设置模块结构模型
   * @param type
   */
  public setModuleStructType(type: EnumComponentStructType) {
    if (
      type !== EnumComponentStructType.RADIO &&
      type !== EnumComponentStructType.CHECKBOX
    ) {
      throw new Error('模板类型错误');
    }

    this._structType = type;

    const format = type as unknown as EnumComponentFormatType;

    if (!ServiceEnumModuleFormatType.ValidateSingleMultipleType(format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = format;

    this._moduleExpandAttribute.format = format;
    this._moduleExpandAttribute.type = type;
  }

  /**
   * 获取单选/多选模块属性 -> 单属性
   * @param key
   * @param value
   */
  public setComponentExpandAttribute(key: string, value: any) {
    // 验证属性是否存在
    if (!(key in this._moduleExpandAttribute)) {
      throw new Error(`模块属性 ${key} 不存在`);
    }
    const typeKey = typeof (this._moduleExpandAttribute as any)[key];
    // 验证属性类型
    if (typeKey !== 'undefined' && typeof value !== typeKey) {
      throw new TypeError(`模块属性 ${key} 的类型与值不匹配`);
    }
    // 设置属性值
    (this._moduleExpandAttribute as any)[key] = ConvertUtils.DeepCopy(value);
  }

  /** 获取组件值属性的值 */
  // eslint-disable-next-line class-methods-use-this
  public getComponentValueAttribute(): any[] {
    return [];
  }

  /**
   * 设置模块格式类型
   * @param format
   */
  public async setComponentFormatType(format: EnumComponentFormatType) {
    if (!ServiceEnumModuleFormatType.ValidateSingleMultipleType(format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = format;

    const type = format as unknown as EnumComponentStructType;

    if (
      type !== EnumComponentStructType.RADIO &&
      type !== EnumComponentStructType.CHECKBOX
    ) {
      throw new Error('模板类型错误');
    }
    this._structType = type;

    this._moduleExpandAttribute.format = format;
    this._moduleExpandAttribute.type = type;

    this._moduleExpandAttribute.max = undefined;
    this._moduleExpandAttribute.min = undefined;

    const store = useComponentStore();
    await store
      .getMetaComponent(this._structType)
      .then((res: ComponentUnifiedAttributeModel | undefined) => {
        Object.assign(this._moduleExpandAttribute, res);
        this.validateParams();
      });
  }

  private verifyFormatType(): string[] {
    return ServiceEnumModuleFormatType.ValidateInputType(this._formatType)
      ? []
      : ['format'];
  }

  public getComponentExampleAttribute(): ShowComponentUploadFileItemModel[] {
    return ValidateUtils.validateAndMapArray<
      string,
      ShowComponentUploadFileItemModel
    >(
      ConvertUtils.convertJson2Array(this._moduleExpandAttribute.exampleImages),
      [ValidateUtils.isString],
      undefined,
      UploadUtils.convertStringToShowComponentUploadFileItem
    );
  }

  /**
   * 设置示例图片
   * @param exampleImages
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentExampleImages(exampleImages: string[] | undefined): void {
    this._moduleExpandAttribute.exampleImages = ValidateUtils.isNullOrEmpty(
      exampleImages
    )
      ? []
      : exampleImages;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyExampleImages(): string[] {
    return [];
  }

  /**
   * 获取文件最大大小限制
   * @param fileMaxSize
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentFileMaxSize(fileMaxSize: number | undefined): void {
    throw new Error('该类型组件没有【文件大小限制】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileMaxSize(): string[] {
    return [];
  }

  /**
   * 获取文件类型限制
   * @param fileTypes
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentFileTypes(fileTypes: string[] | undefined): void {
    throw new Error('该类型组件没有【文件类型】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileTypes(): string[] {
    return [];
  }

  /**
   * 获取数量统一限制
   * @param length
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentLength(length: number | undefined): void {
    if (length ?? 0 < 2) {
      length = 2;
    }
    this._moduleExpandAttribute.length = length;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyLength(): string[] {
    return this._moduleExpandAttribute.length ?? 0 < 2 ? ['length'] : [];
  }

  /**
   * 获取数量最大限制
   * @param max
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMax(max: number | undefined): void {
    if (this._formatType !== EnumComponentFormatType.CheckBox) {
      // throw new Error('该类型组件没有【最大值】属性.');
      this._moduleExpandAttribute.max = undefined;
      return;
    }
    const { min, length } = this._moduleExpandAttribute;
    if (!ValidateUtils.isValuePositiveNumber(max)) {
      max = length;
    }
    // 判断是否限制最小值
    if (ValidateUtils.isValuePositiveNumber(min)) {
      max = Math.max(
        min ?? 0,
        max ??
          length ??
          ActivitySettingLimitValue.activityModuleSingleMultipleLabelMaxCount
      );
    }
    this._moduleExpandAttribute.max = max;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMax(): string[] {
    return [];
  }

  /**
   * 设置组件最大长度
   * @param maxLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMaxLength(maxLength: number | undefined): void {
    throw new Error('该类型组件没有【最大长度】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMaxLength(): string[] {
    return [];
  }

  // eslint-disable-next-line class-methods-use-this
  public setComponentMin(min: number | undefined): void {
    if (this._formatType !== EnumComponentFormatType.CheckBox) {
      // throw new Error('该类型组件没有【最小值】属性.');
      this._moduleExpandAttribute.min = undefined;
    }
    if (!ValidateUtils.isValuePositiveNumber(min)) {
      min = 1;
    }
    this._moduleExpandAttribute.min = min;
    const { max } = this._moduleExpandAttribute;
    // 判断是否限制最小值
    if (ValidateUtils.isValuePositiveNumber(max)) {
      this._moduleExpandAttribute.min = Math.min(min ?? 1, max ?? 2);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMin(): string[] {
    return [];
  }

  /**
   * 设置组件最小长度
   * @param minLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMinLength(minLength: number | undefined): void {
    throw new Error('该类型组件没有【最小长度】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMinLength(): string[] {
    return [];
  }

  /**
   * 设置组件选项
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentOptions(options: KVLModel[] | undefined): void {
    this._moduleExpandAttribute.options = ValidateUtils.isNullOrEmpty(options)
      ? [
          {
            key: '1',
            value: '1',
            label: '选项1',
          },
          {
            key: '2',
            value: '2',
            label: '选项2',
          },
        ]
      : options;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOptions(): string[] {
    return ValidateUtils.isNullOrEmpty(this._moduleExpandAttribute.options)
      ? ['options']
      : [];
  }

  /**
   * 设置组件是否开启图片原图上传
   * @param originalImage
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentOriginalImage(originalImage: boolean | undefined): void {
    this._moduleExpandAttribute.originalImage = originalImage;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOriginalImage(): string[] {
    return [];
  }

  /**
   * 设置组件的placeholder
   * @param placeholder
   */
  // eslint-disable-next-line class-methods-use-this,no-shadow
  public setComponentPlaceholder(placeholder: string | undefined): void {
    throw new Error('该类型组件没有【placeholder】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyPlaceholder(): string[] {
    return [];
  }

  /**
   * 设置组件的图片是否可重命名
   * @param rename
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentRename(rename: boolean | undefined): void {
    throw new Error('该类型组件没有【重命名】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyRename(): string[] {
    return [];
  }

  /**
   * 设置组件的模版
   * @param templates
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentTemplates(templates: KVLModel[] | undefined): void {
    throw new Error('该类型组件没有【模版】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyTemplates(): string[] {
    return [];
  }

  /**
   * 设置组件的值
   * @param value
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentValue(value: unknown): void {
    throw new Error('该类型组件没有【值】属性.');
  }

  /**
   * 校验组件的值的有效性
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  private verifyValue(): string[] {
    return [];
  }

  /**
   * 清理组件值属性的值，将无效数据清理出去
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  private cleanValue(): void {}

  public verifyComponentAttribute(): string[] {
    const attr = this._moduleExpandAttribute;

    if (!attr) {
      console.warn('Module expand attribute is not defined.');
      return ['attr'];
    }

    return [
      ...this.verifyFormatType(),
      ...this.verifyPlaceholder(),
      ...this.verifyMaxLength(),
      ...this.verifyMinLength(),
      ...this.verifyLength(),
      ...this.verifyMax(),
      ...this.verifyMin(),
      ...this.verifyFileMaxSize(),
      ...this.verifyFileTypes(),
      ...this.verifyOptions(),
      ...this.verifyOriginalImage(),
      ...this.verifyRename(),
      ...this.verifyValue(),
      ...this.verifyTemplates(),
      ...this.verifyExampleImages(),
    ];
  }
}
