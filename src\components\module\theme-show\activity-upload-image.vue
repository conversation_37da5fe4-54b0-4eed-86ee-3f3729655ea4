<script setup lang="ts">
  import {
    computed,
    defineProps,
    PropType,
    reactive,
    ref,
    watch,
    watchEffect,
  } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

  import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { ValidateUtils } from '@/utils/validate';
  import { Message, Modal } from '@arco-design/web-vue';
  import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';
  import { AliOSS } from '@/utils/AliOSS';
  import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
  import { debounce } from 'lodash';
  // 父组件传参
  const props = defineProps({
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    // 是否可编辑
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  /** 获取组件格式 */
  const formatAttr = computed(() => props.item?.getComponentType());
  /** 获取组件属性 */
  const itemAttr = computed(() => props.item?.getComponentUnifiedAttribute());
  /** 获取组件是否开启图片原图上传 */
  const renameAttr = computed(() => {
    return itemAttr.value?.rename;
  });
  /** 获取组件值 */
  const valueAttr = computed(() => props.item?.getComponentValueAttribute());
  /** 本地上传文件列表 */
  const localFilList = ref<ShowComponentUploadFileItemModel[]>(valueAttr.value);

  watch(
    () => valueAttr.value,
    (newValue) => {
      localFilList.value = newValue;
    },
    { immediate: true }
  );

  /** 计算组件数量限制 */
  const maxLimit = computed(() => {
    // 如果有统一限制，则使用统一限制
    if (ValidateUtils.isValuePositiveNumber(itemAttr.value?.length)) {
      return itemAttr.value?.length;
    }
    // 如果有最大值，则使用最大值
    if (itemAttr.value?.max) {
      return itemAttr.value?.max;
    }
    // 如果有最小值，则使用最小值
    if (itemAttr.value?.min) {
      return itemAttr.value?.min;
    }
    // 如果没有限制，则使用默认限制
    return ActivitySettingLimitValue.activityModuleUploadFileCountMax;
  });
  /** 计算图像区域宽度 -- 如果有重命名->180；否则->80 */
  const imageShowWidth = computed(() => (renameAttr.value ? 180 : 80));

  // 锁定属性值
  const isLock = ref(false);
  // 修改组件值
  const lockToAttributeValue = () => {
    if (!isLock.value) {
      try {
        isLock.value = true;
        props.item?.setComponentValue(localFilList.value);
      } finally {
        isLock.value = false;
      }
    }
  };

  // 创建OSS上传类实例
  const aliOSS = new AliOSS();

  // 使用封装后的上传方法
  const uploadFile = async (option: RequestOption) => {
    await aliOSS.uploadFile(
      option,
      localFilList, // 响应式数组引用
      (percent, e) => {
        // 进度回调
        console.log(`上传进度: ${percent}%`);
      },
      (response) => {
        Message.success('✅ 上传成功');
        lockToAttributeValue();
      },
      (error) => {
        Modal.error({
          title: '🌐 网络错误，请检查连接',
          content: `文件上传失败，原因为：${error}`,
        });
      },
      (reason) => {
        Modal.warning({
          title: '❌ 上传失败',
          content: `文件上传失败，原因为：${reason}`,
        });
      }
    );
  };

  /**
   * 重命名
   * @param index
   * @param name
   * @param ev
   */
  const fileChangedName = (index: number, name: string, ev: FocusEvent) => {
    debounce(() => {
      // console.log('fileChangedName', localFilList.value[index].label, ev);
      if (name == null) {
        name = '';
      }
      lockToAttributeValue();
    }, 300);
  };

  /** 图片预览显示参数 */
  const imagePreview = reactive({
    url: '',
    visible: false,
  });

  /**
   * 图片显示预览
   * @param url 图片地址
   */
  const imageShowPreview = (url: string) => {
    imagePreview.url = url;
    imagePreview.visible = true;
  };
  /**
   * 关闭图片预览
   */
  const imagePreviewClose = () => {
    imagePreview.url = '';
    imagePreview.visible = false;
  };

  /**
   * 文件删除
   * @param index {number} 索引
   */
  const fileDelete = (index: number) => {
    if (index === localFilList.value.length - 1) {
      localFilList.value.splice(index, 1);
    } else {
      localFilList.value[index].url = '';
      localFilList.value[index].progress = {
        percent: 0,
        status: EnumFileUploadStatue.Empty,
      };
    }
    lockToAttributeValue();
  };
</script>

<template>
  <div v-if="item" class="activity-component-block">
    <div class="body">
      <!--   EnumComponentFormatType.Image   -->
      <div
        v-if="formatAttr === EnumComponentFormatType.Image"
        class="official-image-block"
      >
        <div
          v-for="(llItem, index) in localFilList"
          :key="index"
          class="image-item-block"
        >
          <div
            v-if="llItem.progress.status === EnumFileUploadStatue.Success"
            class="image-space"
          >
            <div class="u-image">
              <img
                :src="llItem.url"
                :width="imageShowWidth"
                height="80"
                loading="lazy"
                style="object-fit: fill"
              />
              <div class="overlay">
                <icon-eye
                  size="20"
                  style="color: #fff"
                  @click="imageShowPreview(llItem.url)"
                />
                <IconDelete
                  size="20"
                  style="color: #fff; margin-left: 10px"
                  @click="fileDelete(index)"
                />
              </div>
            </div>
          </div>
          <div v-else class="image-space">
            <a-upload
              v-if="llItem.progress.status === EnumFileUploadStatue.Empty"
              action="/"
              :data="index"
              :custom-request="uploadFile"
              :show-file-list="false"
              :auto-upload="true"
            >
              <template #upload-button>
                <div class="arco-upload-list-item" @click="doClick">
                  <div class="arco-upload-picture-card">
                    <div class="arco-upload-picture-card-text">
                      <IconPlus size="20" />
                      <div class="add-content"> 添加图片</div>
                    </div>
                  </div>
                </div>
              </template>
            </a-upload>
            <div
              v-else-if="
                llItem.progress.status === EnumFileUploadStatue.UpLoading
              "
              class="u-image"
              style="
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <a-progress
                :percent="llItem.progress.percent"
                type="circle"
                animation
              />
            </div>
            <div v-else class="u-image">
              <img
                :src="llItem.url"
                :width="imageShowWidth"
                height="80"
                loading="lazy"
                style="object-fit: fill"
              />
              <div class="overlay">
                <icon-eye
                  size="20"
                  style="color: #fff"
                  @click="imageShowPreview(llItem.url)"
                />
                <IconDelete
                  size="20"
                  style="color: #fff; margin-left: 10px"
                  @click="fileDelete(index)"
                />
              </div>
            </div>
          </div>

          <div v-if="renameAttr" class="input-block">
            <a-input
              v-model="llItem.label"
              type="text"
              placeholder="请输入图片名称"
              :max-length="
                ActivitySettingLimitValue.activityModuleGroupTitleMaxLength
              "
              show-word-limit
            />
          </div>
        </div>
        <div
          v-if="
            ValidateUtils.isNullOrEmpty(localFilList) ||
            localFilList.length < maxLimit
          "
          class="image-item-block"
        >
          <div class="image-space">
            <a-upload
              action="/"
              :custom-request="uploadFile"
              :show-file-list="false"
              :auto-upload="true"
              multiple
              :limit="maxLimit - localFilList.length"
            >
              <template #upload-button>
                <div class="arco-upload-list-item">
                  <div class="arco-upload-picture-card">
                    <div class="arco-upload-picture-card-text">
                      <IconUpload size="20" />
                      <div
                        style="
                          margin-top: 5px;
                          font-weight: 400;
                          font-size: 12px;
                          line-height: 20px;
                        "
                      >
                        添加图片</div
                      >
                    </div>
                  </div>
                </div>
              </template>
            </a-upload>
          </div>
        </div>
      </div>
      <!--   EnumComponentFormatType.UploadFile   -->
      <div v-else class="file-block">
        <div
          v-for="(llItem, index) in localFilList"
          :key="index"
          class="image-item-block"
        >
          <div class="image-space">
            <div class="u-image file-image">
              <IconPlus size="20" />
              <div class="add-content"> 添加图片</div>
            </div>
          </div>

          <div v-if="renameAttr" class="input-block">
            <a-input
              v-model="llItem.label"
              type="text"
              placeholder="请输入图片名称"
              :max-length="
                ActivitySettingLimitValue.activityModuleGroupTitleMaxLength
              "
              show-word-limit
              @blur="fileChangedName(index, llItem.label)"
            />
          </div>
        </div>

        <div v-if="localFilList.length === 0" class="image-item-block">
          <div class="image-space">
            <div class="u-image file-image">
              <IconPlus size="20" />
              <div class="add-content"> 添加图片</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-image-preview
      v-model:visible="imagePreview.visible"
      :src="imagePreview.url"
      @close="imagePreviewClose"
    />
  </div>
</template>

<style scoped lang="less">
  .body {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    /* 默认样式：自动换行，每列最小 200px，最大 auto */
    .official-image-block {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      /* 可选：添加响应式图片或内容填充 */
      .image-item-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 12px;
        text-align: center;
        overflow: hidden;
        max-width: 160px;

        .image-space {
          height: auto;
          margin-bottom: 10px;
          position: relative;
          display: inline-block;
          overflow: hidden;

          :deep(.arco-image) {
            width: 180px !important;
            .arco-image-img {
              //width: 80px !important;
              //&:hover {
              //  background: rgba(0, 0, 0, 0.5);
              //  z-index: 99;
              //}
            }

            .arco-image-footer-extra {
              padding-left: 0 !important;

              .arco-input-wrapper {
                padding: 0 !important;

                .arco-input {
                  width: 120px;
                }
              }
            }
          }

          :deep(.arco-upload-list-item) {
            margin-top: 0 !important;
          }
        }
      }
    }

    .file-block {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      .image-item-block {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 12px;
        text-align: center;
        overflow: hidden;
        max-width: 160px;

        .image-space {
          height: auto;
          margin-bottom: 10px;
          position: relative;
          display: inline-block;
          overflow: hidden;
          .file-image {
            width: 80px;
            height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: var(--color-text-2);
            text-align: center;
            background: var(--color-fill-2);
            border: 1px dashed var(--color-neutral-3);
            border-radius: var(--border-radius-small);
          }
        }
      }
    }
  }

  img {
    width: 80px !important;
    &:hover {
      cursor: pointer;
      background: rgba(0, 0, 0, 0.5);
    }
  }
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;

    &:hover {
      opacity: 1;
      cursor: pointer;
    }
  }

  .add-content {
    margin-top: 5px;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }
</style>
