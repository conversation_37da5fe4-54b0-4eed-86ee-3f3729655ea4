const TOKEN_KEY = 'maToken';
const MERCHANT_KEY = 'merchant';

const isLogin = () => {
  return !!localStorage.getItem(TOKEN_KEY);
};

const getToken = () => {
  return localStorage.getItem(TOKEN_KEY);
};

const setToken = (token: string) => {
  localStorage.setItem(TOKEN_KEY, token);
};

const clearToken = () => {
  localStorage.removeItem(TOKEN_KEY);
};

const setMerchant = (merchantId: string) => {
  localStorage.setItem(MERCHANT_KEY, merchantId);
};

const getMerchant = () => {
  return localStorage.getItem(MERCHANT_KEY);
};
const clearMerchant = () => {
  localStorage.removeItem(MERCHANT_KEY);
};

export { isLogin, getToken, setToken, clearToken,setMerchant, getMerchant,clearMerchant };
