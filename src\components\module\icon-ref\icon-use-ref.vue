<script setup lang="ts">
  import { computed } from 'vue';

  // 父组件传参
  const props = defineProps({
    iconName: {
      type: String,
      default: '',
    },
  });

  const showIcon = computed(() => {
    return eval(props.iconName);
  });
</script>

<template>
  <div v-if="props.iconName" class="icon">
    <component :is="showIcon" />

    <!--    <IconCheckCircle v-if="props.iconName === 'MX_Radio'" />-->
    <!--    <IconCheckSquare v-if="props.iconName === 'MX_CheckBox'" />-->
    <!--    <IconFile v-if="props.iconName === 'IconFile'" />-->
    <!--    <IconFileImage v-if="props.iconName === 'IconFileImage'" />-->
    <!--    <IconFileVideo v-if="props.iconName === 'IconFileVideo'" />-->
    <!--    <IconFilePdf v-if="props.iconName === 'IconFilePdf'" />-->
    <!--    <IconLink v-if="props.iconName === 'MX_Upload'" />-->
    <!--    <IconUpload v-if="props.iconName === 'IconUpload'" />-->
    <!--    <IconDownload v-if="props.iconName === 'IconDownload'" />-->
    <!--    <IconCopy v-if="props.iconName === 'IconCopy'" />-->
    <!--    <IconList v-if="props.iconName === 'IconList'" />-->
    <!--    <IconExclamationCircle v-if="props.iconName === 'IconExclamationCircle'" />-->
  </div>
</template>

<style scoped lang="less"></style>
