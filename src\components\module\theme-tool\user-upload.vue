<script setup lang="ts">
  import IconUseRef from '@/components/module/icon-ref/icon-use-ref.vue';
  import useModuleStore from '@/components/module/store/module';
  import { ResponseGetModuleItemModel } from '@/api/module/type';
  import { computed, ref } from 'vue';

  const useModule = useModuleStore();

  const emits = defineEmits(['itemAdd']);

  /**
   * 添加模块
   * @param key {ResponseGetModuleItemModel} 模块属性
   */
  const addItem = (key: ResponseGetModuleItemModel) => {
    // 触发父组件事件
    emits('itemAdd', key);
  };

  const uploadModulesList = ref<ResponseGetModuleItemModel[]>([]);

  (async () => {
    uploadModulesList.value = await useModule.getTaskUploadList();
  })();

  const uploadModules = computed(() => uploadModulesList.value);
</script>

<template>
  <div class="general-tools">
    <div class="subtitle">用户任务上传</div>
    <div class="sub-frame">
      <a-button
        v-for="(item, index) in uploadModules"
        :key="index"
        size="small"
        @click="addItem(item)"
      >
        {{ item.title }}
        <template #icon>
          <IconUseRef :icon-name="item.icon" />
        </template>
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  .general-tools {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;

    .subtitle {
      color: #1d2129;
      /* 16/CN-Medium */
      font-family: 'PingFang SC', sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 150% */
    }

    .sub-frame {
      display: flex;
      align-items: flex-start;
      align-content: flex-start;
      gap: 6px;
      align-self: stretch;
      flex-wrap: wrap;
    }
  }
</style>
