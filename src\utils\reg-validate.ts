export const RegularExpression = {
    link : /^(https?:|mailto:|tel:)/,
    email : /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
    password : /^.{4,}$/,
    mobile : /^1[3456789]\d{9}$/

}

export const RegularValidate = {
    link : (path: string): boolean => {
        return RegularExpression.link.test(path)
    },
    email : (rule: any, value: any, callback: (e?: Error) => any) => {
        if (RegularExpression.email.test(value)) {
            callback()
        } else {
            callback(new Error('邮箱格式不正确'))
        }
    },
    password : (rule: any, value: any, callback: (e?: Error) => any) => {
        if (RegularExpression.password.test(value)) {
            callback()
        }else{
            callback(new Error('密码不能小于4位数'))
        }
    },
    mobile : (rule: any, value: any, callback: (e?: Error) => any) => {
        if (RegularExpression.mobile.test(value)) {
            callback()
        } else {
            callback(new Error('手机号格式不正确'))
        }
    }
}