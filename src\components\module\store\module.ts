import { defineStore } from 'pinia';
import { ResponseGetModuleItemModel } from '@/api/module/type';
import { ValidateUtils } from '@/utils/validate';
import { StoreModuleStateModel } from '@/components/module/store/type';
import {
  ComponentTemplateData,
  ModuleTemplateData,
} from '@/components/module/store/template';

const inherentModuleTitle = ['CN', '联系电话', 'QQ'];
const baseModuleTitle = ['性别', '实名信息', '出生日期', '邮箱'];
const taskInfoModuleTitle = ['报名规则', '复制文案', '官方图片'];
const taskUploadModuleTitle = [
  '任务截图上传',
  '正片上传',
  '视频上传',
  '链接上传',
];
const commonModuleTitle = ['单选', '多选', '文本', '文件上传', '日期选择'];

const useModuleStore = defineStore('module', {
  state: (): StoreModuleStateModel => ({
    modules: [] as ResponseGetModuleItemModel[],
    existModulesMap: new Map<string, ResponseGetModuleItemModel>(), // key：ResponseGetModuleItemModel.title, value:ResponseGetModuleItemModel
    metaModulesMap: new Map<string, ResponseGetModuleItemModel>(), // key：ResponseGetModuleItemModel.title, value:ResponseGetModuleItemModel
  }),

  getters: {
    moduleList(): ResponseGetModuleItemModel[] {
      return this.modules;
    },

    existModuleList(): Map<string, ResponseGetModuleItemModel> {
      return this.existModulesMap;
    },
    metaModuleList(): Map<string, ResponseGetModuleItemModel> {
      return this.metaModulesMap;
    },
  },

  actions: {
    setInfo(partial: Partial<ResponseGetModuleItemModel[]>) {
      this.$patch({ modules: partial });

      // 统一清空 map
      this.existModulesMap.clear();
      this.metaModulesMap.clear();

      if (this.modules.length === 0) {
        return;
      }

      this.modules.forEach((module) => {
        if (!module || module.title == null) return;
        const hasKey = ValidateUtils.isNotEmpty(module.key);
        const title = String(module.title); // 确保 key 为字符串

        if (hasKey) {
          this.existModulesMap.set(title, module);
        } else {
          this.metaModulesMap.set(title, module);
        }
      });
    },

    /**
     * 重置信息
     *
     * 该方法用于重置组件或对象的属性到其初始状态
     * 它没有参数，也不返回任何值
     * 使用了框架提供的 `$reset` 方法来实现重置功能
     */
    resetInfo() {
      this.$reset();
    },

    /**
     * 获取模组信息
     */
    async getModuleList() {
      if (ValidateUtils.isNullOrEmpty(this.modules)) {
        const localModule = localStorage.getItem('module');
        if (ValidateUtils.isNullOrEmpty(localModule)) {
          // 从api获取数据
          // const res = await ModuleApi.getModules();
          // const arr = res.data.map((item) =>
          //   JSON.parse(item!)
          // ) as unknown as AttributeModuleGroup[];
          // this.setInfo(arr);
          // localStorage.setItem('module', JSON.stringify(res.data));

          // 从temp数据库拿到数据
          localStorage.setItem('module', JSON.stringify(ModuleTemplateData));
          this.setInfo(ModuleTemplateData);
        } else {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          this.setInfo(JSON.parse(localModule!));
        }
      }
    },

    /**
     * 获取默认模组信息 -- 不显示可点击
     */
    async getInherentModuleList(): Promise<ResponseGetModuleItemModel[]> {
      const modules: ResponseGetModuleItemModel[] = [];

      await this.getModuleList().then(() => {
        inherentModuleTitle.forEach((str) => {
          const module = this.existModuleList.get(str.toLowerCase());
          if (module !== undefined) {
            modules.push(module);
          }
        });
      });

      return modules;
    },

    /**
     * 获取 个人信息部分 基础模组信息 -- 不可修改属性及内容，必选属性除外
     */
    async getBaseModuleList(): Promise<Array<ResponseGetModuleItemModel>> {
      const modules: ResponseGetModuleItemModel[] = [];
      await this.getModuleList().then(() => {
        baseModuleTitle.forEach((str) => {
          const module = this.existModuleList.get(str.toLowerCase());
          if (module !== undefined) {
            modules.push(module);
          }
        });
      });
      return modules;
    },
    /**
     * 获取任务说明部分 基础模组信息模组信息 -- 必选属性除外
     */
    async getTaskInfoModuleList(): Promise<Array<ResponseGetModuleItemModel>> {
      const modules: ResponseGetModuleItemModel[] = [];
      await this.getModuleList().then(() => {
        taskInfoModuleTitle.forEach((str) => {
          const module = this.existModuleList.get(str);
          if (module !== undefined) {
            modules.push(module);
          }
        });
      });
      return modules;
    },

    /**
     * 获取用户任务上传部分 基础模组信息模组信息 -- 必选属性除外
     */
    async getTaskUploadList(): Promise<Array<ResponseGetModuleItemModel>> {
      const modules: ResponseGetModuleItemModel[] = [];
      await this.getModuleList().then(() => {
        taskUploadModuleTitle.forEach((str) => {
          const module = this.existModuleList.get(str);
          if (module !== undefined) {
            modules.push(module);
          }
        });
      });
      return modules;
    },

    /**
     * 获取所有元组件模组信息 -- 可修改全部属性
     */
    async getCommonModuleList(): Promise<Array<ResponseGetModuleItemModel>> {
      const modules: ResponseGetModuleItemModel[] = [];
      await this.getModuleList().then(() => {
        commonModuleTitle.forEach((str) => {
          const module = this.metaModuleList.get(str);
          if (module !== undefined) {
            modules.push(module);
          }
        });
      });
      return modules;
    },

    /**
     * 获取所有已封装属性
     */
    async getExistModuleList(): Promise<Array<ResponseGetModuleItemModel>> {
      const modules: ResponseGetModuleItemModel[] = [];
      await this.getModuleList().then(() => {
        modules.push(...this.existModuleList.values());
      });
      return modules;
    },

    async getModule(
      title: string
    ): Promise<ResponseGetModuleItemModel | undefined> {
      let module: ResponseGetModuleItemModel | undefined;
      await this.getModuleList().then(() => {
        module = this.modules.find((item) => item.title === title);
      });

      return module;
    },
  },
});

export default useModuleStore;
