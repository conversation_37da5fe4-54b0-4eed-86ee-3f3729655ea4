/**
 * 模版角色 枚举
 */
// eslint-disable-next-line no-shadow
export enum EnumTemplateRole {
  /**
   * Coser
   */
  Coser = 1,
  /**
   * 摄影
   */
  Photographer = 2,
  /**
   * 汉服
   */
  Hanfu = 3,
  /**
   * 萝莉塔
   */
  Lolita = 4,
  /**
   * 特摄
   */
  Tokusatsu = 5,
  /**
   * 兽装
   */
  Fursuit = 6,
}

/**
 * 模版角色 枚举转换
 */
export const EnumTemplateRoleTranslated = {
  /**
   * enum转成中文
   */
  TranslatedToCN: (value: EnumTemplateRole): string => {
    switch (value) {
      case EnumTemplateRole.Coser:
        return 'Coser';
      case EnumTemplateRole.Photographer:
        return '摄影';
      case EnumTemplateRole.Hanfu:
        return '汉服';
      case EnumTemplateRole.Lolita:
        return '萝莉塔';
      case EnumTemplateRole.Tokusatsu:
        return '特摄';
      case EnumTemplateRole.Fursuit:
        return '兽装';
      default:
        return '';
    }
  },
  /**
   * 中文转成enum
   */
  TranslatedToEnum: (value: string): EnumTemplateRole | undefined => {
    switch (value) {
      case 'Coser':
        return EnumTemplateRole.Coser;
      case '摄影':
        return EnumTemplateRole.Photographer;
      case '汉服':
        return EnumTemplateRole.Hanfu;
      case '萝莉塔':
        return EnumTemplateRole.Lolita;
      case '特摄':
        return EnumTemplateRole.Tokusatsu;
      case '兽装':
        return EnumTemplateRole.Fursuit;
      default:
        return undefined;
    }
  },
};
