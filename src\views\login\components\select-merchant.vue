<template>
  <div class="merchant_panel">
    <div class="back" @click="handleGoToBack">
      <div class="back-icon">
        <icon-arrow-left :size="13" />
      </div>
      <div class="back-text">返回</div>
    </div>
    <div class="title">请选择商户</div>
    <div class="sub-title"
      >+86
      {{ ValidateUtils.maskString(props.mobile, 3, 4) }} 已绑定以下商户</div
    >
    <div class="search">
      <a-input
        v-model="searchText"
        placeholder="搜索商户"
        clearable
        @input="handleSearch"
        @change="handleSearch"
      >
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
    </div>
    <a-scrollbar height="360px">
      <div class="merchant-list">
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="merchant-list-item"
          :class="
            item.merchantStatus === 2
              ? selectedIndex == index
                ? 'merchant-selected'
                : ''
              : 'disabled'
          "
          @click="selectItem(index)"
        >
          <div class="merchant-info">
            <div class="merchant-icon">
              <img src="@/assets/icons/company.png" alt="商户图标" />
            </div>
            <div class="merchant-name">{{ item.principalName }}</div>
          </div>
        </div>
      </div>
    </a-scrollbar>
    <div class="btn">
      <a-button long type="primary" @click="handleGoToDashboard">确定</a-button>
    </div>
  </div>
  <div class="tips"
    >以上商户并不是我的商户，<span
      class="link"
      @click="handleGoToCreateMerchant"
      >创建自己的商户</span
    ></div
  >
</template>

<script setup lang="ts">
  import { onMounted, ref, reactive } from 'vue';
  import { MerchantAPI } from '@/api/merchant';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store';
  import { LoginAPI } from '@/api/auth';
  import { ValidateUtils } from '@/utils/validate';
  import { Message } from '@arco-design/web-vue';
  import { setMerchant } from '@/utils/auth';
  import {
    EnumMerchantListItemStatus,
    ResponseMerchantListItemModel,
  } from '@/types/api-type/merchant';

  const router = useRouter();
  const userStore = useUserStore();

  const emit = defineEmits(['back']);
  const props = defineProps({
    mobile: {
      type: String,
      default: '',
      required: true,
    },
  });

  const dataList = ref<ResponseMerchantListItemModel[]>([]);
  const dataListCopy = reactive<ResponseMerchantListItemModel[]>([]);

  // 商户列表选择角标
  const selectedIndex = ref<number | undefined>(undefined);

  /**
   * 跳转-上一步
   */
  const handleGoToBack = () => {
    emit('back', true);
  };
  /**
   * 跳转-创建商户
   */
  const handleGoToCreateMerchant = () => {
    router.push({ path: '/merchantInfo' });
  };
  /**
   * 跳转-面板
   */
  const handleGoToDashboard = () => {
    if (ValidateUtils.isNotEmpty(selectedIndex.value)) {
      const selectMerchantItem: ResponseMerchantListItemModel =
        dataList.value[selectedIndex.value!];
      // 如果未认证
      if (
        selectMerchantItem.merchantStatus !==
        EnumMerchantListItemStatus.AUTHENTICATED
      ) {
        Message.error('您选择的商户无法进行下一步操作，请确认商户状态后继续。');
      } else {
        userStore.user.merchantId =
          dataList.value[selectedIndex.value!].merchantId;
        if (userStore.user.merchantId) {
          LoginAPI.SelectMerchantId(userStore.user.merchantId).then(() => {
            // 将merchantID设置到localStorage
            setMerchant(userStore.user.merchantId);
            router.push({ path: '/dashboard/workplace' });
          });
        }
      }
    } else {
      Message.error('未选择商户');
    }
  };

  const selectItem = (index: number) => {
    // if (dataList.value[index].merchantStatus == 1 || dataList.value[index].merchantStatus == 3) {
    // 	selectedIndex.value = undefined
    // 	return
    // }

    switch (dataList.value[index].merchantStatus) {
      case EnumMerchantListItemStatus.IN_AUTHENTICATION:
        Message.error('选择的商户正在认证中，请稍后...');
        break;
      case EnumMerchantListItemStatus.AUTHENTICATED:
        break;
      case EnumMerchantListItemStatus.AUTHENTICATION_REJECTED:
        Message.error('选择的商户已被驳回，请和对接人联系修改认证信息');
        break;
      case EnumMerchantListItemStatus.BANNED:
        Message.error('选择的商户已被封禁，请选择其他商户或联系对接人');
        break;
      default:
        Message.error('未知');
        break;
    }

    selectedIndex.value = index;
  };

  // 搜索
  const searchText = ref(null);
  const handleSearch = (sText: string) => {
    if (!sText) {
      dataList.value = dataListCopy;
    } else {
      dataList.value = dataListCopy.filter((item) =>
        item.principalName.includes(sText)
      );
    }
  };

  onMounted(() => {
    MerchantAPI.getMerchantList().then((res) => {
      Object.assign(dataList.value, res.data);
      Object.assign(dataListCopy, res.data);
      if (dataList.value && dataList.value.length > 0) {
        for (let i = 0; i < dataList.value.length; i += 1) {
          if (dataList.value[i].merchantStatus === 2) {
            selectedIndex.value = i;
            return;
          }
        }
      }
    });
  });
</script>

<style scoped lang="less">
  .back {
    width: 50px;
    cursor: pointer;
    display: flex;
    color: #999;
    position: relative;
    top: -20px;
    left: -20px;

    .back-icon {
      padding-top: 3px;
      padding-right: 7px;
    }
  }

  .title {
    color: #333;
    font-size: 20px;
    font-weight: 800;
  }

  .sub-title {
    color: #999;
    margin: 5px 0;
  }

  .merchant-list {
    .disabled {
      background-color: #eee;
    }

    .merchant-list-item {
      height: 40px;
      margin: 10px 0;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      line-height: 40px;
      display: flex;
      justify-content: space-between;

      .merchant-info {
        display: flex;

        .merchant-icon {
          padding: 5px 6px 0 6px;

          img {
            width: 25px;
          }
        }

        .merchant-name {
          color: #333;
        }
      }

      .merchant-status {
        color: #999;
        margin-right: 10px;
      }
    }
  }

  .btn {
    margin: 10px 0;

    .a-button {
      width: 100%;
      height: 40px;
    }
  }

  .tips {
    text-align: center;
    color: #999;

    .link {
      color: #409eff;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .merchant-selected {
    border: 1px solid #409eff !important;
  }

  .login-form .search .a-input {
    height: 32px !important;
    font-size: 14px !important;
  }

  .merchant-list-item .merchant-info .merchant-check {
    padding-top: 5px;
  }

  :deep(.arco-scrollbar-container) {
    min-height: 360px;
  }
</style>
