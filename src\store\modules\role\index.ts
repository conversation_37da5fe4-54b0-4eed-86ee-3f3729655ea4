import { defineStore } from 'pinia';
import { RoleApi } from '@/api/role/api';

const templateRoleList = {
  data: ['COS', '摄影'],
};

const useRoleStore = defineStore('role', {
  state: () => ({
    list: [] as string[],
  }),

  getters: {
    roleList(): string[] {
      return this.list;
    },
  },

  actions: {
    setList(partial: Partial<string[]>) {
      this.$patch({ list: partial });
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    async getListFromAPI() {
      const roleStorage = localStorage.getItem('role');
      if (!roleStorage) {
        // const res = await RoleApi.getRoleList();
        // this.setList(res.data);
        // localStorage.setItem('role', JSON.stringify(this.roleList));

        this.setList(templateRoleList.data);
      }
    },
  },
});

export default useRoleStore;
