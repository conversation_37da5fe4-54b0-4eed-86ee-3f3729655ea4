<!-- 控件-组空间 -->
<script setup lang="ts">
  import { computed, defineProps, PropType, ref } from 'vue';
  import { CustomGroupModule } from '@/components/module/entity/struct/group';
  import { ValidateUtils } from '@/utils/validate';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
  import useComponentStore from '@/components/module/store/component';
  import useModuleStore from '@/components/module/store/module';

  const emits = defineEmits(['groupDelete']);

  // 父组件传参
  const props = defineProps({
    groupEntity: {
      type: Object as PropType<CustomGroupModule>,
      required: true,
    },

    // 是否是当前选中的组件
    currentFlag: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  /**
   * 组内必填标识
   */
  const groupRequiredFlag = computed(() => {
    return props.groupEntity?.getGroupRequired();
  });

  /**
   * 组内标题
   */
  const groupTitle = computed({
    get: () => props.groupEntity?.getGroupTitle(),
    set: (value) => props.groupEntity?.setGroupTitle(value),
  });

  const localTitle = ref<string | undefined>(
    ValidateUtils.isNotEmpty(groupTitle.value) ? groupTitle.value : undefined
  );

  /**
   * 组内说明
   *  -- 如果没有值则返回undefined
   */
  const groupDesc = computed(() => {
    // const desc = props.groupEntity?.getGroupDesc();
    // return ValidateUtils.isNullOrEmpty(desc) ? undefined : desc;
    return props.groupEntity?.getGroupDesc();
  });

  /**
   * 组内模块
   */
  const groupModules = computed(() => {
    return props.groupEntity?.getGroupModules();
  });

  const groupComponentDefaultFLag = computed(() => {
    return useModuleStore().isModuleInherent(props.groupEntity?.getGroupKey());
  });

  /**
   * 组模块是否是已封装模块
   */
  const groupComponentExistFlag = computed(() => {
    return props.groupEntity?.getExistFlag();
  });

  const removeGroup = () => {
    emits('groupDelete');
  };
</script>

<template>
  <div v-if="props.groupEntity" style="width: 100%; position: relative">
    <div direction="vertical" class="group-space">
      <!-- 组内标题 -->
      <div class="group-space-title" :class="{ required: groupRequiredFlag }">
        <a-input
          v-model="localTitle"
          placeholder="请输入组件标题"
          :disabled="groupComponentExistFlag"
          :max-length="
            ActivitySettingLimitValue.activityModuleInputPlaceholderMaxLength
          "
        />
        <div v-if="!groupComponentDefaultFLag" class="control-block">
          <icon-close size="18" style="color: #86909c" @click="removeGroup" />
        </div>
      </div>
      <!-- 组内标题说明 -->
      <div v-if="groupDesc != null || !groupComponentExistFlag" class="desc">
        <a-textarea
          v-model="groupDesc"
          placeholder="请输入该任务说明(选填)"
          auto-size
          :max-length="
            ActivitySettingLimitValue.activityModuleInputPlaceholderMaxLength
          "
        />
      </div>

      <!-- 组内组件 -->
      <div v-if="groupModules" class="group-component">
        <div
          v-for="(module, index) in groupModules"
          :key="index"
          class="group-component-item"
        >
          <div v-if="module && module.getShowModuleRef()" class="item-space">
            <component
              :is="module.getShowModuleRef()"
              :item="module"
              :enabled="!groupComponentExistFlag"
              class="component-item"
            />
          </div>
          <a-empty v-else>未注册组件!!</a-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .edit_block {
    position: absolute;
    margin-top: -5px;
    margin-right: -10px;
    right: 0;
    cursor: pointer;
    z-index: 9;
    opacity: 0.2;
    &:hover {
      color: #0a5fe4;
      opacity: 1;
    }
  }
  .edit_comp {
    opacity: 1;
  }

  .group-space {
    //padding: 10px;
    width: 100%;

    &:hover {
      .group-space-title {
        .control-block {
          opacity: 1;
        }
      }
    }

    .group-space-title {
      display: flex;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      color: #4e5969;
      font-family: 'PingFang SC', serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .required {
      position: relative;
      padding-left: 15px;
      &:before {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .desc {
      color: #86909c;
      font-family: 'PingFang SC', serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 4px;

      .edit_icon {
        color: #4e5969;
        font-size: 14px;
        line-height: 22px;
      }
    }

    .group-component {
      display: flex;
      flex-direction: column;
      width: 100%;
      grid-gap: 12px;
      cursor: auto !important;
      .component-item {
        width: 100%;
      }
    }
  }

  .edit_icon {
    cursor: pointer;
  }

  .group-space-title {
    :deep(.arco-input-wrapper) {
      background-color: transparent !important;
      .arco-input {
        color: #4e5969 !important;
        font-size: 14px !important;
        line-height: 22px !important;
        font-family: 'PingFang SC', Arial, sans-serif !important;
        padding: 0 !important;
      }
    }
    .control-block {
      opacity: 0;
      cursor: pointer;
    }
  }
  .desc {
    :deep(.arco-textarea-wrapper) {
      background-color: transparent !important;
      .arco-textarea-mirror {
        padding: 0 !important;
      }
      .arco-textarea {
        color: #86909c !important;
        font-size: 12px !important;
        line-height: 20px !important;
        font-family: 'PingFang SC', Arial, sans-serif !important;
        padding: 0 !important;
        min-height: 20px !important;
      }
    }
  }
</style>
