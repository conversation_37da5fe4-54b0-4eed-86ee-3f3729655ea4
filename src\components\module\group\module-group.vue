<!-- 控件-组空间 -->
<script setup lang="ts">
  import { computed, defineProps, PropType } from 'vue';
  import { CustomGroupModule } from '@/components/module/entity/struct/group';
  import { ValidateUtils } from '@/utils/validate';

  // 父组件传参
  const props = defineProps({
    groupEntity: {
      type: Object as PropType<CustomGroupModule>,
      required: true,
    },

    // 是否是当前选中的组件
    currentFlag: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  /**
   * 组内必填标识
   */
  const groupRequiredFlag = computed(() => {
    return props.groupEntity?.getGroupRequired();
  });

  /**
   * 组内标题
   */
  const groupTitle = computed(() => {
    return props.groupEntity?.getGroupTitle();
  });

  /**
   * 组内说明
   *  -- 如果没有值则返回undefined
   */
  const groupDesc = computed(() => {
    const desc = props.groupEntity?.getGroupDesc();
    return ValidateUtils.isNullOrEmpty(desc) ? undefined : desc;
  });

  /**
   * 组内模块
   */
  const groupModules = computed(() => {
    return props.groupEntity?.getGroupModules();
  });

  /**
   * 组内模块当前可修改
   *  -- 组模块当前被选中
   *  -- 组模块为非已封装模块
   *  -- 组模块为非只读模块
   */
  const groupModulesCurrentCanEdit = computed(() => {
    return !props.groupEntity?.getExistFlag();
  });
</script>

<template>
  <div v-if="props.groupEntity" style="width: 100%; position: relative;">
    <div class="group-space">
      <!-- 组内标题 -->
      <div class="group-space-title" :class="{ required: groupRequiredFlag }">
        {{ groupTitle }}
      </div>
      <!-- 组内标题说明 -->
      <div v-if="groupDesc" class="desc">{{ groupDesc }}</div>

      <!-- 组内组件 -->
      <div v-if="groupModules" class="group-component">
        <div
          v-for="(module, index) in groupModules"
          :key="index"
          class="group-component-item"
        >
          <div v-if="module && module.getShowModuleRef()" class="item-space">
            <component
              :is="module.getShowModuleRef()"
              :item="module"
              :enabled="groupModulesCurrentCanEdit"
              class="component-item"
            />
          </div>
          <a-empty v-else>未注册组件!!</a-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .edit_block {
    position: absolute;
    margin-top: -5px;
    margin-right: -10px;
    right: 0;
    cursor: pointer;
    z-index: 9;
    opacity: 0.2;
    &:hover {
      color: #0a5fe4;
      opacity: 1;
    }
  }
  .edit_comp {
    opacity: 1;
  }

  .group-space {
    //padding: 10px;
    width: 100%;

    .group-space-title {
      display: flex;
      margin-bottom: 8px;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      color: #4e5969;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .required {
      position: relative;
      padding-left: 15px;
      &:before {
        content: '*';
        color: red;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .desc {
      color: #86909c;
      font-family: 'PingFang SC', serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 4px;

      .edit_icon {
        color: #4e5969;
        font-size: 14px;
        line-height: 22px;
      }
    }

    .group-component {
      display: flex;
      flex-direction: column;
      width: 100%;
      grid-gap: 12px;
      cursor: auto !important;
      .component-item {
        width: 100%;
      }
    }
  }

  .edit_icon {
    cursor: pointer;
  }
</style>
