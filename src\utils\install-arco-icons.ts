import * as icons from '@arco-design/web-vue/es/icon';
import type { App, Component } from 'vue';

export default {
  install(app: App) {
    // 遍历所有图标组件并注册为全局组件
    Object.keys(icons).forEach((key) => {
      const iconComponent = icons[key as keyof typeof icons] as Component;
      if (iconComponent && iconComponent.name) {
        app.component(iconComponent.name, iconComponent);
      }
    });
  },
};
