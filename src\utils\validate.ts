import {
  KVLModel,
  LVModel,
} from '@/components/module/type/attribute-model-unified';
import { ConvertUtils } from '@/utils/convert';

export class ValidateUtils {
  /**
   * 检测传参是否为非空（包含 null, undefined, 空字符串, 空数组, 空白字符串等）
   * @param value - 要判断的值
   * @returns 是否非空
   */
  public static isNotEmpty(value: any): boolean {
    if (value === null || value === undefined) {
      return false;
    }

    // 处理字符串：排除空白字符组成的字符串
    if (typeof value === 'string') {
      return value.trim().length > 0;
    }

    // 处理数组
    if (Array.isArray(value)) {
      return value.length > 0;
    }

    // 可选：处理 Set / Map
    if (value instanceof Set || value instanceof Map) {
      return value.size > 0;
    }

    // 其他类型（对象、数字、布尔值等）视为非空
    return true;
  }

  /**
   * 判断对象是否为空
   * @param val
   */
  public static isNullOrEmpty(val: any): boolean {
    return !ValidateUtils.isNotEmpty(val);
  }

  /**
   * 判断对象是否为正数
   * @param value
   */
  public static isValuePositiveNumber(value: number | undefined): boolean {
    return typeof value === 'number' && value > 0;
  }

  /**
   * 获取文件尾缀
   * @param fileName
   */
  public static getFileExtension(fileName: any) {
    if (typeof fileName !== 'string') {
      return '';
    }

    const lastPart = fileName.split('.').pop();
    return lastPart?.toLowerCase() ?? '';
  }

  /**
   * 掩码字符串
   * @param str {string} - 要掩码的字符串
   * @param prefixLength {number} - 前缀需要显示的长度
   * @param suffixLength {number} - 后缀需要显示的长度
   */
  public static maskString(
    str: string,
    prefixLength: number,
    suffixLength: number
  ): string {
    // 如果字符串长度小于等于要显示的前后总长度，则直接返回原字符串
    if (str.length <= prefixLength + suffixLength) return str;
    return `${str.slice(0, prefixLength)}****${str.slice(-suffixLength)}`;
  }

  /**
   * 判断传入的值是否是一个对象（排除 null）
   * 因为 JavaScript 中的 typeof null === 'object' 是一个历史遗留问题，所以需要判断非空
   *
   * @param value 要判断的对象
   * @returns 如果是对象且不为 null，返回 true；否则返回 false
   */
  static isObject(value: unknown): value is object {
    return typeof value === 'object' && value !== null;
  }

  /**
   * 判断传入的值是否是一个字符串
   * @param value
   */
  static isString(value: unknown): value is string {
    return typeof value === 'string';
  }

  // 判断是否为数字（排除 NaN）
  static isNumber(value: unknown): value is number {
    return typeof value === 'number' && !Number.isNaN(value);
  }

  // 判断是否为函数
  static isFunction(value: unknown): value is (...args: any[]) => any {
    return typeof value === 'function';
  }

  // 判断是否为数组
  static isArray(value: unknown): value is any[] {
    return Array.isArray(value);
  }

  // 判断是否为布尔值
  static isBoolean(value: unknown): value is boolean {
    return typeof value === 'boolean';
  }

  // 判断是否为 undefined
  static isUndefined(value: unknown): value is undefined {
    return typeof value === 'undefined';
  }

  // 判断是否为 null
  public static isNull(value: unknown): value is null {
    return value === null;
  }

  // 判断是否为 null 或 undefined
  static isNullOrUndefined(value: unknown): value is null | undefined {
    return value == null;
  }

  static isVLModel(item: unknown): item is LVModel {
    return (
      typeof item === 'object' &&
      item !== null &&
      'label' in item &&
      typeof item.label === 'string' &&
      'value' in item &&
      typeof item.value === 'string'
    );
  }

  static isKVLModel(item: unknown): item is KVLModel {
    return (
      this.isVLModel(item) && 'key' in item && typeof item.key === 'string'
    );
  }

  /**
   * 过滤并映射数组中的元素
   *
   * 支持：
   * - 多个类型守卫（validator）—— 每个都必须通过
   * - 可选的状态校验（validatorStatus）
   * - 映射函数（mapper），如果不传则返回原始数据
   *
   * @param value 要处理的原始数组或任意类型
   * @param validators 类型守卫函数列表（可传多个），用于判断元素是否为目标类型 T
   * @param validatorStatuses 状态校验函数（可传多个），用于进一步筛选目标类型中的有效项（可选）
   * @param mapper 映射函数，将符合条件的项转换为 R 类型, 如果不传则强转 T 的数据 为 R
   * @returns 返回由转换后的元素组成的数组，若输入不是数组则返回空数组
   * @example filterAndMapArray<T>(value) 返回 T []
   * @example filterAndMapArray<T, R>(value, (item) => new R(item), (item) => item.status === 'success') 筛选出状态为 success 的项并返回 T []
   * @example filterAndMapArray<T, R>(value, [(item)=>new T(item)], undefined, (item) => new R(item)) 返回 R []* @example filterAndMapArray<T>(value, void 0) 返回 undefined
   * @example filterAndMapArray<User>(value, value, [(item)=>new T(item)], (item) => item.status === 'success', (item) => new R(item)) 筛选出状态为 success 的项并返回 R []
   */
  public static validateAndMapArray<T, R = T>(
    value: unknown,
    validators: ((item: unknown) => item is T)[],
    validatorStatuses?: ((item: T) => boolean)[],
    mapper?: (item: T) => R
  ): R[] {
    if (!Array.isArray(value)) {
      return [];
    }

    // 校验器为空时，默认返回 true
    const isValidStatus = (item: T): boolean => {
      if (!validatorStatuses || validatorStatuses.length === 0) {
        return true;
      }
      return validatorStatuses.every((validator) => validator(item));
    };

    // 默认映射函数，直接返回原对象
    const defaultMapper = (item: T): R => item as unknown as R;

    return value.reduce((acc: R[], item: unknown) => {
      // 所有守卫必须通过
      const passesGuards = validators.every((validator) => validator(item));

      if (passesGuards && isValidStatus(item as T)) {
        acc.push(mapper ? mapper(item as T) : defaultMapper(item as T));
      }

      return acc;
    }, []);
  }

  /**
   * 验证并映射对象
   *
   * @param value - 要验证和映射的值（unknown 类型）
   * @param validators - 类型守卫函数数组，用于判断 value 是否为类型 T
   * @param validatorStatuses - 状态校验函数数组（可选），进一步筛选有效项
   * @param mapper - 映射函数（可选），将符合条件的 T 转换为 R
   * @returns 如果所有验证通过，返回映射后的 R 类型；否则返回 undefined
   */
  public static validateAndMapToObject<T, R = T>(
    value: unknown,
    validators: ((item: unknown) => item is T)[],
    validatorStatuses?: ((item: T) => boolean)[],
    mapper?: (item: T) => R
  ): R | undefined {
    if (!value) {
      return undefined;
    }

    // 所有守卫必须通过
    const passesGuards = validators.every((validator) => validator(value));
    if (!passesGuards) return undefined;

    const valueAsT = value as T;

    // 校验状态（如果存在）
    if (validatorStatuses && validatorStatuses.length > 0) {
      const passesStatus = validatorStatuses.every((validator) =>
        validator(valueAsT)
      );
      if (!passesStatus) return undefined;
    }

    // 映射或返回原对象
    if (mapper) {
      return mapper(valueAsT);
    }
    return valueAsT as unknown as R;
  }

  /**
   * 验证某个值是否可以被解析为 JSON 数组，并满足所有提供的验证规则。
   *
   * @param value - 要验证的值（通常是字符串）
   * @param validators - 可选的验证器数组，用于验证数组中每一项是否合法
   * @returns 如果能解析成数组并且所有项都通过验证，返回 true；否则返回 false
   */
  public static verifyArrayJson<T>(
    value: unknown,
    ...validators: ((item: T) => boolean)[]
  ): boolean {
    let parsed: T[];

    // 如果已经是数组，直接使用
    if (Array.isArray(value)) {
      parsed = value;
    } else {
      parsed = ConvertUtils.convertJson2Array<T>(value);
    }

    if (parsed.length === 0) {
      return false;
    }

    // 如果没有校验规则，只保证是数组即可
    if (validators.length === 0) {
      return true;
    }

    // 每个元素必须通过至少一个验证器
    return parsed.every((item) =>
      validators.every((validator) => validator(item))
    );
  }

  private static generateDefaultArray<T>(
    length: number,
    defaultValue: () => T
  ): T[] {
    if (length <= 0) return [];
    return Array.from({ length }, defaultValue);
  }

  /**
   * 调整列表的尾部，使其满足指定长度。如果列表的长度小于指定长度，则添加默认值。
   * 如果列表的长度大于指定长度，则删除多余的元素。
   *
   * @param value - 输入值，可能是 JSON 字符串或其它格式
   * @param finalCount - 数组最终的长度
   * @param defaultValue - 填充数组项时的默认值生成函数
   * @param validators - 类型守卫函数数组，用于校验每一项是否符合 T 类型（可选）
   * @returns 返回调整后的 T[] 数组
   */
  public static adjustListTail<T>(
    value: unknown,
    finalCount: number,
    defaultValue: () => T,
    validators?: ((item: unknown) => item is T)[]
  ): T[] {
    let parsedData: T[];

    // 参数合法性检查：如果不是字符串直接返回空数组
    if (typeof value !== 'string') {
      return this.generateDefaultArray(finalCount, defaultValue);
    }

    try {
      const trimmed = value.trim();
      parsedData = trimmed.length ? JSON.parse(trimmed) : [];
    } catch (error) {
      console.error('JSON 解析失败:', error);
      return this.generateDefaultArray(finalCount, defaultValue);
    }

    // 确保解析结果是数组
    if (!Array.isArray(parsedData)) {
      return this.generateDefaultArray(finalCount, defaultValue);
    }

    // 校验每一项是否符合类型守卫条件
    if (validators && validators.length > 0) {
      parsedData = parsedData.filter((item): item is T => {
        return validators.every((validator) => validator(item));
      });
    }

    // 防止负数长度
    if (finalCount < 0) {
      finalCount = 0;
    }

    const dev = finalCount - parsedData.length;

    if (dev > 0) {
      // 补齐缺失项
      parsedData = parsedData.concat(
        this.generateDefaultArray(dev, defaultValue)
      );
    } else if (dev < 0) {
      // 截断多余项
      parsedData = parsedData.slice(0, finalCount);
    }

    return parsedData;
  }
}
