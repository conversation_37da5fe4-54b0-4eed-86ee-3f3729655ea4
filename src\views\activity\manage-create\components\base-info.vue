<template>
  <div class="base-info">


    <a-form
      ref="formRef"
      :model="formData"
      class="form"
      label-align="left"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <a-form-item
        field="goodsId"
        label="选择招募的漫展"
      >
        <a-select
          v-model="formData.goodsId"
          placeholder="请选择你需要开启招募的漫展"
        />
      </a-form-item>
      <a-form-item
        field="activityName"
        label="活动名称"
      >
        <a-input
          v-model="formData.activityName"
          placeholder="如：自由行招募/舞台招募/游场团招募"
          max-length="20"
          show-word-limit
        >
        </a-input>
      </a-form-item>
      <a-form-item
        field="mainImage"
        label="主图"
      >
        <a-upload
          action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
          :fileList="formData.mainImage ? [formData.mainImage] : []"
          :show-file-list="false"
          list-type="picture-card"
          limit="1"
          style="width: 100px;height: 75px"
        >
        </a-upload>
      </a-form-item>
      <a-form-item
        field="showImage"
        label="展示图"
      >
        <a-upload
          action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
          :fileList="formData.showImage ? [formData.mainImage] : []"
          :show-file-list="false"
          list-type="picture-card"
          limit="1"
          style="width: 75px;height: 100px"
        >
        </a-upload>
      </a-form-item>
      <a-form-item field="promotionTime" label="待招募日期">
        <a-range-picker v-model="formData.recruitmentDate" />
      </a-form-item>
      <a-form-item field="registrationDate" label="活动报名日期">
        <a-range-picker v-model="formData.registrationDate" show-time />
      </a-form-item>
      <a-form-item
        field="publicationDate"
        label="报名公布日期"
      >
        <a-date-picker v-model="formData.publicationDate" style="width: 380px" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="onNextClick">
          下一步
        </a-button>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { BaseInfoModel } from '@/api/form';
  import { FormInstance } from '@arco-design/web-vue';

  const emits = defineEmits(['changeStep']);
  const formRef = ref<FormInstance>();
  const formData = ref<BaseInfoModel>({
    goodsId: '',
    activityName: '',
    recruitmentDate: [],
    registrationDate: [],
    publicationDate: '',
    mainImage: '',
    showImage: ''
  });


  const file = ref();


  const onNextClick = async () => {
    const res = await formRef.value?.validate();
    if (!res) {
      emits('changeStep', 'forward', { ...formData.value });
    }
  };
</script>

<style scoped lang="less">

</style>
