<template>
  <div class="tw-flex tw-flex-col tw-gap-4 tw-w-full">
    <!-- 提交记录信息 -->
    <div class="submission-record">
      <div class="submission-title">提交记录</div>
      <div class="submission-platform">任务平台：小红书</div>
      <div class="submission-date">提交日期：{{ submissionDate }}</div>
      <div class="submission-link-wrapper">
        <span class="submission-link-label">链接：</span>
        <a :href="submissionLink" target="_blank" class="submission-link">
          {{ submissionLink }}
        </a>
      </div>
    </div>

    <!-- 审核按钮区域 -->
    <div class="audit-buttons-container">
      <a-button class="audit-button pass-button" @click="handlePass">
        <a-span class="button-text">通过</a-span>
      </a-button>
      <a-button class="audit-button reject-button" @click="handleReject">
        <a-span class="button-text">不通过</a-span>
      </a-button>
    </div>

    <!-- 拒绝理由弹窗 -->
    <a-modal
      v-model:visible="rejectModalVisible"
      title=""
      :footer="false"
      :closable="false"
      width="464px"
      class="reject-modal"
    >
      <div
        class="tw-bg-white tw-rounded tw-inline-flex tw-flex-col tw-justify-center tw-items-center tw-gap-6 tw-overflow-hidden tw-w-full"
      >
        <!-- 标题 -->
        <div class="tw-inline-flex tw-justify-start tw-items-center tw-gap-2">
          <div
            class="tw-flex tw-justify-start tw-items-end tw-gap-1 tw-overflow-hidden"
          >
            <div
              class="tw-justify-center tw-text-[#1d2129] tw-text-lg tw-font-medium tw-leading-normal"
              >拒绝理由</div
            >
          </div>
        </div>

        <!-- 表单内容 -->
        <div
          class="tw-self-stretch tw-flex tw-flex-col tw-justify-start tw-items-start tw-gap-4"
        >
          <!-- 输入理由区域 -->
          <div
            class="tw-self-stretch tw-flex tw-flex-col tw-justify-start tw-items-start tw-gap-2"
          >
            <div
              class="tw-self-stretch tw-inline-flex tw-justify-start tw-items-center tw-gap-3"
            >
              <div
                class="tw-flex-1 tw-inline-flex tw-flex-col tw-justify-center tw-items-start tw-gap-2"
              >
                <div
                  class="tw-self-stretch tw-flex tw-flex-col tw-justify-start tw-items-start"
                >
                  <div
                    class="tw-self-stretch tw-flex tw-flex-col tw-justify-center tw-items-start tw-overflow-hidden"
                  >
                    <div
                      class="tw-self-stretch tw-justify-start tw-text-[#1d2129] tw-text-sm tw-font-normal tw-leading-snug"
                      >请输入不通过的理由</div
                    >
                  </div>
                </div>
              </div>
              <a-button
                type="text"
                size="mini"
                class="tw-py-0.5 tw-rounded-sm tw-flex tw-justify-center tw-items-center tw-gap-2 tw-overflow-hidden"
              >
                <icon-plus class="tw-w-3 tw-h-3" />
                <span
                  class="tw-justify-start tw-text-[#165dff] tw-text-xs tw-font-normal tw-leading-tight"
                  >添加为常用理由</span
                >
              </a-button>
            </div>
            <a-input
              v-model="rejectReason"
              placeholder="输入不通过理由/直接点击确认跳过"
              class="tw-self-stretch"
            />
          </div>

          <!-- 我的常用理由区域 -->
          <div
            class="tw-self-stretch tw-flex tw-flex-col tw-justify-start tw-items-start tw-gap-2"
          >
            <div
              class="tw-self-stretch tw-inline-flex tw-justify-start tw-items-center tw-gap-3"
            >
              <div
                class="tw-flex-1 tw-inline-flex tw-flex-col tw-justify-center tw-items-start tw-gap-2"
              >
                <div
                  class="tw-self-stretch tw-flex tw-flex-col tw-justify-start tw-items-start"
                >
                  <div
                    class="tw-self-stretch tw-flex tw-flex-col tw-justify-center tw-items-start tw-overflow-hidden"
                  >
                    <div
                      class="tw-self-stretch tw-justify-start tw-text-[#1d2129] tw-text-sm tw-font-normal tw-leading-snug"
                      >我的常用理由</div
                    >
                  </div>
                </div>
              </div>
              <a-button
                type="text"
                size="mini"
                class="tw-py-0.5 tw-rounded-sm tw-flex tw-justify-center tw-items-center tw-gap-2 tw-overflow-hidden"
              >
                <span
                  class="tw-justify-start tw-text-[#165dff] tw-text-xs tw-font-normal tw-leading-tight"
                  >删除</span
                >
              </a-button>
            </div>

            <!-- 常用理由选项 -->
            <div
              class="tw-flex tw-flex-col tw-justify-start tw-items-start tw-gap-2"
            >
              <a-checkbox-group v-model="selectedReasons">
                <div class="tw-flex tw-flex-col tw-gap-2">
                  <a-checkbox
                    value="照片模糊"
                    class="tw-inline-flex tw-justify-start tw-items-center tw-gap-2"
                  >
                    <span
                      class="tw-justify-start tw-text-[#4e5969] tw-text-sm tw-font-normal tw-leading-snug"
                      >照片模糊</span
                    >
                  </a-checkbox>
                  <a-checkbox
                    value="长得太丑"
                    class="tw-inline-flex tw-justify-start tw-items-center tw-gap-2"
                  >
                    <span
                      class="tw-justify-start tw-text-[#4e5969] tw-text-sm tw-font-normal tw-leading-snug"
                      >长得太丑</span
                    >
                  </a-checkbox>
                  <a-checkbox
                    value="任务帖子已删除"
                    class="tw-inline-flex tw-justify-start tw-items-center tw-gap-2"
                  >
                    <span
                      class="tw-justify-start tw-text-[#4e5969] tw-text-sm tw-font-normal tw-leading-snug"
                      >任务帖子已删除</span
                    >
                  </a-checkbox>
                  <a-checkbox
                    value="角色不还原"
                    class="tw-inline-flex tw-justify-start tw-items-center tw-gap-2"
                  >
                    <span
                      class="tw-justify-start tw-text-[#4e5969] tw-text-sm tw-font-normal tw-leading-snug"
                      >角色不还原</span
                    >
                  </a-checkbox>
                </div>
              </a-checkbox-group>
            </div>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div
          class="tw-w-72 tw-inline-flex tw-justify-start tw-items-center tw-gap-4"
        >
          <a-button type="primary" class="tw-flex-1" @click="confirmReject">
            <span
              class="tw-justify-center tw-text-white tw-text-sm tw-font-normal tw-leading-snug"
              >确认</span
            >
          </a-button>
          <a-button class="tw-flex-1" @click="cancelReject">
            <span
              class="tw-justify-center tw-text-[#4e5969] tw-text-sm tw-font-normal tw-leading-snug"
              >取消</span
            >
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Message } from '@arco-design/web-vue';
  import { IconPlus } from '@arco-design/web-vue/es/icon';

  // 提交记录数据
  const submissionDate = ref('2025年5月12日 00:13分');
  const submissionLink = ref('https://www.douyin.com/note/7512856937408728379');

  // 弹窗相关状态
  const rejectModalVisible = ref(false);
  const rejectReason = ref('');
  const selectedReasons = ref(['照片模糊']); // 默认选中"照片模糊"

  // 处理通过审核
  const handlePass = () => {
    Message.success('审核通过');
    console.log('审核通过');
    // TODO: 调用API处理审核通过逻辑
  };

  // 处理拒绝审核
  const handleReject = () => {
    rejectModalVisible.value = true;
  };

  // 确认拒绝
  const confirmReject = () => {
    const reasons = [];
    if (rejectReason.value.trim()) {
      reasons.push(rejectReason.value.trim());
    }
    if (selectedReasons.value.length > 0) {
      reasons.push(...selectedReasons.value);
    }

    console.log('拒绝理由:', reasons);
    Message.warning('审核不通过');

    // 重置表单
    rejectReason.value = '';
    selectedReasons.value = ['照片模糊'];
    rejectModalVisible.value = false;

    // TODO: 调用API处理审核拒绝逻辑
  };

  // 取消拒绝
  const cancelReject = () => {
    // 重置表单
    rejectReason.value = '';
    selectedReasons.value = ['照片模糊'];
    rejectModalVisible.value = false;
  };
</script>

<style lang="less" scoped>
  .submission-record {
    width: 100%;
    height: 40px;
    padding: 8px 16px;
    background-color: #ffffff;
    border-radius: 4px;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    max-width: 100%;
    overflow-x: auto;

    .submission-title {
      display: flex;
      justify-content: center;
      color: var(--color-text-1, #1d2129);
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      white-space: nowrap;
    }

    .submission-platform,
    .submission-date {
      display: flex;
      justify-content: center;
      color: var(--color-text-2, #4e5969);
      font-size: 14px;
      font-weight: 400;
      line-height: 1.2;
      white-space: nowrap;
    }

    .submission-link-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;

      .submission-link-label {
        color: var(--color-text-2, #4e5969);
        font-size: 14px;
        font-weight: 400;
        line-height: 1.2;
      }

      .submission-link {
        color: var(--color-primary-5, #4080ff);
        font-size: 14px;
        font-weight: 400;
        line-height: 1.2;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  .audit-buttons-container {
    width: 100%;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
  }

  .audit-button {
    flex: 1;
    height: 56px;
    padding: 6px 20px;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    overflow: hidden;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    .button-text {
      text-align: right;
      justify-content: center;
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
    }

    &.pass-button {
      background-color: var(--color-primary-5, #4080ff);

      .button-text {
        color: #ffffff;
      }

      &:hover {
        background-color: var(--color-primary-6, #3366ff);
      }

      &:active {
        background-color: var(--color-primary-7, #2952cc);
      }
    }

    &.reject-button {
      background-color: #e0e0e0;

      .button-text {
        color: var(--color-text-2, #4e5969);
      }

      &:hover {
        background-color: #cdcdcd;
      }

      &:active {
        background-color: #afafaf;
      }
    }
  }

  // 链接样式
  :deep(.arco-link) {
    color: #4080ff;
    text-decoration: none;

    &:hover {
      color: #3366ff;
      text-decoration: underline;
    }
  }

  // 拒绝理由弹窗样式
  :deep(.reject-modal) {
    .arco-modal-header {
      display: none;
    }

    .arco-modal-body {
      padding: 0;
    }

    .arco-textarea-wrapper {
      background-color: #f7f8fa;
      border: 1px solid #e5e6eb;
      border-radius: 4px;

      &:hover {
        border-color: #c9cdd4;
      }

      &:focus-within {
        border-color: #165dff;
        box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
      }
    }

    .arco-textarea {
      background-color: transparent;
      border: none;
      font-size: 12px;
      color: #86909c;

      &::placeholder {
        color: #86909c;
      }
    }

    .arco-checkbox-wrapper {
      .arco-checkbox {
        width: 14px;
        height: 14px;

        .arco-checkbox-icon {
          width: 14px;
          height: 14px;
          border-radius: 2px;
        }

        &.arco-checkbox-checked {
          .arco-checkbox-icon {
            background-color: #165dff;
            border-color: #165dff;
          }
        }
      }
    }

    .arco-btn-primary {
      background-color: #165dff;
      border-color: #165dff;

      &:hover {
        background-color: #3366ff;
        border-color: #3366ff;
      }
    }

    .arco-btn-secondary {
      background-color: #f7f8fa;
      border-color: #e5e6eb;
      color: #4e5969;

      &:hover {
        background-color: #e5e6eb;
      }
    }
  }
</style>
