<template>
  <div class="container">
    <Breadcrumb :items="['menu.list', 'menu.list.cardList']" />
    <a-row :gutter="20" align="stretch">
      <a-col :span="24">
        <a-card class="general-card" title="活动中心">
          <a-row justify="space-between">
            <a-col :span="24">
              <a-tabs :default-active-tab="1" type="rounded">
                <a-tab-pane key="1" title="全部">
                  <QualityInspection />
                  <TheService />
                  <RulesPreset />
                </a-tab-pane>
                <a-tab-pane key="2" title="草稿箱">
                  <QualityInspection />
                </a-tab-pane>
                <a-tab-pane key="3" title="审核中">
                  <TheService />
                </a-tab-pane>
                <a-tab-pane key="4" title="招募中">
                  <RulesPreset />
                </a-tab-pane>
                <a-tab-pane key="5" title="招募结束">
                  <RulesPreset />
                </a-tab-pane>
                <a-tab-pane key="6" title="已公布">
                  <RulesPreset />
                </a-tab-pane>
                <a-tab-pane key="7" title="已结束">
                  <RulesPreset />
                </a-tab-pane>
              </a-tabs>
            </a-col>
            <a-input-search
              :placeholder="$t('cardList.searchInput.placeholder')"
              style="width: 240px; position: absolute; top: 60px; right: 280px"
            />

            <a-button
              type="primary"
              style="width: 240px; position: absolute; top: 60px; right: 20px;border: 1px solid #000;"
              @click="createHandle"
            >
              创建活动
              <template #icon>
                <icon-plus />
              </template>
            </a-button>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { useRouter } from 'vue-router';
  import QualityInspection from './components/quality-inspection.vue';
  import TheService from './components/the-service.vue';
  import RulesPreset from './components/rules-preset.vue';

  const route = useRouter();

  const createHandle = () => {
    route.push({ path: '/activity/create' });
  };
</script>

<script lang="ts">
  export default {
    name: 'Card',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;

    :deep(.arco-list-content) {
      overflow-x: hidden;
    }

    :deep(.arco-card-meta-title) {
      font-size: 14px;
    }
  }

  :deep(.arco-list-col) {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  :deep(.arco-list-item) {
    width: 33%;
  }

  :deep(.block-title) {
    margin: 0 0 12px 0;
    font-size: 14px;
  }

  :deep(.list-wrap) {
    // min-height: 140px;
    .list-row {
      align-items: stretch;

      .list-col {
        margin-bottom: 16px;
      }
    }

    :deep(.arco-space) {
      width: 100%;

      .arco-space-item {
        &:last-child {
          flex: 1;
        }
      }
    }
  }
</style>
