<template>
  <div class="container">
    <Breadcrumb :items="['menu.list', 'menu.list.orderDetails']" />
    <a-card class="general-card" :title="$t('menu.list.orderDetails')">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            label-align="left"
            auto-label-width
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
          >
            <a-row :gutter="16">
              <a-col :span="span">
                <a-form-item
                  field="orderItemId"
                  :label="$t('orderDetails.form.number')"
                >
                  <a-input
                    v-model="formModel.orderItemId"
                    :placeholder="$t('orderDetails.form.number.placeholder')"
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="status"
                  :label="$t('orderDetails.form.status')"
                >
                  <a-input
                    v-model="formModel.status"
                    :placeholder="$t('orderDetails.form.status.placeholder')"
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="userId"
                  :label="$t('orderDetails.form.userCN')"
                >
                  <a-input
                    v-model="formModel.userId"
                    :placeholder="$t('orderDetails.form.userCN.placeholder')"
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="mobile"
                  :label="$t('orderDetails.form.contact')"
                >
                  <a-input
                    v-model="formModel.mobile"
                    :placeholder="$t('orderDetails.form.contact.placeholder')"
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="mobile"
                  :label="$t('orderDetails.form.loginNumber')"
                >
                  <a-input
                    v-model="formModel.mobile"
                    :placeholder="
                      $t('orderDetails.form.loginNumber.placeholder')
                    "
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="goodsName"
                  :label="$t('orderDetails.form.commodityName')"
                >
                  <a-input
                    v-model="formModel.goodsName"
                    :placeholder="
                      $t('orderDetails.form.commodityName.placeholder')
                    "
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="goodsItemName"
                  :label="$t('orderDetails.form.couponsName')"
                >
                  <a-input
                    v-model="formModel.goodsItemName"
                    :placeholder="
                      $t('orderDetails.form.couponsName.placeholder')
                    "
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="signUpDate"
                  :label="$t('orderDetails.form.sessionDate')"
                >
                  <a-input
                    v-model="formModel.signUpDate"
                    :placeholder="
                      $t('orderDetails.form.sessionDate.placeholder')
                    "
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="span">
                <a-form-item
                  field="provideTime"
                  :label="$t('orderDetails.form.releaseTime')"
                >
                  <a-range-picker
                    v-model="formModel.provideTime"
                    style="width: 90%"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-col :flex="'80px'" style="text-align: right">
          <a-space direction="vertical" :size="18">
            <a-button type="primary" @click="search">
              <template #icon>
                <icon-search />
              </template>
              {{ $t('orderDetails.form.search') }}
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              {{ $t('orderDetails.form.reset') }}
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-table
        row-key="orderItemId"
        :loading="loading"
        :pagination="{
          current: pagination.page,
          pageSize: pagination.limit,
          total: pagination.total,
          showTotal: true,
          showJumper: false,
          showPageSize: true,
          pageSizeOptions: [10, 20, 50, 100],
          onPageSizeChange: onPageSizeChange,
        }"
        :data="renderData"
        :bordered="false"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column
            title="卷码"
            data-index="orderItemId"
          ></a-table-column>
          <a-table-column title="订单状态" data-index="status">
            <template #cell="{ record }">
              <a-button v-if="record.status === 2" status="success"
                >已核销</a-button
              >
              <a-button v-else-if="record.status === 1" status="warning"
                >待核销</a-button
              >
              <a-button v-else-if="record.status === 3" status="danger"
                >已作废</a-button
              >
              <a-button v-else-if="record.status === 4" status="warning"
                >请假</a-button
              >
              <a-button v-else-if="record.status === 5" status="danger"
                >已过期</a-button
              >
              <a-button v-else status="danger">未知状态</a-button>
            </template>
          </a-table-column>
          <a-table-column title="用户CN" data-index="userId"></a-table-column>
          <a-table-column title="联系方式" data-index="mobile"></a-table-column>
          <a-table-column
            title="登录手机号"
            data-index="mobile"
          ></a-table-column>
          <a-table-column
            title="商品名称"
            data-index="goodsName"
          ></a-table-column>
          <a-table-column
            title="卷名称"
            data-index="goodsItemName"
          ></a-table-column>
          <a-table-column
            title="场次日期"
            data-index="signUpDate"
          ></a-table-column>
          <a-table-column
            title="发放时间"
            data-index="provideTime"
          ></a-table-column>
          <a-table-column title="作废原因" data-index="reason"></a-table-column>
          <a-table-column
            title="操作员"
            data-index="operationId"
          ></a-table-column>
          <a-table-column title="操作" data-index="operation">
            <template #cell>
              <a-link @click="handleClick">作废</a-link>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
    <a-modal
      :visible="visible"
      @ok="handleOk"
      :footer="false"
      @cancel="handleCancel"
    >
      <template #title>
        <div style="display: flex; align-items: center; gap: 8px">
          <icon-exclamation-circle style="color: #faad14; font-size: 18px" />
          <span style="font-size: 16px; font-weight: 500"
            >确认要作废该券码吗</span
          >
        </div>
      </template>
      <div style="padding: 0 8px 8px 8px">
        <div style="color: #666; font-size: 14px; margin-bottom: 20px"
          >作废后此券码将作废，无法正常核验，请选择作废原因</div
        >
        <div style="display: flex; gap: 12px; margin-bottom: 16px">
          <a-button
            v-for="item in reasonList"
            :key="item"
            :status="selectedReason === item ? 'normal' : undefined"
            style="
              min-width: 110px;
              border-radius: 6px;
              font-size: 14px;
              padding: 0 16px;
              height: 36px;
            "
            @click="selectReason(item)"
          >
            <template v-if="selectedReason === item">
              <icon-check-circle-fill />
            </template>
            {{ item }}
          </a-button>
        </div>
        <div style="position: relative; margin-bottom: 24px">
          <a-input
            v-model="otherReason"
            :max-length="20"
            placeholder="其他原因"
            style="
              height: 40px;
              border-radius: 6px;
              font-size: 14px;
              background: #f7f8fa;
              border: none;
              padding-right: 48px;
            "
            @focus="selectedReason = ''"
          />
          <span
            style="
              position: absolute;
              right: 12px;
              top: 50%;
              transform: translateY(-50%);
              color: #999;
              font-size: 13px;
            "
          >
            {{ otherReason.length }}/20
          </span>
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 16px">
          <a-button
            style="min-width: 72px; height: 36px; border-radius: 6px"
            @click="handleCancel"
            >取消</a-button
          >
          <a-button
            type="primary"
            style="min-width: 72px; height: 36px; border-radius: 6px"
            @click="handleOk"
            >确定</a-button
          >
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, onUnmounted } from 'vue';
  import useLoading from '@/hooks/loading';
  import { PolicyParams, queryInvitationCouponsInfoList } from '@/api/list';
  import { Pagination } from '@/types/global';

  const generateFormModel = () => {
    return {
      goodsId: '',
      goodsItemId: '',
      goodsItemName: '',
      goodsName: '',
      mobile: '',
      operationId: '',
      orderItemId: '',
      provideTime: '',
      signUpDate: '',
      status: '',
      userId: '',
    };
  };
  const { loading, setLoading } = useLoading(true);
  const renderData = ref<any[]>([]);
  const formModel = ref(generateFormModel());

  const basePagination: Pagination = {
    page: 1,
    limit: 20,
  };
  const pagination = reactive({
    ...basePagination,
  });

  const fetchData = async (params: PolicyParams = { page: 1, limit: 20 }) => {
    setLoading(true);
    try {
      const { data } = await queryInvitationCouponsInfoList(params);
      renderData.value = data.list;
      pagination.page = params.page;
      pagination.total = data.total;
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };

  const search = () => {
    fetchData({
      ...basePagination,
      ...formModel.value,
    } as unknown as PolicyParams);
    console.log('🚀 ~ search ~ formModel:', formModel);
  };

  const onPageChange = (page: number) => {
    fetchData({ ...basePagination, page });
  };

  fetchData();
  const reset = () => {
    formModel.value = generateFormModel();
  };

  const visible = ref(false);

  const handleClick = () => {
    visible.value = true;
  };

  const onPageSizeChange = (pageSize: number) => {
    pagination.limit = pageSize;
    pagination.page = 1;
    // fetchData({ ...pagination });
  };

  const span = ref(6);

  const handleResize = () => {
    const width = window.innerWidth;
    if (width < 768) {
      span.value = 24;
    } else if (width < 992) {
      span.value = 12;
    } else if (width < 1200) {
      span.value = 8;
    } else if (width < 1921) {
      span.value = 7;
    } else {
      span.value = 6;
    }
  };

  handleResize();

  onMounted(() => {
    window.addEventListener('resize', handleResize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });

  // 作废原因相关变量和方法
  const reasonList = ['发错卷码', '用户无法到场'];
  const selectedReason = ref('');
  const otherReason = ref('');

  function selectReason(item: string) {
    selectedReason.value = item;
    if (item !== '其他') {
      otherReason.value = '';
    }
  }

  const handleOk = () => {
    visible.value = false;
  };
  const handleCancel = () => {
    visible.value = false;
  };
</script>

<script lang="ts">
  export default {
    name: 'SearchTable',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
    left: -20px;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  @media screen and (max-width: 768px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 280px);
    }
  }

  @media screen and (max-width: 1920px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 500px);
    }
  }
  @media screen and (min-width: 1921px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 500px);
    }
  }
  :deep(.arco-table-container) {
    overflow: auto;
  }
  :deep(.arco-form) {
    flex-wrap: wrap;
    display: flex;
    .arco-row {
      flex-direction: row;
    }
  }
  .void-modal {
    .void-modal-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    .void-modal-content {
      .void-modal-desc {
        font-size: 14px;
        margin-bottom: 16px;
      }
      .void-modal-reason-group {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16px;
        .void-modal-reason-btn {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      .void-modal-input-wrap {
        position: relative;
        margin-bottom: 16px;
        .void-modal-input {
          width: 100%;
        }
        .void-modal-count {
          position: absolute;
          right: 10px;
          bottom: 10px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
      .void-modal-footer {
        display: flex;
        justify-content: flex-end;
        .void-modal-cancel {
          margin-right: 8px;
        }
      }
    }
  }
</style>
