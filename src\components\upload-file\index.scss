.upload-file-block{
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;

  .image-item-block{
    padding: 0 10px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .file-block{

    }

    .upload-success-block{
      .image-space{

        .overlay {
          position: absolute;
          top: 0;
          left: 10px;
          width: calc(100% - 2 * 10px);
          height: 80px;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 1;

          &:hover {
            opacity: 1;
            cursor: pointer;
          }
        }
      }
    }
    .upload-load-block{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    .upload-empty-block{

    }

    .upload-fail-block{
      .image-space{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .overlay {
          position: absolute;
          top: 0;
          left: 10px;
          width: calc(100% - 2 * 10px);
          height: 80px;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
          z-index: 1;

          &:hover {
            opacity: 1;
            cursor: pointer;
          }
        }
      }
    }
  }
}

.input-block{
  margin-top: 10px;
  margin-bottom: 20px;
}

.arco-upload-list-item{
  margin-top: 0 !important;
  .add-content{
    margin-top: 5px;
  }
}