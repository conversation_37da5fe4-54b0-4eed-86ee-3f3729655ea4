import Mock from 'mockjs';
import qs from 'query-string';
import setupMock, { successResponseWrap } from '@/utils/setup-mock';
import { GetParams } from '@/types/global';

const userList = Mock.mock({
  'list|100': [
    {
      'id|+1': 1,
      'userCN|1': ['PPK', 'Arco Design', '小明', '小红', '小李'],
      'phoneNumber|1': [
        '13382876037',
        '13812345678',
        '13987654321',
        '13700001111',
        '13688889999',
      ],
      'date': function () {
        const start = new Date('2025-07-01').getTime();
        const end = new Date('2025-07-10').getTime();
        return new Date(start + Math.random() * (end - start))
          .toISOString()
          .split('T')[0];
      },
    },
  ],
});

setupMock({
  setup() {
    // submit
    Mock.mock(new RegExp('/api/channel-form/submit'), () => {
      return successResponseWrap('ok');
    });
    // 用户列表
    Mock.mock(new RegExp('/api/form/step/user-list'), (params: GetParams) => {
      const { current = 1, pageSize = 10 } = qs.parseUrl(params.url).query;
      const p = Number(current);
      const ps = Number(pageSize);
      return successResponseWrap({
        list: userList.list.slice((p - 1) * ps, p * ps),
        total: userList.list.length,
      });
    });
  },
});
