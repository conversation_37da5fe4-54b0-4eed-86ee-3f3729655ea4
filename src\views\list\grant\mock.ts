import Mock from 'mockjs';
import qs from 'query-string';
import setupMock, { successResponseWrap } from '@/utils/setup-mock';
import { GetParams } from '@/types/global';

const { Random } = Mock;

const data = Mock.mock({
  'list|200': [
    {
      'batchNo|10000-99999': 1,
      'commodityName|1': ['常州漫展', '苏州漫展', '北京漫展', '上海漫展'],
      'couponsName|1': ['优惠券1', '优惠券2', '优惠券3', '优惠券4'],
      'uploadTotal|100-200': 1,
      'uploadFailed|0-10': 1,
      'uploadSuccess|90-200': 1,
      'publishCount|80-200': 1,
      'releaseTime': function () {
        const start = new Date('2025-01-01').getTime();
        const end = new Date('2025-06-18').getTime();
        return new Date(start + Math.random() * (end - start))
          .toISOString()
          .split('T')[0];
      },
      'operator|1': ['admin', 'operator1', 'operator2', 'system'],
      'operation': '查看',
    },
  ],
});

const examine = Mock.mock({
  'list|100': [
    {
      'userCN|1': ['PPK', 'Arco Design', '小明', '小红', '小李'],
      'phoneNumber|1': [
        '13382876037',
        'Arco Design',
        '13812345678',
        '13987654321',
      ],
      'date|1-2': [
        function () {
          const start = new Date('2025-07-01').getTime();
          const end = new Date('2025-07-10').getTime();
          return new Date(start + Math.random() * (end - start))
            .toISOString()
            .split('T')[0];
        },
      ],
    },
  ],
});

setupMock({
  setup() {
		Mock.mock(new RegExp('/api/list/examine'), (params: GetParams) => {
			const { current = 1, pageSize = 10 } = qs.parseUrl(params.url).query;

			const p = current as number;
			const ps = pageSize as number;
			return successResponseWrap({
				list: examine.list.slice((p - 1) * ps, p * ps),
				total: 100,
			});
		});
    Mock.mock(new RegExp('/api/list/grant'), (params: GetParams) => {
      const { current = 1, pageSize = 10 } = qs.parseUrl(params.url).query;
      const p = current as number;
      const ps = pageSize as number;
      return successResponseWrap({
        list: data.list.slice((p - 1) * ps, p * ps),
        total: 200,
      });
    });
  },
});
