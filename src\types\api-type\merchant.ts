/**
 * 商户列表项状态枚举
 */
// eslint-disable-next-line no-shadow
export enum EnumMerchantListItemStatus {
  /**
   * 认证中
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  IN_AUTHENTICATION = 1,
  /**
   * 已认证
   */
  AUTHENTICATED = 2,
  /**
   * 认证驳回
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  AUTHENTICATION_REJECTED = 3,
  /**
   * 封禁
   */
  BANNED = 4,
}

/**
 * 响应模型 - 商户列表项
 */
export interface ResponseMerchantListItemModel {
  merchantId: string;

  principalName: string;

  merchantStatus: EnumMerchantListItemStatus;
}
