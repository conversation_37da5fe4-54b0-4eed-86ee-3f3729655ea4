import { AttributeModuleGroup } from '@/components/module/type/attribute-module-group';
import { CustomGroupModule } from '@/components/module/entity/struct/group';

export interface IFormItemModel {
  /** 模组key - uuid */
  uuid: string;
  /** 模组内模块部分 */
  entity?: CustomGroupModule;
}
export interface IFormData {
  id: string;
  baseInfo: IFormItemModel[];
  taskInfo: IFormItemModel[];
}

//
// export interface RoleBaseInfoModel_V2 {
//   uuid: string;
//   /** 内部特定组件Key，如：CN_Module、Tel_Module、QQ_Module。undefined则为放置通用控件 */
//   key?: string;
//   /** 是否勾选必选 */
//   required?: boolean;
//   /** 自定义组件标题 */
//   title?: string;
//   /** 自定义组件标题描述 */
//   desc?: string;
//   /** 自定义组件类型 */
//   moduleType?: EnumModuleType[];
//
//   itemModule?: GroupModel;
//
//   editFlag: boolean;
// }
//
// export interface RoleTaskPlatformModel_V2 {
//   uuid: string;
//   /** 内部特定组件Key，如：todo 。undefined则为放置通用控件 */
//   key: string;
//   /** 自定义组件标题 */
//   title?: string;
//   /** 自定义组件标题描述 */
//   desc?: string;
//   /** 自定义组件类型 */
//   moduleType?: EnumModuleType[];
//   itemModule?: GroupModel;
// }
