import axios from 'axios';
import qs from 'query-string';
import type { DescData } from '@arco-design/web-vue/es/descriptions/interface';

export interface PolicyRecord {
  id: string;
  number: number;
  name: string;
  contentType: 'img' | 'horizontalVideo' | 'verticalVideo';
  filterType: 'artificial' | 'rules';
  count: number;
  status: 'online' | 'offline';
  createdTime: string;
}

export interface PolicyParams extends Partial<PolicyRecord> {
  page: number;
  limit: number;
}

export interface PolicyListRes {
  list: PolicyRecord[];
  total: number;
}

export function queryPolicyList(params: PolicyParams) {
  return axios.get<PolicyListRes>('/api/list/policy', {
    params,
    paramsSerializer: (obj) => {
      return qs.stringify(obj);
    },
  });
}

export interface ServiceRecord {
  id: number;
  title: string;
  description: string;
  name?: string;
  actionType?: string;
  icon?: string;
  data?: DescData[];
  enable?: boolean;
  expires?: boolean;
}
export function queryInspectionList() {
  return axios.get('/api/list/quality-inspection');
}

export function queryTheServiceList() {
  return axios.get('/api/list/the-service');
}

export function queryRulesPresetList() {
  return axios.get('/api/list/rules-preset');
}

export interface GrantParams extends Partial<GrantRecord> {
  current: number;
  pageSize: number;
}

export function queryGrantList(params: GrantParams) {
  return axios.get<GrantListRes>('/api/list/grant', {
    params,
    paramsSerializer: (obj) => {
      return qs.stringify(obj);
    },
  });
}

export function queryExamineData(params: GrantParams) {
  return axios.get<GrantListRes>('/api/list/examine', {
    params,
    paramsSerializer: (obj) => {
      return qs.stringify(obj);
    },
  });
}

export interface pageParams {
  page?: number;
  limit?: number;
  cursor?: string;
}
export interface FormSearchModel extends pageParams {
  name?: string;
  coupons?: string;
  providedTime?: string[];
  providedStartTime?: string;
  providedEndTime?: string;
}

export interface GrantRecord {
  merchantInvitationId: string;
  goodsName: string;
  goodsItemName: string;
  analysisTotalCount: number;
  analysisFailCount: number;
  analysisSuccessCount: number;
  ticketProvidedCount: number;
  createTime: string;
  operationName: string;
}

export interface pageResponseModel<T> {
  list: T;
  total: number;
  limit:number;
  page:number;
}
export type GrantListRes = pageResponseModel<GrantRecord[]>;

export interface DetailPageResp {
  merchantInvitationId: string;
  goodsName: string;
  goodsItemName: string;
  createTime: string;
  detailPage: pageResponseModel<DetailPageItemModel[]>;
}

export interface DetailPageItemModel {
  merchantInvitationUserId: string;
  userCn: string;
  mobile: string;
  signUpDate: string;
  signUpDates: string[];
  name?: string;
  idCard?: string;
}

export const ListAPI = {
  queryInspectionPageList: (params: FormSearchModel) => {
    return axios.get<GrantListRes>('/api/pd/v2/merchant/invitation/page', {
      params,
    });
  },
  queryInspectionDetailPageList: (
    merchantInvitationId: string,
    params: pageParams
  ) => {
    return axios.get<pageResponseModel<DetailPageItemModel[]>>(
      `/api/pd/v2/merchant/invitation/detail/${merchantInvitationId}`,
      { params }
    );
  },
};


export interface CancellationParams {
  reason: string;
  orderItemId: string;
}

export const OrderListAPI = {
  queryInspectionOrderItemPageList: (params: any) => {
    return axios.post('/api/pd/v2/merchant/invitation/coupons/infoList', params);
  },

  queryCancelInspectionOrderItem: (params: CancellationParams) =>{
    return axios.post('/api/pd/v2/merchant/invitation/coupons/cancellation',params);
  }
}

export function queryInvitationCouponsInfoList(params: any) {
  return axios.post('/api/pd/v2/merchant/invitation/coupons/infoList', params);
}
export interface CancellationParams {
  reason: string;
  orderItemId: string;
}

export function queryCancellationCoupons(params: CancellationParams) {
  return axios.post(
    '/api/pd/v2/merchant/invitation/coupons/cancellation',
    params
  );
}
