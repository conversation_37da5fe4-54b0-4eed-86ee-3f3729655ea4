<template>
  <div class="activity-radio">
    <div class="body">
      <template v-for="item in options" :key="item">
        <div class="item">
          <div class="radio"></div>
          <div class="label">{{ item.label }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    options: [],
    disabled: {
      type: Boolean,
      default: false,
    },
  });
</script>

<style scoped lang="less">
  .activity-radio {
    display: flex;
    align-items: center;

    .body {
      display: flex;
      padding: 5px 0px;
      align-items: center;
      flex-direction: column;
      gap: 24px;
      align-self: stretch;

      .item {
        display: flex;
        align-items: center;
        gap: 8px;

        .radio {
          width: 14px;
          height: 14px;
          border-radius: 7px;
          border: 2px solid var(--border-2, #e5e6eb);
          background: var(--bg-2, #fff);
        }

        .text {
          color: var(--text-1, #1d2129);
          /* 14/CN-Regular */
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }
      }
    }
  }
</style>
