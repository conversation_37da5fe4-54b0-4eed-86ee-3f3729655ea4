import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { AttributeModuleGroup } from '@/components/module/type/attribute-module-group';
import { CustomInputTextModule } from '@/components/module/entity/component/input-text';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import { ValidateUtils } from '@/utils/validate';
import { EnumRoleContentType } from '@/components/module/enum/enum-role-content-type';
import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { ComponentFactory } from '@/components/module/factory/component-factory';
import useComponentStore from '@/components/module/store/component';
import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';
import { ConvertUtils } from '@/utils/convert';
import { nextTick } from 'vue';

export class CustomGroupModule {
  /** 组key */
  private readonly _groupUUId: string;

  /** 组模块是否为【已封装】模块。除了_groupRequired外，不可修改内部任何参数 */
  private readonly _existGroup: boolean = false;

  /** 分组标题 */
  private _groupTitle: string;

  /** 分组描述 */
  private _groupDesc: string | undefined;

  /** 分组是否必填 */
  private _groupRequired = false;

  /** 分组是否仅展示，无需填写 -- 说明文字部分input-area，分组模块为空 */
  private _groupReadOnly: boolean | undefined = undefined;

  /** 分组是否仅展示，无需填写选项 */
  private _groupReadOnlyShowFlag = false;

  /** 分组模块 */
  private readonly _groupModules: Array<ICustomComponent | undefined>;

  /** 分组模块所在区域 */
  private readonly _groupInnerSpace: EnumRoleContentType;

  constructor(
    attribute: AttributeModuleGroup,
    roleInfoType: EnumRoleContentType
  ) {
    // 赋值模块所在区域
    this._groupInnerSpace = roleInfoType;
    // 赋值UUID
    this._groupUUId = attribute.uuid;
    // 判断是否为封装好的组模块
    this._existGroup = ValidateUtils.isNotEmpty(attribute.key);
    // 赋值必选项，默认为false
    this._groupRequired = attribute.required ?? false;
    // 赋值标题
    this._groupTitle = ValidateUtils.isNotEmpty(attribute.title)
      ? attribute.title!.toUpperCase()
      : '请输入组件标题';
    // 赋值描述,如果没有内容则为''
    this._groupDesc = attribute.description ?? '';
    console.log('attribute', attribute);
    // 设置模组内容
    this._groupModules = attribute.list ?? [];
    // 设置模组是否只读
    if (attribute.readonly) {
      this._groupRequired = !this._groupReadOnly;
      // 在只读状态下，不再需要模块描述，因此将其设置为undefined
      this._groupDesc = undefined;
    }
  }

  /** 获取组模块key - uuid */
  public getGroupUUID(): string {
    return this._groupUUId;
  }

  /** 获取组模块标题 */
  public getGroupTitle(): string {
    return this._groupTitle;
  }

  /** 获取组模块描述 */
  public getGroupDesc(): string | undefined {
    return this._groupDesc;
  }

  /** 获取组模块内容是否必填 */
  public getGroupRequired(): boolean {
    return this._groupRequired;
  }

  /** 获取组模块是否仅展示，无需填写 -- 说明文字部分input-area，分组模块为空 */
  public getGroupReadOnly(): boolean | undefined {
    return this._groupReadOnly;
  }

  /** 获取组模块内容模块实体 */
  public getGroupModules(): Array<ICustomComponent | undefined> {
    return this._groupModules;
  }

  /** 获取某个组模块内容模块实体 */
  public getGroupModule(index: number): ICustomComponent | undefined {
    return this._groupModules[index];
  }

  /** 获取组模块所在区域 */
  public getGroupSpace(): EnumRoleContentType {
    return this._groupInnerSpace;
  }

  /** 获取组模块是否为封装模块 */
  public getExistFlag(): boolean {
    return this._existGroup;
  }

  public getGroupHasReadonlyFlag(): boolean {
    return this._groupReadOnlyShowFlag;
  }

  /** 设置组模块标题 */
  public setGroupTitle(title: string) {
    if (this._existGroup) {
      throw new Error('基础模块不可修改必挑剔');
    }
    if (ValidateUtils.isNullOrEmpty(title)) {
      throw new Error('组标题不能为空');
    }
    this._groupTitle = title.toUpperCase();
  }

  /** 设置组模块描述 */
  public setGroupDesc(desc?: string) {
    if (this._existGroup) {
      throw new Error('基础模块不可修改描述内容');
    }
    this._groupDesc = desc ?? '';
  }

  /** 设置组模块是否必填 */
  public setGroupRequired(required: boolean) {
    if (this._existGroup) {
      throw new Error('基础模块不可修改必填项');
    }

    this._groupRequired = required;
  }

  /** 添加组模块内容模块 */
  public addGroupModule(module: ICustomComponent) {
    if (this._existGroup) {
      throw new Error('基础模块不可添加新模块');
    }
    if (this._groupReadOnly) {
      throw new Error('仅展示模块不可添加新模块');
    }
    this._groupModules.push(module);
  }

  public setGroupReadonlyFlag() {
    if (this._groupReadOnly) {
      this._groupReadOnlyShowFlag = true;
    } else {
      const structType =
        this._groupModules[0]?.getComponentStructType() ?? undefined;
      const rightTYpe =
        structType !== undefined &&
        structType === EnumComponentStructType.TEXTAREA;

      this._groupReadOnlyShowFlag =
        this._groupModules.length === 1 && rightTYpe;
    }
  }

  private async setGroupReadOnlyPrivate(readonly: boolean | undefined) {
    // 设置模块组的只读状态，如果readonly为真，则设置为只读，否则为可写
    this._groupReadOnly = readonly;
    // 如果当前模块组处于只读状态，则执行以下操作
    if (this._groupReadOnly) {
      this._groupRequired = !this._groupReadOnly;
      // 在只读状态下，不再需要模块描述，因此将其设置为undefined
      this._groupDesc = undefined;

      if (
        !this._groupModules ||
        this._groupModules.length !== 1 ||
        this._groupModules[0]?.getComponentType() !==
          EnumComponentFormatType.InputArea
      ) {
        // 清空模块组中的所有模块，以确保只读状态下的展示内容不受其他模块影响
        this._groupModules.length = 0;
        await useComponentStore()
          .getMetaComponent(EnumComponentStructType.TEXTAREA)
          .then((res: ComponentUnifiedAttributeModel | undefined) => {
            if (res) {
              // 创建一个新的自定义文本输入模块，用于展示只读内容
              this._groupModules.push(
                new CustomInputTextModule(ConvertUtils.DeepCopy(res))
              );
            } else {
              throw new Error('无法找到TEXTAREA元组件属性信息');
            }
          });
      }
    }

    await nextTick(() => {
      this.setGroupReadonlyFlag();
    });
  }

  /** 设置组模块是否仅展示，无需填写 -- 说明文字部分input-area，组模块内容模块为空 */
  public setGroupReadOnly(readonly: boolean | undefined) {
    // 检查当前模块组是否为不可修改的基础模块
    if (this._existGroup) {
      // 如果是基础模块，则抛出错误，不允许修改
      throw new Error('基础模块不可修改');
    }
    this.setGroupReadOnlyPrivate(readonly);
  }
}
