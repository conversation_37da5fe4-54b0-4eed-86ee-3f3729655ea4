export default {
  'menu.list.orderDetails': '邀请票订单详情',
  'orderDetails.form.name': '商品名称',
  'orderDetails.form.name.placeholder': '请输入商品名称',
  'orderDetails.form.coupons': '卷名称',
  'orderDetails.form.coupons.placeholder': '请输入卷名称',
  'orderDetails.form.startTime': '发放时间',

  'orderDetails.form.number': '卷码',
  'orderDetails.form.status': '订单状态',
  'orderDetails.form.userCN': '用户CN',
  'orderDetails.form.contact': '联系方式',
  'orderDetails.form.loginNumber': '登录手机号',
  'orderDetails.form.commodityName': '商品名称',
  'orderDetails.form.couponsName': '卷名称',
  'orderDetails.form.sessionDate': '场次日期',
  'orderDetails.form.releaseTime': '发放时间',
  'orderDetails.form.number.placeholder': '请输入卷码',
  'orderDetails.form.status.placeholder': '请输入订单状态',
  'orderDetails.form.userCN.placeholder': '请输入用户CN',
  'orderDetails.form.contact.placeholder': '请输入联系方式',
  'orderDetails.form.loginNumber.placeholder': '请输入登录手机号',
  'orderDetails.form.commodityName.placeholder': '请输入商品名称',
  'orderDetails.form.couponsName.placeholder': '请输入卷名称',
  'orderDetails.form.sessionDate.placeholder': '请输入场次日期',
  'orderDetails.form.releaseTime.placeholder': '请输入发放时间',

  'orderDetails.form.contentType': '内容体裁',
  'orderDetails.form.contentType.img': '图文',
  'orderDetails.form.contentType.horizontalVideo': '横版短视频',
  'orderDetails.form.contentType.verticalVideo': '竖版小视频',
  'orderDetails.form.filterType': '筛选方式',
  'orderDetails.form.filterType.artificial': '人工筛选',
  'orderDetails.form.filterType.rules': '规则筛选',
  'orderDetails.form.createdTime': '创建时间',
  'orderDetails.form.status.online': '已上线',
  'orderDetails.form.status.offline': '已下线',
  'orderDetails.form.search': '查询',
  'orderDetails.form.reset': '重置',
  'orderDetails.form.selectDefault': '全部',
  'orderDetails.operation.create': '发放卷码',
  'orderDetails.operation.import': '批量导入',
  'orderDetails.operation.download': '下载',
  // columns
  'orderDetails.columns.number': '卷码',
  'orderDetails.columns.status': '订单状态',
  'orderDetails.columns.userCN': '用户CN',
  'orderDetails.columns.contact': '联系方式',
  'orderDetails.columns.loginNumber': '登录手机号',
  'orderDetails.columns.commodityName': '商品名称',
  'orderDetails.columns.couponsName': '卷名称',
  'orderDetails.columns.sessionDate': '场次日期',
  'orderDetails.columns.releaseTime': '发放时间',
  'orderDetails.columns.reason': '作废原因',
  'orderDetails.columns.operator': '操作员',
  'orderDetails.columns.operation': '操作',

  // size
  'orderDetails.size.mini': '迷你',
  'orderDetails.size.small': '偏小',
  'orderDetails.size.medium': '中等',
  'orderDetails.size.large': '偏大',
  // actions
  'orderDetails.actions.refresh': '刷新',
  'orderDetails.actions.density': '密度',
  'orderDetails.actions.columnSetting': '列设置',
};
