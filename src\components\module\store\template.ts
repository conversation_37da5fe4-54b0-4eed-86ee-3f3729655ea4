import { ResponseGetModuleItemModel } from '@/api/module/type';
import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';
import { StoreFileTypeStateItemModel } from '@/components/module/store/type';

export const ModuleTemplateData: ResponseGetModuleItemModel[] = [
  {
    id: '1',
    uuid: 'uuid',
    key: 'cn',
    title: 'cn',
    description: '',

    list: [
      {
        format: EnumComponentFormatType.InputText,
        maxLength: 8,
        placeholder: '请输入您的CN',
        type: EnumComponentStructType.TEXT,
      },
    ],
    required: true,
  },
  {
    description: '',
    id: '3',
    key: 'qq',
    list: [
      {
        format: EnumComponentFormatType.InputQQ,
        placeholder: '请输入您的QQ',
        type: EnumComponentStructType.TEXT,
      },
    ],
    required: true,
    title: 'qq',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '2',
    key: 'mobile',
    list: [
      {
        format: EnumComponentFormatType.InputMobile,
        placeholder: '请输入您的联系电话',
        minLength: 11,
        maxLength: 11,
        type: EnumComponentStructType.TEXT,
      },
    ],
    required: true,
    title: '联系电话',
    uuid: 'uuid',
  },

  {
    description: '',
    id: '3',
    key: 'sex',
    list: [
      {
        format: EnumComponentFormatType.Radio,
        type: EnumComponentStructType.RADIO,
        options: [
          {
            key: '1',
            value: '1',
            label: '男',
          },
          {
            key: '2',
            value: '2',
            label: '女',
          },
        ],
        length: 2,
      },
    ],
    required: true,
    title: '性别',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '4',
    key: '实名信息',
    list: [
      {
        format: EnumComponentFormatType.InputName,
        placeholder: '请输入您的姓名',
        minLength: 1,
        maxLength: 12,
        type: EnumComponentStructType.TEXT,
      },
      {
        format: EnumComponentFormatType.InputIDCard,
        placeholder: '请输入您的身份证号码',
        minLength: 18,
        maxLength: 18,
        type: EnumComponentStructType.TEXT,
      },
    ],
    required: true,
    title: '实名信息',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '5',
    key: '出生日期',
    list: [
      {
        format: EnumComponentFormatType.DateTime,
        type: EnumComponentStructType.DATETIME,
      },
    ],
    required: true,
    title: '出生日期',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '6',
    key: '邮箱',
    list: [
      {
        format: EnumComponentFormatType.InputEmail,
        placeholder: '请输入您的邮箱',
        type: EnumComponentStructType.TEXT,
      },
    ],
    required: true,
    title: '邮箱',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '7',
    key: '报名规则',
    list: [
      {
        format: EnumComponentFormatType.InputArea,
        type: EnumComponentStructType.TEXTAREA,
        templates: [
          {
            key: '转评赞模版',
            label: '转评赞模版',
            value: '单日转发15\n双日转发30\n评论20\n点赞30',
          },
          {
            key: '发布数量模板',
            label: '发布数量模板',
            value: '发布官方照片3张\n现场场照6张\n单日转发15\n双日转发30\n0/50',
          },
        ],
        exampleImages: [],
        value: '',
      },
    ],
    readonly: true,
    title: '报名规则',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '8',
    key: '复制文案',
    list: [
      {
        format: EnumComponentFormatType.InputAreaCopy,
        type: EnumComponentStructType.TEXTAREA,
        exampleImages: [],
        value: '',
      },
    ],
    readonly: true,
    title: '复制文案',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '9',
    key: '官方图片',
    list: [
      {
        format: EnumComponentFormatType.UploadImage,
        type: EnumComponentStructType.UPLOAD,
        exampleImages: [],
        originalImage: true,
        rename: true,
        value: '',
      },
    ],
    title: '官方图片',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '11',
    key: '官方图片',
    list: [
      {
        format: EnumComponentFormatType.Image,
        type: EnumComponentStructType.UPLOAD,
        max: 9,
        min: 2,
        fileTypes: ['jpg', 'gif', 'jpeg', 'png'],
        fileMaxSize: 30,
        exampleImages: [],
        originalImage: true,
        rename: true,
        value: '',
      },
    ],
    required: true,
    title: '官方图片',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '12',
    key: '正片上传',
    list: [
      {
        format: EnumComponentFormatType.UploadFile,
        type: EnumComponentStructType.UPLOAD,
        length: 6,
        fileTypes: ['jpg', 'gif', 'jpeg', 'png'],
        fileMaxSize: 30,
        exampleImages: [],
        originalImage: true,
        value: '',
      },
    ],
    required: true,
    title: '正片上传',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '13',
    key: '视频上传',
    list: [
      {
        format: EnumComponentFormatType.UploadFile,
        type: EnumComponentStructType.UPLOAD,
        length: 6,
        fileTypes: ['mp4', 'mov'],
        fileMaxSize: 30,
        exampleImages: [],
        originalImage: true,
        value: '',
      },
    ],
    required: true,
    title: '视频上传',
    uuid: 'uuid',
  },
  {
    description: '',
    id: '14',
    key: '链接上传',
    list: [
      {
        format: EnumComponentFormatType.InputLink,
        placeholder: '请输入链接',
        type: EnumComponentStructType.TEXT,
        exampleImages: [],
      },
    ],
    required: true,
    title: '链接上传',
    uuid: 'uuid',
  },
  {
    uuid: 'uuid',
    title: '文本',
    required: false,
    readonly: false,
    description: '',
    id: '1001',
    list: [
      {
        format: EnumComponentFormatType.InputText, // 必填。可选TEXTAREA、TEXT
        type: EnumComponentStructType.TEXT, // 必填。可选TEXTAREA、TEXT,QQ,MOBILE,NUMBER,EMAIL,IDENTITY,NAME,LINK
        placeholder: '请输入...', // 可为空。占位符
        minLength: 1, // 可为空。字符少长度.length/.size
        maxLength: 16, // 可为空。字符多长度.length/.size
        min: 1, // 可为空。输入内容为NUMBER、QQ、MOBILE时的 最小值
        max: 16, // 可为空。输入内容为NUMBER、QQ、MOBILE时的 最大值
        templates: [], // 可为空，输入值模版
        exampleImages: [], // 可为空,示例图片，存储图片链接，如果为空则为不需要示例图；如果为空数组则为需要但没有示例图
        value: '', // 必填，输入内容
      },
    ],
  },
  {
    uuid: 'uuid',
    title: '单选',
    required: false,
    description: '',
    id: '1002',
    list: [
      {
        format: EnumComponentFormatType.Radio, // 必填。
        type: EnumComponentStructType.RADIO, // 必填。
        length: 2, // 必填。选项数量
        options: [
          {
            key: '1',
            value: '1',
            label: '选项1',
          },
          {
            key: '2',
            value: '2',
            label: '选项2',
          },
        ], // 必填，选项内容
        value: '', // 可为空，选择内容
      },
    ],
  },
  {
    uuid: 'uuid',
    title: '多选',
    required: false,
    description: '',
    id: '1003',
    list: [
      {
        format: EnumComponentFormatType.CheckBox, // 必填。
        type: EnumComponentStructType.CHECKBOX, // 必填。
        length: 2, // 必填。选项数量
        min: 1,
        max: 2,
        options: [
          {
            key: '1',
            value: '1',
            label: '选项1',
          },
          {
            key: '2',
            value: '2',
            label: '选项2',
          },
        ], // 必填，选项内容
        value: '', // 可为空，选择内容
      },
    ],
  },
  {
    uuid: 'uuid',
    title: '文件上传',
    required: false,
    description: '',
    id: '1004',
    list: [
      {
        format: EnumComponentFormatType.UploadFile, // 必填。可选择file和image
        type: EnumComponentStructType.UPLOAD, // 必填。
        fileTypes: [], //   可为空，文件类型限制，为空或者空数组则表示不限制
        exampleImages: [], // 可为空,示例图片，存储图片链接，如果为空则为不需要示例图；如果为空数组则为需要但没有示例图
        fileMaxSize: 30, // 必填。文件最大大小限制，单位为M
        length: 2, // 可为空。要求上传到数量
        min: 1, // 可为空，表示最少需要上传数量限制
        max: 2, // 可为空，表示最多需要上传的数量限制
        originalImage: true, // 是否开启图片原图上传。为true则显示勾选且不能取消
        rename: true, // 是否开启文件重命名。
        value: '', // 必填，给出的文件地址与重命名的数组的json。如[{ label内容
      },
    ],
  },
  {
    uuid: 'uuid',
    title: '日期选择',
    required: false,
    description: '',
    id: '1005',
    list: [
      {
        format: EnumComponentFormatType.DateTime, // 必填。
        type: EnumComponentStructType.DATETIME, // 必填。
        placeholder: '请输入...', // 可为空。占位符
        value: '', // 可为空，选择时间 yyyy-MM-dd HH:mm
      },
    ],
  },
];

export const ComponentTemplateData: ComponentUnifiedAttributeModel[] = [
  /**
   * 单行文本
   */
  {
    type: EnumComponentStructType.TEXT, // 必填。
    format: EnumComponentFormatType.InputText, // 必填。可选TEXT,QQ,MOBILE,NUMBER,EMAIL,IDENTITY,NAME,LINK
    placeholder: '请输入...', // 可为空。占位符
    minLength: 1, // 可为空。字符少长度.length/.size
    maxLength: 16, // 可为空。字符多长度.length/.size
    min: 1, // 可为空。输入内容为NUMBER、QQ、MOBILE时的 最小值
    max: 16, // 可为空。输入内容为NUMBER、QQ、MOBILE时的 最大值
    value: '', // 可为空，输入内容
  },
  /**
   * 多行文本
   */
  {
    type: EnumComponentStructType.TEXTAREA, // 必填。
    format: EnumComponentFormatType.InputArea, // 必填。可选TEXT,QQ,MOBILE,NUMBER,EMAIL,IDENTITY,NAME,LINK
    placeholder: '请输入...', // 可为空。占位符
    minLength: 1, // 可为空。字符少长度.length/.size
    maxLength: 999, // 可为空。字符多长度.length/.size
    templates: [
      {
        key: '转评赞模板',
        label: '转评赞模板',
        value: '单日转发15\n双日转发30\n评论20\n点赞30',
      },
      {
        key: '发布数量模板',
        label: '发布数量模板',
        value: '发布官方照片3张\n现场场照6张\n单日转发15\n双日转发30',
      },
    ], // 可为空，输入值模版
    exampleImages: [], // 可为空,示例图片，存储图片链接，如果为空则为不需要示例图；如果为空数组则为需要但没有示例图
    value: '', // 必填，输入内容
  },
  /**
   * 单选
   */
  {
    type: EnumComponentStructType.RADIO, // 必填。
    format: EnumComponentFormatType.Radio, // 必填。
    length: 2, // 必填。选项数量
    options: [
      {
        key: '1',
        value: '1',
        label: '选项1',
      },
      {
        key: '2',
        value: '2',
        label: '选项2',
      },
    ], // 必填，选项内容
    value: '', // 可为空，选择内容
  },
  /**
   * 单选
   */
  {
    type: EnumComponentStructType.CHECKBOX, // 必填。
    format: EnumComponentFormatType.CheckBox, // 必填。
    length: 2, // 必填。选项数量
    min: 1, // 可为空，表示最少需要选择的数量限制
    max: 2, // 可为空，表示最多需要选择的数量限制
    options: [
      {
        key: '1',
        value: '1',
        label: '选项1',
      },
      {
        key: '2',
        value: '2',
        label: '选项2',
      },
    ], // 必填，选项内容
    value: '', // 可为空，选择内容数组的json
  },
  /**
   * 日期/时间
   */
  {
    type: EnumComponentStructType.DATETIME, // 必填。
    format: EnumComponentFormatType.DateTime, // 必填。
    placeholder: '请输入...', // 可为空。占位符
    value: '', // 可为空，选择时间 yyyy-MM-dd HH:mm
  },
  /**
   * 上传
   */
  {
    type: EnumComponentStructType.UPLOAD, // 必填。可选择：FILE，IMAGE。仅主办上传到图片使用IMAGE，只要涉及到用户上传，使用FILE。
    format: EnumComponentFormatType.UploadFile, // 必填。
    fileTypes: [], //   可为空，文件类型限制，为空或者空数组则表示不限制
    exampleImages: [], // 可为空,示例图片，存储图片链接，如果为空则为不需要示例图；如果为空数组则为需要但没有示例图
    fileMaxSize: 30, // 必填。文件最大大小限制，单位为M
    length: 2, // 可为空。要求上传到数量
    min: 1, // 可为空，表示最少需要上传数量限制
    max: 2, // 可为空，表示最多需要上传的数量限制
    originalImage: true, // 是否开启图片原图上传。为true则显示勾选且不能取消
    rename: true, // 是否开启文件重命名。
    value: '', // 必填，给出的文件地址与重命名的数组的json。如[{ label:"qqq",value:"www.baidu.com" }]
  },
];

export const FileTypeTemplateData: StoreFileTypeStateItemModel[] = [
  {
    key: '图片类',
    title: '图片类',
    children: [
      {
        key: 'png',
        title: 'png',
      },
      {
        key: 'gif',
        title: 'gif',
      },
      {
        key: 'jpg',
        title: 'jpg',
      },
      {
        key: 'jpeg',
        title: 'jpeg',
      },
    ],
  },
  {
    key: '文档类',
    title: '文档类',
    children: [
      {
        key: 'doc',
        title: 'doc',
      },
      {
        key: 'docx',
        title: 'docx',
      },
      {
        key: 'xls',
        title: 'xls',
      },
      {
        key: 'xlsx',
        title: 'xlsx',
      },
      {
        key: 'ppt',
        title: 'ppt',
      },
      {
        key: 'pptx',
        title: 'pptx',
      },
      {
        key: 'pdf',
        title: 'pdf',
      },
    ],
  },
  {
    key: '压缩类',
    title: '压缩类',
    children: [
      {
        key: 'zip',
        title: 'zip',
      },
      {
        key: 'rar',
        title: 'rar',
      },
      {
        key: 'gzip',
        title: 'gzip',
      },
    ],
  },
  {
    key: '视频类',
    title: '视频类',
    children: [
      {
        key: 'mp4',
        title: 'mp4',
      },
      {
        key: 'mov',
        title: 'mov',
      },
    ],
  },
];
