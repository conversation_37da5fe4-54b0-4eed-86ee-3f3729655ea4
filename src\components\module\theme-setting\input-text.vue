<script setup lang="ts">
  import { computed, defineProps, nextTick, PropType } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
  import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
  import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
  import { ValidateUtils } from '@/utils/validate';
  import { KVLModel } from '@/components/module/type/attribute-model-unified';
  import { IOUtils } from '@/utils/io-utils';
  import { Message } from '@arco-design/web-vue';
  import { EnumRoleContentType } from '@/components/module/enum/enum-role-content-type';
  import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
  import UploadFIle from '@/components/upload-file/index.vue';

  const emits = defineEmits(['typeChanged']);

  // 父组件传参
  const props = defineProps({
    /** 模块实体 */
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    /** 当前组，组内参数是否可修改 */
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
    space: {
      type: Object as PropType<EnumRoleContentType>,
      required: true,
      default: () => {
        return EnumRoleContentType.BASE;
      },
    },
  });

  // 计算属性简化访问
  const moduleAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  const formatAttr = computed({
    get: () => moduleAttribute.value?.format,
    set: (value) => {
      props.item?.setComponentFormatType(value);
    },
  });

  const itemCanShow = computed(() => {
    return (
      formatAttr.value === EnumComponentFormatType.InputText ||
      formatAttr.value === EnumComponentFormatType.InputArea
    );
  });

  const placeholderAttr = computed({
    get: () => moduleAttribute.value?.placeholder,
    set: (value) => {
      if (!value) {
        value = '请输入...';
      }
      props.item?.setComponentExpandAttribute('placeholder', value);
    },
  });
  const minLengthAttr = computed({
    get: () => moduleAttribute.value?.minLength,
    set: (value) => {
      if (ValidateUtils.isNullOrEmpty(value)) {
        value = 0;
        props.item?.setComponentExpandAttribute('minLength', -1);
      }
      nextTick(() => {
        props.item?.setComponentMinLength(value);
      });
    },
  });
  const maxLengthAttr = computed({
    get: () => moduleAttribute.value?.maxLength,
    set: (value) => {
      if (ValidateUtils.isNullOrEmpty(value)) {
        value = ActivitySettingLimitValue.activityModuleInputValueMax;
        props.item?.setComponentExpandAttribute('maxLength', -1);
      }
      nextTick(() => {
        props.item?.setComponentMaxLength(value);
      });
    },
  });

  const minAttr = computed({
    get: () => moduleAttribute.value?.min,
    set: (value) => {
      if (ValidateUtils.isNullOrEmpty(value)) {
        value = 0;
        props.item?.setComponentExpandAttribute('min', -1);
      }
      nextTick(() => {
        props.item?.setComponentMin(value);
      });
    },
  });
  const maxAttr = computed({
    get: () => moduleAttribute.value?.max,
    set: (value) => {
      if (ValidateUtils.isNullOrEmpty(value)) {
        value = ActivitySettingLimitValue.activityModuleInputValueMax;
        props.item?.setComponentExpandAttribute('max', -1);
      }
      nextTick(() => {
        props.item?.setComponentMax(value);
      });
    },
  });

  const tempAttrList = computed(() => moduleAttribute.value?.templates);

  const structType = computed(() => props.item?.getComponentStructType());

  const expImageList = computed(() =>
    props.item?.getComponentExampleAttribute()
  );

  const handleCopyTemplate = (item: KVLModel) => {
    IOUtils.CopyToClipboard(item.value)
      .then(() => {
        Message.success('复制成功');
      })
      .catch(() => {
        Message.error('复制失败');
      });
  };

  const handleUpdateUploadFileVM = (
    item: ShowComponentUploadFileItemModel[]
  ) => {
    props.item?.setComponentExampleImages(item);
  };
</script>

<template>
  <div class="form-format-content">
    <a-form layout="vertical">
      <!-- 格式选择 -->
      <a-form-item v-if="formatAttr" label="格式选择">
        <a-select v-model="formatAttr" :disabled="!props.enabled">
          <a-option
            v-for="(
              optionItem, optionIndex
            ) in ServiceEnumModuleFormatType.GetInputTypeOptionList()"
            :key="optionIndex"
            :value="optionItem.itemKey"
            :label="optionItem.itemLabel"
          />
        </a-select>
      </a-form-item>

      <!-- 占位符输入 -->
      <a-form-item v-if="placeholderAttr != undefined" label="提示文案">
        <a-input
          v-model="placeholderAttr"
          :disabled="!props.enabled"
          :max-length="
            ActivitySettingLimitValue.activityModuleInputPlaceholderMaxLength
          "
          show-word-limit
        />
      </a-form-item>
      <!-- 最小长度输入 -->
      <a-form-item
        v-if="minLengthAttr != null && itemCanShow"
        label="最少填入字数"
      >
        <a-input-number
          v-model="minLengthAttr"
          :disabled="!props.enabled"
          :max="maxLengthAttr ?? Infinity"
          :min="1"
          :precision="0"
        />
      </a-form-item>
      <!-- 最大长度输入 -->
      <a-form-item
        v-if="maxLengthAttr != null && itemCanShow"
        label="最大输入字数"
      >
        <a-input-number
          v-model="maxLengthAttr"
          :disabled="!props.enabled"
          :min="minLengthAttr ?? 1"
          :precision="0"
        />
      </a-form-item>
      <!-- 最小值 -->
      <a-form-item
        v-if="
          minAttr != null && formatAttr === EnumComponentFormatType.InputNumber
        "
        :disabled="!props.enabled"
        label="最小输入值"
      >
        <a-input-number
          v-model="minAttr"
          :max="maxAttr ?? Infinity"
          :min="1"
          :precision="0"
        />
      </a-form-item>

      <!-- 最大值 -->
      <a-form-item
        v-if="
          maxAttr != null && formatAttr === EnumComponentFormatType.InputNumber
        "
        :disabled="!props.enabled"
        label="最大输入值"
      >
        <a-input-number v-model="maxAttr" :min="minAttr ?? 1" :precision="0" />
      </a-form-item>

      <a-form-item
        v-if="
          tempAttrList &&
          tempAttrList.length > 0 &&
          structType === EnumComponentStructType.TEXTAREA &&
          props.space === EnumRoleContentType.TASK
        "
        disabled
      >
        <div class="form-items-block">
          <div class="form-item-title">参考模版</div>
          <div
            v-for="(tempItem, tempIndex) in tempAttrList"
            :key="tempIndex"
            class="template-item-block"
          >
            <div
              class="temp-item-content-block"
              style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
              "
            >
              <div class="temp-item-title">{{ tempItem.label }}</div>
              <icon-copy
                class="form-items-copy-icon"
                @click="handleCopyTemplate(tempItem)"
              />
            </div>
            <a-textarea v-model="tempItem.value" disabled auto-size />
          </div>
        </div>
      </a-form-item>
      <a-form-item
        v-if="
          structType &&
          structType === EnumComponentStructType.TEXTAREA &&
          props.space === EnumRoleContentType.TASK &&
          expImageList != null
        "
      >
        <div class="template-block">
          <div class="title" style="margin-bottom: 20px">上传示例图</div>
          <UploadFIle
            v-model:model-value="expImageList"
            :width="80"
            :height="80"
            :rename="true"
            :max-count="
              ActivitySettingLimitValue.activityModuleUploadFileCountMax
            "
            @update:model-value="handleUpdateUploadFileVM"
          />
        </div>
      </a-form-item>
    </a-form>
    <div class="upload-block"> </div>
  </div>
</template>

<style scoped lang="less">
  .form-items-block {
    width: 100%;
    display: flex;
    flex-direction: column;
    .form-item-title {
      margin-left: -15px;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      color: #1d2129;
    }
    .template-item-block {
      margin-top: 15px;
      .temp-item-content-block {
        .form-items-copy-icon {
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
  }
</style>
