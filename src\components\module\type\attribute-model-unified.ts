import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';

export interface LVModel {
  /**
   * value 值
   */
  value: string;
  /**
   * label 显示名称，一般来说label
   */
  label: string;
}

export interface KVModel {
  /**
   *  key 唯一标识
   */
  key: string;
  /**
   * value 值
   */
  value: string;
}

/**
 * 拥有key、value、label的模型
 */
export interface KVLModel extends LVModel, KVModel {
  // /**
  //  *  key 唯一标识
  //  */
  // key: string;
  // /**
  //  * value 值
  //  */
  // value: string;
  // /**
  //  * label 显示名称，一般来说label === key
  //  */
  // label: string;
}

/**
 * 组模块内容组件 - 统一属性模型
 */
export interface ComponentUnifiedAttributeModel {
  /** 模块实例模版类型 */
  type: EnumComponentStructType;
  /** 模块属性类型，格式类型 */
  format: EnumComponentFormatType;

  /** 占位符,应用于所有有输入的组件 */
  placeholder?: string;
  /** 最小限制长度，应用于所有input类型的组件 */
  minLength?: number;
  /** 最大限制长度，应用于所有input类型的组件 */
  maxLength?: number;
  /** 最小限制值，应用于所有有数量控制的组件。如：input数字类型最小值限制、checkbox的最少选择数限制、upload的最小上传限制 */
  min?: number;
  /** 最大限制值，应用于所有有数量控制的组件。如：input数字类型最大值限制、checkbox的最多选择数限制、upload的最大上传限制 */
  max?: number;
  /** 数量值，应用于所有有数量显示的组件。如：radio的可选择项数目，checkbox的可选择数目，upload的上传数量要求 */
  length?: number;
  /** 文件最大大小限制，单位为M */
  fileMaxSize?: number;
  /** 文件类型限制。如果为空则表示不限制 */
  fileTypes?: string[];
  /** 选项-项目。应用于checkbox和radio组件 */
  options?: KVLModel[];
  /** 是否开启图片原图上传。 */
  originalImage?: boolean;
  /** 是否开启文件重命名。 */
  rename?: boolean;

  /** 组件值。应用于任务区域组件中的，Upload组件和group为readonly的Input_Area的值。主要用于承接前端产生的结果值 */
  value?: string;

  /** 模版参考。应用于任务区域组件中。组件group必须为readonly，且format ===  EnumModuleType.Input_Area */
  templates?: KVLModel[];
  /** 示例图片。应用于任务区域组件中，如果为空，则表示没有示例图片。存储图片的oss地址 */
  exampleImages?: string[];
}
