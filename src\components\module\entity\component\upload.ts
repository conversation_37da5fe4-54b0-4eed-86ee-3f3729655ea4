import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { DefineComponent } from 'vue';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import {
  ComponentUnifiedAttributeModel,
  KVLModel,
  LVModel,
} from '@/components/module/type/attribute-model-unified';
import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
import { ServiceEnumModuleStructType } from '@/components/module/service/service-enum-module-struct-type';
import { ConvertUtils } from '@/utils/convert';
import { ValidateUtils } from '@/utils/validate';
import useComponentStore from '@/components/module/store/component';
import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';
import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
import { UploadUtils } from '@/components/module/tools/upload-utils';
import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

export class CustomUploadComponent implements ICustomComponent {
  /**  模块标题 */
  private readonly _moduleName: string = '输入框';

  /**  模块结构类型 */
  private _structType: EnumComponentStructType = EnumComponentStructType.UPLOAD;

  /**  模块格式类型 */
  private _formatType: EnumComponentFormatType =
    EnumComponentFormatType.UploadFile;

  /**  模块显示模版实例 */
  private readonly _showModuleRef: DefineComponent | undefined;

  /**  模块设置模版实例 */
  private readonly _settingModuleRef: DefineComponent | undefined;

  /**  模块属性 */
  private readonly _moduleExpandAttribute: ComponentUnifiedAttributeModel;

  /** 构造函数 */
  constructor(attribute: ComponentUnifiedAttributeModel) {
    // 判断结构是否正确
    if (attribute.type !== EnumComponentStructType.UPLOAD) {
      throw new Error('组件结构类型错误,应使用UPLOAD类型');
    }
    this._structType = attribute.type;

    // 判断格式是否正确
    if (!ServiceEnumModuleFormatType.ValidateUploadType(attribute.format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = attribute.format;

    // 获取显示组件实例
    this._showModuleRef = ServiceEnumModuleStructType.GetShowModuleComponent(
      this._structType
    );
    // 获取设置组件实例
    this._settingModuleRef =
      ServiceEnumModuleStructType.GetSettingModuleComponent(this._structType);

    // 赋值属性
    this._moduleExpandAttribute = ConvertUtils.DeepCopy(attribute);

    // 校验属性
    this.validateParams();
  }

  /**
   * 验证参数
   */
  private validateParams = (): void => {
    if (this._moduleExpandAttribute.max) {
      const dMinCount = this._moduleExpandAttribute.min ?? 0;
      if (this._moduleExpandAttribute.max < dMinCount) {
        this._moduleExpandAttribute.min = dMinCount;
      }
    }
    if (
      ValidateUtils.isValuePositiveNumber(this._moduleExpandAttribute.length)
    ) {
      this._moduleExpandAttribute.max = this._moduleExpandAttribute.length;
      this._moduleExpandAttribute.min = this._moduleExpandAttribute.length;
    }
    console.log('validateParams');

    this._moduleExpandAttribute.value = JSON.stringify(
      ValidateUtils.adjustListTail<LVModel>(
        this._moduleExpandAttribute.value,
        this._moduleExpandAttribute.max ?? this._moduleExpandAttribute.min ?? 0,
        () => ({
          value: '',
          label: '',
        }),
        [ValidateUtils.isVLModel]
      )
    );
  };

  public getComponentStructType(): EnumComponentStructType {
    return this._structType;
  }

  /**  获取模块名称 */
  public getComponentName(): string {
    return this._moduleName;
  }

  /**  获取模块类型 */
  public getComponentType(): EnumComponentFormatType {
    return this._formatType;
  }

  /**  获取模块显示页面实例 */
  public getShowModuleRef(): DefineComponent | undefined {
    return this._showModuleRef;
  }

  /**  获取模块设置页面实例 */
  public getSettingModuleRef(): DefineComponent | undefined {
    return this._settingModuleRef;
  }

  /**  获取模块属性 */
  public getComponentUnifiedAttribute(): ComponentUnifiedAttributeModel {
    return this._moduleExpandAttribute;
  }

  public getComponentValueAttribute(): ShowComponentUploadFileItemModel[] {
    return ValidateUtils.validateAndMapArray<
      LVModel,
      ShowComponentUploadFileItemModel
    >(
      ConvertUtils.convertJson2Array(this._moduleExpandAttribute.value),
      [ValidateUtils.isVLModel],
      undefined,
      UploadUtils.convertLVModelToShowComponentUploadFileItem
    );
  }

  /**
   * 获取输入模块属性 -> 单属性
   * @param key
   * @param value
   */
  public setComponentExpandAttribute(key: string, value: any) {
    // 验证属性是否存在
    if (!(key in this._moduleExpandAttribute)) {
      throw new Error(`模块属性 ${key} 不存在`);
    }
    // 验证属性类型

    const typeKey = typeof (this._moduleExpandAttribute as any)[key];

    if (
      typeKey !== 'undefined' &&
      typeKey !== 'number' &&
      typeof value !== typeKey
    ) {
      throw new TypeError(`模块属性 ${key} 的类型与值不匹配`);
    }
    // 设置属性值
    (this._moduleExpandAttribute as any)[key] = value
      ? ConvertUtils.DeepCopy(value)
      : value;
  }

  /**
   * 设置模块格式类型
   * @param format
   */
  public async setComponentFormatType(format: EnumComponentFormatType) {
    if (!ServiceEnumModuleFormatType.ValidateUploadType(format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = format;
    this._moduleExpandAttribute.format = format;

    if (format === EnumComponentFormatType.Image) {
      this._moduleExpandAttribute.length = undefined;
      this._moduleExpandAttribute.min = undefined;
      this._moduleExpandAttribute.max = undefined;
      this._moduleExpandAttribute.fileTypes = undefined;
    } else if (format === EnumComponentFormatType.UploadFile) {
      const store = useComponentStore();
      await store
        .getMetaComponent(this._structType)
        .then((res: ComponentUnifiedAttributeModel | undefined) => {
          Object.assign(this._moduleExpandAttribute, res);
          this.validateParams();
        });
    }
  }

  private verifyFormatType(): string[] {
    return this._formatType === EnumComponentFormatType.Image ||
      this._formatType === EnumComponentFormatType.UploadFile
      ? []
      : ['format'];
  }

  /**
   * 获取示例图片
   * @param exampleImages
   */
  public getComponentExampleAttribute(): ShowComponentUploadFileItemModel[] {
    return ValidateUtils.validateAndMapArray<
      string,
      ShowComponentUploadFileItemModel
    >(
      this._moduleExpandAttribute.exampleImages,
      [ValidateUtils.isString],
      undefined,
      UploadUtils.convertStringToShowComponentUploadFileItem
    );
  }

  /**
   * 设置示例图片
   * @param exampleImages
   */
  public setComponentExampleImages(exampleImages: unknown): void {
    this._moduleExpandAttribute.exampleImages =
      ValidateUtils.validateAndMapArray<
        ShowComponentUploadFileItemModel,
        string
      >(
        exampleImages,
        [UploadUtils.isShowComponentUploadFileItemModel],
        [UploadUtils.canSavedConditionWithShowComponentUploadFileItem],
        UploadUtils.convertShowComponentUploadFileItemToString
      );
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyExampleImages(): string[] {
    return [];
  }

  /**
   * 获取文件最大大小限制
   * @param fileMaxSize
   */
  public setComponentFileMaxSize(fileMaxSize: number | undefined): void {
    this._moduleExpandAttribute.fileMaxSize =
      ValidateUtils.isValuePositiveNumber(fileMaxSize) ? fileMaxSize : 0;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileMaxSize(): string[] {
    return [];
  }

  /**
   * 获取文件类型限制
   * @param fileTypes
   */
  public setComponentFileTypes(fileTypes: string[] | undefined): void {
    this._moduleExpandAttribute.fileTypes = ValidateUtils.isNullOrEmpty(
      fileTypes
    )
      ? []
      : fileTypes;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileTypes(): string[] {
    return [];
  }

  /**
   * 获取数量统一限制
   * @param length
   */
  public setComponentLength(length: number | undefined): void {
    this._moduleExpandAttribute.length = length;
    if (ValidateUtils.isValuePositiveNumber(length)) {
      // this._moduleExpandAttribute.max = length;
      // this._moduleExpandAttribute.min = length;

      this._moduleExpandAttribute.value = JSON.stringify(
        ValidateUtils.adjustListTail<LVModel>(
          this._moduleExpandAttribute.value,
          length ?? 0,
          () => ({
            value: '',
            label: '',
          }),
          [ValidateUtils.isVLModel]
        )
      );
      this.setComponentMax(length);
      this.setComponentMin(length);
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyLength(): string[] {
    return [];
  }

  /**
   * 获取数量最大限制
   * @param max
   */
  public setComponentMax(max: number | undefined): void {
    // 判断是否有统一限制
    if (
      ValidateUtils.isValuePositiveNumber(this._moduleExpandAttribute.length)
    ) {
      this._moduleExpandAttribute.max = this._moduleExpandAttribute.length;
      return;
    }
    if (!ValidateUtils.isValuePositiveNumber(max)) {
      max = ActivitySettingLimitValue.activityModuleUploadFileCountMax;
    }
    const { min } = this._moduleExpandAttribute;
    this._moduleExpandAttribute.max = Math.max(
      min ?? 0,
      max ?? ActivitySettingLimitValue.activityModuleUploadFileCountMax
    );

    // this._moduleExpandAttribute.max = Math.min(
    //   Math.max(min ?? 0, max ?? 0),
    //   ActivitySettingLimitValue.activityModuleUploadFileCountMax
    // );

    this._moduleExpandAttribute.value = JSON.stringify(
      ValidateUtils.adjustListTail<LVModel>(
        this._moduleExpandAttribute.value,
        this._moduleExpandAttribute.max ??
          min ??
          ActivitySettingLimitValue.activityModuleUploadFileCountMax,
        () => ({
          value: '',
          label: '',
        }),
        [ValidateUtils.isVLModel]
      )
    );
  }

  private verifyMax(): string[] {
    const { length, max } = this._moduleExpandAttribute;

    // 如果 length 是正数，判断是否等于 max
    if (ValidateUtils.isValuePositiveNumber(length)) {
      return max != null && length === max ? [] : ['max'];
    }

    // 否则验证 max 是否大于等于 min（若存在）
    const min = this._moduleExpandAttribute.min ?? 0;
    return max == null || max >= min ? [] : ['max'];
  }

  /**
   * 设置组件最大长度
   * @param maxLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMaxLength(maxLength: number | undefined): void {
    throw new Error('该类型组件没有【最大长度】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMaxLength(): string[] {
    return [];
  }

  public setComponentMin(min: number | undefined): void {
    // 判断是否有统一限制
    if (
      ValidateUtils.isValuePositiveNumber(this._moduleExpandAttribute.length)
    ) {
      this._moduleExpandAttribute.min = this._moduleExpandAttribute.length;
      return;
    }

    if (!ValidateUtils.isValuePositiveNumber(min)) {
      min = 0;
    }
    this._moduleExpandAttribute.min = min;
    const { max } = this._moduleExpandAttribute;
    // 判断是否限制最小值
    if (ValidateUtils.isValuePositiveNumber(max)) {
      this._moduleExpandAttribute.min = Math.min(min ?? 0, max ?? 0);
    }
    this._moduleExpandAttribute.value = JSON.stringify(
      ValidateUtils.adjustListTail<LVModel>(
        this._moduleExpandAttribute.value,
        max ?? min ?? 0,
        () => ({
          value: '',
          label: '',
        }),
        [ValidateUtils.isVLModel]
      )
    );
  }

  private verifyMin(): string[] {
    const { length, min } = this._moduleExpandAttribute;

    // 如果 length 是正数，判断是否等于 max
    if (ValidateUtils.isValuePositiveNumber(length)) {
      return min != null && length === min ? [] : ['min'];
    }

    // 否则验证 max 是否大于等于 min（若存在）
    const max = this._moduleExpandAttribute.max ?? 0;
    return min == null || max >= min ? [] : ['min'];
  }

  /**
   * 设置组件最小长度
   * @param minLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMinLength(minLength: number | undefined): void {
    throw new Error('该类型组件没有【最小长度】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMinLength(): string[] {
    return [];
  }

  /**
   * 设置组件选项
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentOptions(options: KVLModel[] | undefined): void {
    throw new Error('该类型组件没有【组件选项】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOptions(): string[] {
    return [];
  }

  /**
   * 设置组件是否开启图片原图上传
   * @param originalImage
   */
  public setComponentOriginalImage(originalImage: boolean | undefined): void {
    this._moduleExpandAttribute.originalImage = originalImage;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOriginalImage(): string[] {
    return [];
  }

  /**
   * 设置组件的placeholder
   * @param placeholder
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentPlaceholder(placeholder: string | undefined): void {
    throw new Error('该类型组件没有【placeholder】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyPlaceholder(): string[] {
    return [];
  }

  /**
   * 设置组件的图片是否可重命名
   * @param rename
   */
  public setComponentRename(rename: boolean | undefined): void {
    this._moduleExpandAttribute.rename = rename;
    console.log('rename');
    if (!rename) {
      this._moduleExpandAttribute.value = JSON.stringify(
        ValidateUtils.validateAndMapArray<LVModel, LVModel>(
          ConvertUtils.convertJson2Array(this._moduleExpandAttribute.value),
          [ValidateUtils.isVLModel],
          undefined,
          UploadUtils.uploadValueLabelClean
        )
      );
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyRename(): string[] {
    return [];
  }

  /**
   * 设置组件的模版
   * @param templates
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentTemplates(templates: KVLModel[] | undefined): void {
    throw new Error('该类型组件没有【模版】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyTemplates(): string[] {
    return [];
  }

  /**
   * 设置组件的值
   * @param value
   */
  public setComponentValue(value: unknown): void {
    // 将 ShowComponentUploadFileItemModel类型数组的值中筛选状态为Success和Empty的项目，并缓存
    const valueConvert: LVModel[] = ValidateUtils.validateAndMapArray<
      ShowComponentUploadFileItemModel,
      LVModel
    >(
      value,
      [UploadUtils.isShowComponentUploadFileItemModel],
      [UploadUtils.canSavedConditionWithShowComponentUploadFileItem],
      UploadUtils.convertShowComponentUploadFileItemToLVModel
    );
    this._moduleExpandAttribute.value = JSON.stringify(valueConvert);
  }

  /**
   * 校验组件的值的有效性
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  private verifyValue(): string[] {
    // 解析缓存的value值是否为JSON数组，并校验值是否有空值
    return ValidateUtils.verifyArrayJson<LVModel>(
      this._moduleExpandAttribute.value,
      UploadUtils.uploadValueAttrValidator
    )
      ? []
      : ['value'];
  }

  /**
   * 清理组件值属性的值，将无效数据清理出去
   * @private
   */
  private cleanValue(): void {
    // 解析缓存的value值是否为JSON数组,去除无效数据
    this._moduleExpandAttribute.value = JSON.stringify(
      ValidateUtils.validateAndMapArray<LVModel, LVModel>(
        ConvertUtils.convertJson2Array(this._moduleExpandAttribute.value),
        [ValidateUtils.isVLModel],
        [UploadUtils.uploadValueAttrValidator]
      )
    );
  }

  public verifyComponentAttribute(): string[] {
    const attr = this._moduleExpandAttribute;

    if (!attr) {
      console.warn('Module expand attribute is not defined.');
      return ['attr'];
    }

    return [
      ...this.verifyFormatType(),
      ...this.verifyPlaceholder(),
      ...this.verifyMaxLength(),
      ...this.verifyMinLength(),
      ...this.verifyLength(),
      ...this.verifyMax(),
      ...this.verifyMin(),
      ...this.verifyFileMaxSize(),
      ...this.verifyFileTypes(),
      ...this.verifyOptions(),
      ...this.verifyOriginalImage(),
      ...this.verifyRename(),
      ...this.verifyValue(),
      ...this.verifyTemplates(),
      ...this.verifyExampleImages(),
    ];
  }
}
