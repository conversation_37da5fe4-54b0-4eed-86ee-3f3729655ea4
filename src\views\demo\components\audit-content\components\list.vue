<template>
  <a-modal
    v-model:visible="visible"
    :hide-title="true"
    width="842px"
    :footer="false"
    class="registration-records-modal"
  >
    <div class="tw-flex tw-flex-col tw-gap-4">
      <!-- 表格头部操作区 -->
      <div class="tw-flex tw-justify-between tw-items-center">
        <div class="tw-text-lg tw-font-semibold tw-text-[#1d2129]">
          报名记录汇总
        </div>
        <a-button type="primary" class="tw-flex tw-items-center tw-gap-2">
          <icon-question-circle />
          加入我的黑名单
        </a-button>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="pagination"
        row-key="id"
        class="registration-table"
      >
        <!-- 通过状态列 -->
        <template #passStatus="{ record }">
          <a-tag v-if="record.passStatus === '通过'" color="green">
            通过
          </a-tag>
          <a-tag v-else-if="record.passStatus === '未通过'" color="gray">
            未通过
          </a-tag>
          <span v-else class="tw-text-[#86909c]">{{ record.passStatus }}</span>
        </template>

        <!-- 到场情况列 -->
        <template #attendanceStatus="{ record }">
          <a-tag v-if="record.attendanceStatus === '请假'" color="blue">
            请假
          </a-tag>
          <a-tag v-else-if="record.attendanceStatus === '已核销'" color="green">
            已核销
          </a-tag>
          <a-tag v-else-if="record.attendanceStatus === '被作废'" color="red">
            被作废
          </a-tag>
          <a-tag v-else-if="record.attendanceStatus === '未到场'" color="gray">
            未到场
          </a-tag>
          <a-tag v-else-if="record.attendanceStatus === '未通过'" color="gray">
            未通过
          </a-tag>
          <span v-else class="tw-text-[#86909c]">{{
            record.attendanceStatus
          }}</span>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import { IconUserAdd } from '@arco-design/web-vue/es/icon';
  import type { TableColumnData } from '@arco-design/web-vue';

  // 弹窗显示状态
  const visible = ref(false);

  // 表格列配置
  const columns: TableColumnData[] = [
    {
      title: '序号',
      dataIndex: 'id',
      width: 60,
      align: 'center',
    },
    {
      title: '报名CN',
      dataIndex: 'username',
      width: 120,
    },
    {
      title: '报名日期',
      dataIndex: 'registrationDate',
      width: 140,
    },
    {
      title: '活动名称',
      dataIndex: 'activityName',
      width: 120,
    },
    {
      title: '通过状态',
      dataIndex: 'passStatus',
      slotName: 'passStatus',
      width: 80,
      align: 'start',
    },
    {
      title: '到场情况',
      dataIndex: 'attendanceStatus',
      slotName: 'attendanceStatus',
      width: 80,
      align: 'start',
    },
    {
      title: '漫展名称',
      dataIndex: 'eventName',
      width: 200,
    },
  ];

  // 表格数据
  const tableData = ref([
    {
      id: 1,
      username: 'PPK',
      registrationDate: '2025年7月13日',
      activityName: '自由行报名',
      passStatus: '通过',
      attendanceStatus: '请假',
      eventName: '常州·新次元微光漫展',
    },
    {
      id: 2,
      username: '霸道',
      registrationDate: '2025年7月13日',
      activityName: '自由行报名',
      passStatus: '通过',
      attendanceStatus: '已核销',
      eventName: '常州·新****漫展',
    },
    {
      id: 3,
      username: '糖豆豆',
      registrationDate: '2025年7月13日',
      activityName: '自由行报名',
      passStatus: '通过',
      attendanceStatus: '被作废',
      eventName: 'Arco Design',
    },
    {
      id: 4,
      username: 'Arco Design',
      registrationDate: '2025年7月13日',
      activityName: '自由行报名',
      passStatus: '通过',
      attendanceStatus: '未到场',
      eventName: 'Arco Design',
    },
  ]);

  // 分页配置
  const pagination = reactive({
    current: 1,
    pageSize: 10,
    total: 50,
    showTotal: true,
    pageSizeOptions: [10, 20, 50],
  });

  // 查看详情
  const viewDetails = (record: any) => {
    console.log('查看详情:', record);
  };

  // 暴露方法供外部调用
  const openModal = () => {
    visible.value = true;
  };

  const closeModal = () => {
    visible.value = false;
  };

  defineExpose({
    openModal,
    closeModal,
  });
</script>

<script lang="ts">
  export default {
    name: 'LookList',
  };
</script>

<style lang="less" scoped>
  :deep(.registration-records-modal) {
    .arco-modal-header {
      border-bottom: 1px solid #e5e6eb;
      padding: 16px 24px;
    }

    .arco-modal-body {
      padding: 24px;
    }

    .arco-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d2129;
    }
  }

  :deep(.registration-table) {
    .arco-table-th {
      background-color: #f7f8fa;
      color: #4e5969;
      font-weight: 500;
      border-bottom: 1px solid #e5e6eb;
    }

    .arco-table-td {
      border-bottom: 1px solid #f2f3f5;
    }

    .arco-table-tr:hover {
      background-color: #f7f8fa;
    }

    // 状态标签样式
    .arco-tag {
      border: none;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 2px;

      &.arco-tag-green {
        background-color: #f6ffed;
        color: #52c41a;
      }

      &.arco-tag-blue {
        background-color: #e6f7ff;
        color: #1890ff;
      }

      &.arco-tag-red {
        background-color: #fff2f0;
        color: #ff4d4f;
      }

      &.arco-tag-gray {
        background-color: #f5f5f5;
        color: #8c8c8c;
      }
    }

    // 分页器样式
    .arco-pagination {
      margin-top: 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .arco-pagination-total {
        margin-right: auto;
      }
    }
  }
</style>
