export default {
  'menu.list.grant': 'Distribution of invitation tickets',
  'searchTable.form.name': 'Product Name',
  'searchTable.form.name.placeholder': 'Please enter the name of the product.',
  'searchTable.form.coupons': 'Coupons Name',
  'searchTable.form.coupons.placeholder':
    'Please enter the name of the coupons.',
  'searchTable.form.startTime': 'Time Of Release',
  'searchTable.form.number': 'Set Number',
  'searchTable.form.number.placeholder': 'Please enter Set Number',
  'searchTable.form.contentType': 'Content Type',
  'searchTable.form.contentType.img': 'image-text',
  'searchTable.form.contentType.horizontalVideo': 'Horizontal short video',
  'searchTable.form.contentType.verticalVideo': 'Vertical short video',
  'searchTable.form.filterType': 'Filter Type',
  'searchTable.form.filterType.artificial': 'artificial',
  'searchTable.form.filterType.rules': 'Rules',
  'searchTable.form.createdTime': 'Create Date',
  'searchTable.form.status': 'Status',
  'searchTable.form.status.online': 'Online',
  'searchTable.form.status.offline': 'Offline',
  'searchTable.form.search': 'Search',
  'searchTable.form.reset': 'Reset',
  'searchTable.form.selectDefault': 'All',
  'searchTable.operation.create': 'Create',
  'searchTable.operation.import': 'Import',
  'searchTable.operation.download': 'Download',
  // columns
  'searchTable.columns.index': '#',
  'searchTable.columns.number': 'Coupon Code',
  'searchTable.columns.status': 'Order Status',
  'searchTable.columns.userCN': 'User CN',
  'searchTable.columns.contact': 'Contact',
  'searchTable.columns.loginNumber': 'Login Phone Number',
  'searchTable.columns.commodityName': 'Product Name',
  'searchTable.columns.couponsName': 'Coupon Name',
  'searchTable.columns.sessionDate': 'Session Date',
  'searchTable.columns.releaseTime': 'Release Time',
  'searchTable.columns.reason': 'Invalidation Reason',
  'searchTable.columns.operator': 'Operator',
  'searchTable.columns.operation': 'Operation',
  'searchTable.columns.name': 'Set Name',
  'searchTable.columns.contentType': 'Content Type',
  'searchTable.columns.filterType': 'Filter Type',
  'searchTable.columns.count': 'Count',
  'searchTable.columns.createdTime': 'CreatedTime',
  'searchTable.columns.operations': 'Operations',
  'searchTable.columns.operations.view': 'View',
  // size
  'searchTable.size.mini': 'mini',
  'searchTable.size.small': 'small',
  'searchTable.size.medium': 'middle',
  'searchTable.size.large': 'large',
  // actions
  'searchTable.actions.refresh': 'refresh',
  'searchTable.actions.density': 'density',
  'searchTable.actions.columnSetting': 'columnSetting',
};
