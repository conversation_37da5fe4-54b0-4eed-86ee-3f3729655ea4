import axios from 'axios';
import type { AxiosRequestConfig, AxiosResponse } from 'axios';
import { Message, Modal } from '@arco-design/web-vue';
import { useUserStore } from '@/store';
import { clearMerchant, clearToken, getToken } from '@/utils/auth';
import qs from 'query-string';

export interface HttpResponse<T = unknown> {
  status: number;
  msg: string;
  code: number;
  data: T;
}

if (import.meta.env.VITE_API_BASE_URL) {
  axios.defaults.baseURL = import.meta.env.VITE_API_BASE_URL;
}

axios.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    if (!config.headers) {
      config.headers = {};
    }
    config.headers.device = 'web';
    const token = getToken();
    if (token) {
      config.headers.Authorization = `${token}`;

      // 追加时间戳，防止GET请求缓存
      if (config.method?.toUpperCase() === 'GET') {
        config.params = { ...config.params, t: new Date().getTime() };
      }
      if (
        Object.values(config.headers).includes(
          'application/x-www-form-urlencoded'
        )
      ) {
        config.data = qs.stringify(config.data);
      }
    }
    return config;
  },
  (error) => {
    // do something
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  async (response: AxiosResponse<any>) => {
    const res = response.data;
    if (Object.prototype.toString.call(res) === '[object Blob]') {
      return response;
    }

    // 响应成功
    if (res.code === 'OK' || response.status === 200) {
      return res;
    }
    Message.error(res.message);

    return Promise.reject(new Error(res.msg || 'Error'));
  },
  (error) => {
    const { response } = error;
    const res = error.response.data;
    if (response.status === 401) {
      handleAuthorized();
    } else {
      Message.error(res.message);
      return Promise.reject(error);
    }
  }
);

/**
 * '登录超时，请重新登录', '提示',
 */
const handleAuthorized = () => {
  Modal.warning({
    title: '提示',
    content: '登录超时，请重新登录',
    hideCancel: true,
    closable: false,
    okText: '重新登录',
    onOk() {
      useUserStore().resetInfo();
      clearToken();
      clearMerchant();
      location.reload();
      return Promise.reject('登录超时，请重新登录');
    },
  });
};
