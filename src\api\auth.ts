import axios from "axios";
import {Request_MobileCodeLoginParamsModel, Response_MobileCodeLoginModel} from "@/types/api-type/auth";

export const LoginAPI = {
    /**
     * 获取验证码
     * @param mobile
     * @constructor
     */
    GetSMSVerificationCode : (mobile:string) => {
        return axios.post(`/api/pd/auth/send/code/${mobile}`)
    },
    /**
     * 使用【手机-验证码】登录
     * @param data
     * @constructor
     */
    UseMobileLogin:(data: Request_MobileCodeLoginParamsModel) => {
        return axios.post<Response_MobileCodeLoginModel>('/api/pd/auth/mobile/login', data)
    },
    /**
     * 选择商户
     */
    SelectMerchantId :(merchantId: string) => {
        return axios.get(`/api/pd/merchant/check/merchant/${merchantId}`)
    }
}