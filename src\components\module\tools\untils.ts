// import {
//   ShowComponentUploadFileItemModel,
//   UploadFileInfosModel,
// } from '@/components/module/type/show-component-attribute-model';
// import { ValidateUtils } from '@/utils/validate';
// import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';
//
// /**
//  * 检查对象是否具有合法的 label 字段
//  *
//  * label 可选，如果存在必须是字符串类型
//  *
//  * @param item 被检查的对象
//  * @returns 如果 label 符合要求，返回 true；否则返回 false
//  */
// const hasValidLabel = (item: any): boolean => {
//   if (!('label' in item)) return false;
//   const { label } = item;
//   return label === undefined || typeof label === 'string';
// };
//
// const hasValidValue = (item: any): boolean => {
//   if (!('value' in item)) return false;
//   const { value } = item;
//   return typeof value === 'string';
// };
//
// /**
//  * 检查对象的 progress 字段是否符合指定结构
//  *
//  * progress 必须是一个对象，并且包含 status（字符串）和 percent（数字）
//  *
//  * @param item 要检查的 progress 对象
//  * @returns 如果结构合法，返回 true；否则返回 false
//  */
// const getValidValue = (item: any): UploadFileInfosModel | undefined => {
//   if ('value' in item) {
//     return ValidateUtils.isJsonObject<UploadFileInfosModel>(
//       item.value,
//       // eslint-disable-next-line no-use-before-define
//       ModuleComponentTools.isUploadFileInfosModel
//     );
//   }
//   return undefined;
// };
//
// export class ModuleComponentTools {
//   /**
//    * 类型守卫函数，用于判断一个对象是否满足 UploadFileInfosModel 接口定义
//    * @param item
//    */
//   public static isUploadFileInfosModel(
//     item: any
//   ): item is UploadFileInfosModel {
//     return (
//       'url' in item &&
//       typeof item.url === 'string' &&
//       'status' in item &&
//       typeof item.status === 'string' &&
//       'percent' in item &&
//       typeof item.percent === 'number'
//     );
//   }
//
//   /**
//    * 类型守卫函数，用于判断一个对象是否满足 ShowComponentUploadFileItemModel 接口定义
//    *
//    * @param item 要验证的对象
//    * @returns 如果 item 是合法的 ShowComponentUploadFileItemModel 类型，返回 true；否则返回 false
//    */
//   public static isShowComponentUploadFileItemModel(
//     item: any
//   ): item is ShowComponentUploadFileItemModel {
//     return (
//       ValidateUtils.isObject(item) &&
//       hasValidLabel(item) &&
//       hasValidValue(item)
//     );
//   }
//
//   /**
//    * 过滤并映射数组中的元素
//    *
//    * 1. 首先判断输入是否为数组；
//    * 2. 然后对每个元素进行两次校验（validator 和 validatorStatus）；
//    * 3. 最后使用 mapper 函数将符合条件的元素转换为新类型；
//    * 4. 返回由所有转换后的元素组成的新数组。
//    *
//    * @param value 要处理的原始数组或任意类型
//    * @param validator 类型守卫函数，用于判断元素是否为目标类型 T
//    * @param validatorStatus 状态校验函数，用于进一步筛选目标类型中的有效项
//    * @param mapper 映射函数，将符合条件的项转换为 R 类型
//    * @returns 返回由转换后的元素组成的数组，若输入不是数组则返回空数组
//    */
//   public static filterAndMapArray<T, R>(
//     value: unknown,
//     validator: (item: unknown) => item is T,
//     validatorStatus: (item: T) => boolean,
//     mapper: (item: T) => R
//   ): R[] {
//     // 如果输入不是数组，直接返回空数组
//     if (!Array.isArray(value)) {
//       return [];
//     }
//
//     // 使用 reduce 方法一次性完成过滤和映射操作
//     return value.reduce((acc: R[], item: unknown) => {
//       // 先通过类型校验和状态校验
//       if (validator(item) && validatorStatus(item)) {
//         // 校验通过后执行映射，并将结果加入结果数组
//         acc.push(mapper(item));
//       }
//       // 返回累计的结果数组
//       return acc;
//     }, []);
//   }
//
//   /**
//    * 校验数组或可转换为数组的JSON
//    * 判断传入的 value 是否可解析为 T 类型的数组。
//    * 如果是，则遍历数组中的每一项：
//    * - 如果有任何一项满足校验条件，则返回 false；
//    * - 否则返回 true。
//    *
//    * @param value 要处理的数据（可以是 JSON 字符串、数组等）
//    * @param validator 类型守卫函数，用于判断数组中的每一项是否为类型 T
//    * @param predicate 校验函数，用于判断 T 类型的对象是否满足特定条件
//    * @returns 如果数组中没有任何元素满足 predicate 条件，返回 true；否则返回 false
//    */
//   public static verifyArrayJSON<T>(
//     value: unknown,
//     validator: (item: unknown) => item is T,
//     predicate: (item: T) => boolean
//   ): boolean {
//     let array: T[];
//
//     // 如果已经是数组，尝试过滤出满足条件的项
//     if (Array.isArray(value)) {
//       array = value;
//     } else if (typeof value === 'string') {
//       // 如果是字符串，尝试解析为数组
//       try {
//         const parsed = JSON.parse(value);
//         if (!Array.isArray(parsed)) {
//           return false; // 不是数组，直接返回 false
//         }
//         array = parsed;
//       } catch (e) {
//         console.error('JSON 解析失败:', e);
//         return false; // 解析失败，直接返回 false
//       }
//     } else {
//       return false; // 非数组也非字符串，直接返回 false
//     }
//
//     // 遍历数组，检查是否有任意一个元素满足 predicate 条件
//     return !array.some((item) => validator(item) && predicate(item));
//   }
//
//   public static verifyJsonUploadFileInfosModel(item: any): boolean {
//     if ('value' in item) {
//       const jsonObject = ValidateUtils.isJsonObject<UploadFileInfosModel>(
//         item,
//         ModuleComponentTools.isUploadFileInfosModel
//       );
//       return !!jsonObject && jsonObject.status === EnumFileUploadStatue.Success;
//     }
//     return false;
//   }
//
//   /**
//    * 尝试将传入的 JSON 字符串解析为对象，并判断其是否是一个数组。
//    * 如果是，则筛选出符合指定类型的元素组成新数组并返回。
//    *
//    * @param value 要解析的 JSON 字符串
//    * @param validator 类型守卫函数，用于判断数组中的每一项是否为类型 T
//    * @param validatorStatus 可选的状态校验函数，用于进一步过滤符合条件的项
//    * @returns 返回由满足条件的项组成的数组，若解析失败或非数组则返回空数组
//    */
//   public static parseAndFilterJsonArray<T>(
//     value: unknown,
//     validator: (item: any) => item is T,
//     validatorStatus?: (item: T) => boolean
//   ): T[] {
//     // 确保传入的是字符串
//     if (typeof value !== 'string') {
//       return [];
//     }
//
//     let parsedData: any;
//     try {
//       parsedData = JSON.parse(value);
//     } catch (error) {
//       console.error('JSON 解析失败:', error);
//       return [];
//     }
//
//     // 确保解析结果是数组
//     if (!Array.isArray(parsedData)) {
//       return [];
//     }
//
//     // 过滤出满足类型和状态条件的项
//     return parsedData.filter((item): item is T => {
//       return (
//         validator(item) && (validatorStatus ? validatorStatus(item) : true)
//       );
//     });
//   }
//
//   /**
//    * 调整数组尾部：正数添加，负数删除
//    * @param value JSON格式数组
//    * @param finalCount 数组最后数量
//    * @param defaultValue 默认值生成函数
//    */
//   public static adjustListTail(
//     value: unknown,
//     finalCount: number,
//     defaultValue: () => any
//   ): string {
//     // 确保传入的是字符串
//     if (typeof value !== 'string') {
//       return '';
//     }
//
//     let parsedData: any;
//     try {
//       if (value.trim().length === 0) {
//         parsedData = [];
//       } else {
//         parsedData = JSON.parse(value);
//       }
//     } catch (error) {
//       console.error('JSON 解析失败:', error);
//       return value;
//     }
//
//     // 确保解析结果是数组
//     if (!Array.isArray(parsedData)) {
//       return value;
//     }
//
//     const dev = finalCount - parsedData.length;
//
//     if (dev > 0) {
//       for (let i = 0; i < dev; i += 1) {
//         parsedData.push(defaultValue());
//       }
//     } else if (dev < 0) {
//       // JS 引擎会自动处理超出范围的情况，只删除存在的元素，不会出错。
//       parsedData.splice(parsedData.length + dev, -dev);
//     }
//     return JSON.stringify(parsedData);
//   }
// }
