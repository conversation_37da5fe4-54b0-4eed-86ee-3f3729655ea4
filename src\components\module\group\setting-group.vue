<script setup lang="ts">
  import { computed, defineProps, PropType } from 'vue';
  import { CustomGroupModule } from '@/components/module/entity/struct/group';
  import { ValidateUtils } from '@/utils/validate';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

  // 父组件传参
  const props = defineProps({
    groupEntity: {
      type: Object as PropType<CustomGroupModule>,
      required: true,
    },
  });

  const entityExistFlag = computed(
    () => props.groupEntity?.getExistFlag() ?? false
  );

  const entityInnerSpace = computed(() => props.groupEntity?.getGroupSpace());

  /**
   * 组内模块是否可编辑
   *  -- 如果组内模块为已封装模块，则不可编辑
   *  -- 如果组内模块为只读模块，则不可编辑
   */
  const entityCanChangeRequired = computed(() => {
    return props.groupEntity?.getExistFlag();
  });

  /**
   * 组模块是否必填
   */
  const entityRequired = computed({
    get: () => props.groupEntity?.getGroupRequired(),
    set: (value: boolean) => {
      props.groupEntity?.setGroupRequired(value);
    },
  });

  /**
   * 组模块是否可显示只读
   */
  const entityShowReadonlyAttr = computed(() => {
    return props.groupEntity?.getGroupHasReadonlyFlag();
  });

  /**
   * 组模块是否仅展示
   */
  const entityReadonlyAttr = computed({
    get: () => props.groupEntity?.getGroupReadOnly(),
    set: (value: boolean) => {
      props.groupEntity?.setGroupReadOnly(value);
    },
  });

  /**
   * 组模块标题
   */
  const entityTitle = computed({
    get: () => props.groupEntity?.getGroupTitle(),
    set: (value) => {
      if (ValidateUtils.isNotEmpty(value)) {
        props.groupEntity?.setGroupTitle(value);
      } else {
        props.groupEntity?.setGroupTitle('请输入组件标题');
      }
    },
  });

  /**
   * 组模块描述
   */
  const entityDesc = computed({
    get: () => props.groupEntity?.getGroupDesc(),
    set: (value) => {
      props.groupEntity?.setGroupDesc(value);
    },
  });

  /**
   * 组内模块参数是否可修改
   *  -- 如果组内模块为封装模块，则不可修改参数
   *  -- 如果组内模块为只读模块，则不可修改参数
   */
  const entityModuleCanChange = computed(() => {
    return (
      !props.groupEntity?.getExistFlag() &&
      !props.groupEntity?.getGroupReadOnly()
    );
  });
</script>

<template>
  <div v-if="props.groupEntity" class="setting-form">
    <a-form style="width: 100%" auto-label-width layout="vertical">
      <a-form-item>
        <a-checkbox
          v-model="entityRequired"
          :disabled="entityCanChangeRequired"
        >
          <span class="heavy-dark-label">必填项</span>
        </a-checkbox>
      </a-form-item>
      <a-form-item v-if="entityShowReadonlyAttr">
        <a-checkbox v-model="entityReadonlyAttr">
          <span class="form-required-label">仅展示，无需用户填写</span>
        </a-checkbox>
      </a-form-item>
      <a-form-item label="标题设置">
        <div class="name-block base-info-block must-item">
          <a-input
            v-model="entityTitle"
            type="text"
            placeholder="请输入标题"
            :disabled="entityExistFlag"
            :max-length="
              ActivitySettingLimitValue.activityModuleGroupTitleMaxLength
            "
            show-word-limit
          />
        </div>
      </a-form-item>
      <a-form-item
        v-if="entityDesc !== undefined && !entityExistFlag"
        label="描述设置(可为空)"
      >
        <div class="desc-block base-info-block">
          <div class="title-block"></div>
          <a-input
            v-model="entityDesc"
            type="text"
            placeholder="请输入描述"
            :max-length="
              ActivitySettingLimitValue.activityModuleGroupDescriptionMaxLength
            "
          />
        </div>
      </a-form-item>

      <a-form-item
        v-for="(module, index) in props.groupEntity.getGroupModules()"
        :key="index"
      >
        <div v-if="module" class="group-item-block">
          <div class="form-format-title">
            【{{ module.getComponentName() }}】属性
          </div>
          <!-- 修改了can-edit为enabled ;item-module-entity为item-->
          <component
            :is="module.getSettingModuleRef()"
            :enabled="entityModuleCanChange"
            :item="module"
            :space="entityInnerSpace"
            class="component-item"
          />
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="less">
  .setting-form {
    min-height: 500px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    align-self: stretch;

    .heavy-dark-label {
      color: #1d2129;
      /* 16/CN-Regular */
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px; /* 150% */
    }
    .base-info-block {
      width: 100%;
    }
    .group-item-block {
      width: calc(100% - 10px);
      padding-left: 10px;

      .form-format-title {
        margin-left: -20px;
        margin-bottom: 10px;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        color: #1d2129;
      }
    }

    .form-format {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      align-self: stretch;

      .form-format-item {
        width: 100%;
        margin-bottom: 15px;
        .form-format-title {
          color: var(--text-1, #1d2129);

          /* 16/CN-Medium */
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px; /* 150% */
        }

        .form-format-content {
          padding-left: 15px;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          align-self: stretch;
        }
      }
    }
  }
</style>
