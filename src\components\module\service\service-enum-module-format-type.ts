import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { MapEnumModuleFormatTypeLabel } from '@/components/module/maps/map-enum-module-format-type-label';
import { EnumUtils } from '@/utils/enum-utils';

/**
 * 枚举模块格式类型服务
 */
export const ServiceEnumModuleFormatType = {
  /** 从 MapEnumModuleFormatType.MapOfInputFormatTypeLabel 获取类型名称 */
  GetInputTypeName: (key: EnumComponentFormatType): string | undefined => {
    return EnumUtils.getEnumModuleFormatLabel<EnumComponentFormatType>(
      key,
      MapEnumModuleFormatTypeLabel.MapOfInputFormatTypeLabel
    );
  },
  /** 将 MapEnumModuleFormatType.MapOfInputFormatTypeLabel 转换为对象数组 */
  GetInputTypeOptionList: (): {
    itemKey: EnumComponentFormatType;
    itemLabel: string;
  }[] => {
    return EnumUtils.getEnumModuleFormatOptionList<EnumComponentFormatType>(
      MapEnumModuleFormatTypeLabel.MapOfInputFormatTypeLabel
    );
  },
  /**
   * 校验是否为输入格式
   * @param key
   * @constructor
   */
  ValidateInputType(key: EnumComponentFormatType): boolean {
    return MapEnumModuleFormatTypeLabel.MapOfInputFormatTypeLabel.has(key);
  },

  /** 从 MapEnumModuleFormatType.MapOfSingleMultipleFormatTypeLabel 获取类型名称 */
  GetSingleMultipleFormatTypeLabel: (
    key: EnumComponentFormatType
  ): string | undefined => {
    return EnumUtils.getEnumModuleFormatLabel<EnumComponentFormatType>(
      key,
      MapEnumModuleFormatTypeLabel.MapOfSingleMultipleFormatTypeLabel
    );
  },
  /** 将 MapEnumModuleFormatType.MapOfSingleMultipleFormatTypeLabel 转换为对象数组 */
  GetSingleMultipleFormatTypeOptionList: (): {
    itemKey: EnumComponentFormatType;
    itemLabel: string;
  }[] => {
    return EnumUtils.getEnumModuleFormatOptionList<EnumComponentFormatType>(
      MapEnumModuleFormatTypeLabel.MapOfSingleMultipleFormatTypeLabel
    );
  },
  /**
   * 校验是否为单/多选格式
   * @param key
   * @constructor
   */
  ValidateSingleMultipleType(key: EnumComponentFormatType): boolean {
    return MapEnumModuleFormatTypeLabel.MapOfSingleMultipleFormatTypeLabel.has(
      key
    );
  },

  /** 从 MapEnumModuleFormatType.MapOfUploadFormatTypeLabel 获取类型名称 */
  GetUploadFormatTypeLabel: (
    key: EnumComponentFormatType
  ): string | undefined => {
    return EnumUtils.getEnumModuleFormatLabel<EnumComponentFormatType>(
      key,
      MapEnumModuleFormatTypeLabel.MapOfUploadFormatTypeLabel
    );
  },
  /** 从 MapEnumModuleFormatType.MapOfUploadFormatTypeLabel 获取类型选项列表 */
  GetUploadFormatTypeOptionList: (): {
    itemKey: EnumComponentFormatType;
    itemLabel: string;
  }[] => {
    return EnumUtils.getEnumModuleFormatOptionList<EnumComponentFormatType>(
      MapEnumModuleFormatTypeLabel.MapOfUploadFormatTypeLabel
    );
  },
  /**
   * 校验是否为上传格式
   * @param key
   * @constructor
   */
  ValidateUploadType(key: EnumComponentFormatType): boolean {
    return MapEnumModuleFormatTypeLabel.MapOfUploadFormatTypeLabel.has(key);
  },

  /** 从 MapEnumModuleFormatType.MapOfDateFormatTypeLabel 获取类型名称 */
  GetDateFormatTypeLabel: (
    key: EnumComponentFormatType
  ): string | undefined => {
    return EnumUtils.getEnumModuleFormatLabel<EnumComponentFormatType>(
      key,
      MapEnumModuleFormatTypeLabel.MapOfDateFormatTypeLabel
    );
  },
  /** 从 MapEnumModuleFormatType.MapOfDateFormatTypeLabel 获取类型选项列表 */
  GetDateFormatTypeOptionList: (): {
    itemKey: EnumComponentFormatType;
    itemLabel: string;
  }[] => {
    return EnumUtils.getEnumModuleFormatOptionList<EnumComponentFormatType>(
      MapEnumModuleFormatTypeLabel.MapOfDateFormatTypeLabel
    );
  },
  /**
   * 校验是否为日期格式
   * @param key
   * @constructor
   */
  ValidateDateType(key: EnumComponentFormatType): boolean {
    return MapEnumModuleFormatTypeLabel.MapOfDateFormatTypeLabel.has(key);
  },

  /** 从 MapEnumModuleFormatType.MapOfDateRangeFormatTypeLabel 获取类型名称 */
  GetDateRangeFormatTypeLabel: (
    key: EnumComponentFormatType
  ): string | undefined => {
    return EnumUtils.getEnumModuleFormatLabel<EnumComponentFormatType>(
      key,
      MapEnumModuleFormatTypeLabel.MapOfDateRangeFormatTypeLabel
    );
  },
  /** 从 MapEnumModuleFormatType.MapOfDateRangeFormatTypeLabel 获取类型列表 */
  GetDateRangeFormatTypeOptionList: (): {
    itemKey: EnumComponentFormatType;
    itemLabel: string;
  }[] => {
    return EnumUtils.getEnumModuleFormatOptionList<EnumComponentFormatType>(
      MapEnumModuleFormatTypeLabel.MapOfDateRangeFormatTypeLabel
    );
  },
  /**
   * 校验是否为日期范围格式
   * @param key
   * @constructor
   */
  ValidateDateRangeType(key: EnumComponentFormatType): boolean {
    return MapEnumModuleFormatTypeLabel.MapOfDateRangeFormatTypeLabel.has(key);
  },
};
