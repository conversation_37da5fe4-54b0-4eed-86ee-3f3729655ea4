<template>
  <div class="official-demo-tip-card">
    <div class="official-demo-tip-title">官方图片</div>
    <div class="official-demo-tip-desc">
      官方图片支持主办方上传需要用户转发的官方图片，并支持用户在报名端口保存图片。<br />
      主办方也可以为官方图片单独命名，并在其任务描述内要求用户按照指定步骤发送。<br />
      例一：“发布时需将主图置于九宫格中间，并将嘉宾图置于九宫格第一张。”<br />
      例二：“从9张主图中任选3张发布，必须包含主图。”
    </div>
    <div class="official-demo-tip-example">
      <div class="official-demo-tip-input demo-first"></div>
      <div class="official-demo-tip-imgs">
        <div class="official-demo-tip-img img-selected">
          <div class="official-demo-tip-img-car"></div>
          <div class="official-demo-tip-img-bar"> </div>
        </div>
        <div class="official-demo-tip-img">
          <div class="official-demo-tip-img-car"></div>
          <div class="official-demo-tip-img-bar"> </div>
        </div>
        <div class="official-demo-tip-img">
          <div class="official-demo-tip-img-car"></div>
          <div class="official-demo-tip-img-bar"> </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .official-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .official-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .official-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .official-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .official-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 4px;
  }
  .official-demo-tip-imgs {
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%;
    justify-content: center;
    background: #e8f3ff;
    border-radius: 12px;
  }
  .official-demo-tip-img {
    width: 68px;
    height: 93px;
    background: #e8f3ff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    overflow: hidden;
  }
  .official-demo-tip-img-car {
    width: 68px;
    height: 68px;
    background-color: #ffffff;
    border-radius: 10px;
    margin-bottom: 6px;
  }
  .img-selected {
    border: 2.5px solid #4080ff;
    box-sizing: border-box;
  }
  .official-demo-tip-img-bar {
    width: 80%;
    height: 15px;
    background: #c6e0ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
</style>
