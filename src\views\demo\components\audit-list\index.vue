<template>
  <div
    class="tw-relative tw-bg-white tw-rounded-lg tw-shadow-sm tw-h-full tw-w-72 registration-manage"
  >
    <!-- 状态标签 -->
    <StatusTabs
      v-model:activeTab="activeTab"
      :tabs="statusTabs"
      @change="handleTabChange"
    />

    <!-- 搜索和筛选区域 -->
    <SearchFilters
      v-model:searchText="searchText"
      v-model:platform="selectedPlatform"
      v-model:date="selectedDate"
      v-model:role="selectedRole"
      :platforms="platforms"
      :roles="roles"
      @search="handleSearch"
      @filter="handleFilter"
    />

    <!-- 用户列表 -->
    <UserList
      :users="filteredUsers"
      :loading="loading"
      @action="handleUserAction"
    />

    <div
      class="tw-w-full tw-px-4 tw-pt-3 tw-pb-2 tw-absolute tw-bottom-0 tw-bg-white/25 dark:tw-bg-gray-800/30 tw-backdrop-blur-md tw-backdrop-saturate-150 tw-border-t tw-border-white/30 dark:tw-border-gray-700/30 tw-rounded-t-lg tw-flex tw-justify-center tw-items-center tw-gap-2 tw-transition-all tw-duration-300"
    >
      <div
        class="tw-flex-1 tw-px-2 tw-py-1 tw-bg-primary-5 tw-rounded tw-flex tw-justify-center tw-items-center tw-gap-2"
      >
        <a-button
          type="primary"
          style="background-color: #4080ff"
          class="tw-text-center tw-justify-center tw-text-text-white tw-text-sm tw-font-medium tw-font-['PingFang_SC'] tw-leading-snug tw-w-[228px] tw-h-[30px] tw-border-none"
          >批量通过</a-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import StatusTabs from './components/status-tabs.vue';
  import SearchFilters from './components/search-filters.vue';
  import UserList from './components/user-list.vue';
  import type { User, StatusTab, Platform, Role } from './types';

  // 响应式数据
  const activeTab = ref('pending');
  const searchText = ref('');
  const selectedPlatform = ref('');
  const selectedDate = ref('');
  const selectedRole = ref('');
  const loading = ref(false);
  const users = ref<User[]>([]);

  // 状态标签配置 - 根据实际数据更新计数
  const statusTabs: StatusTab[] = [
    { key: 'pending', label: '待审批', count: 13 },
    { key: 'approved', label: '已通过', count: 3 },
    { key: 'rejected', label: '未通过', count: 2 },
  ];

  // 平台选项
  const platforms: Platform[] = [
    { value: 'xiaohongshu', label: '小红书' },
    { value: 'wechat', label: '微信' },
    { value: 'kuaishou', label: '快手' },
  ];

  // 角色选项 - 根据图片中的标签更新
  const roles: Role[] = [
    { value: 'photographer', label: '摄影' },
    { value: 'coser', label: 'COSER' },
    { value: 'model', label: '模特' },
    { value: 'makeup', label: '化妆' },
    { value: 'costume', label: '服装' },
    { value: 'lolita', label: 'LOLITA' },
    { value: 'hanfu', label: '汉服' },
    { value: 'sankeng', label: '三坑' },
    { value: 'fursuit', label: '兽装' },
  ];

  // 计算属性 - 过滤后的用户列表
  const filteredUsers = computed(() => {
    let result = users.value;

    // 根据状态过滤
    if (activeTab.value !== 'all') {
      result = result.filter((user) => user.status === activeTab.value);
    }

    // 根据搜索文本过滤
    if (searchText.value) {
      result = result.filter((user) => user.name.includes(searchText.value));
    }

    // 根据平台过滤
    if (selectedPlatform.value) {
      result = result.filter(
        (user) => user.platform === selectedPlatform.value
      );
    }

    // 根据角色过滤
    if (selectedRole.value) {
      result = result.filter((user) => user.role === selectedRole.value);
    }

    // 将结果数组重复一次（复制并合并）
    return result;
  });

  // 方法
  const handleTabChange = (tab: string) => {
    activeTab.value = tab;
    loadUsers();
  };

  const handleSearch = () => {
    // 搜索逻辑已在计算属性中处理
  };

  const handleFilter = () => {
    // 筛选逻辑已在计算属性中处理
  };

  const handleUserAction = (action: string, user: User) => {
    switch (action) {
      case 'approve':
        approveUser(user);
        break;
      case 'reject':
        rejectUser(user);
        break;
      case 'view':
        viewUserDetail(user);
        break;
      default:
        console.log(`未知操作: ${action}`, user);
    }
  };

  const approveUser = async (user: User) => {
    try {
      // 调用审批通过API
      // await approveRegistration(user.id);
      user.status = 'approved';
      // 可以添加成功提示
    } catch (error) {
      console.error('审批失败:', error);
    }
  };

  const rejectUser = async (user: User) => {
    try {
      // 调用审批拒绝API
      // await rejectRegistration(user.id);
      user.status = 'rejected';
      // 可以添加成功提示
    } catch (error) {
      console.error('拒绝失败:', error);
    }
  };

  const viewUserDetail = (user: User) => {
    // 查看用户详情逻辑
    console.log('查看用户详情:', user);
  };

  const loadUsers = async () => {
    loading.value = true;
    try {
      // const response = await getRegistrationList({
      //   status: activeTab.value,
      //   platform: selectedPlatform.value,
      //   role: selectedRole.value
      // });

      // 模拟数据 - 根据图片内容设置
      users.value = [
        {
          id: '1',
          name: '太长的用户名...',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'model',
          status: 'pending',
          applyDate: '2024-01-15',
          actionLabel: '太长的...',
        },
        {
          id: '2',
          name: '太长的用户名',
          avatar: '',
          platform: 'wechat',
          role: 'model',
          status: 'pending',
          applyDate: '2024-01-14',
          actionLabel: '太长的角色名',
        },
        {
          id: '3',
          name: '普通被选中时',
          avatar: '',
          platform: 'kuaishou',
          role: 'photographer',
          status: 'pending',
          applyDate: '2024-01-13',
          actionLabel: '摄影',
        },
        {
          id: '4',
          name: '没被选中时',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'coser',
          status: 'pending',
          applyDate: '2024-01-12',
          actionLabel: 'COSER',
        },
        {
          id: '5',
          name: '中风险选中',
          avatar: '',
          platform: 'wechat',
          role: 'model',
          status: 'pending',
          applyDate: '2024-01-11',
          actionLabel: 'LOLITA',
        },
        {
          id: '6',
          name: '中风险没选中',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'model',
          status: 'pending',
          applyDate: '2024-01-10',
          actionLabel: '汉服',
        },
        {
          id: '7',
          name: '高风险选中',
          avatar: '',
          platform: 'kuaishou',
          role: 'makeup',
          status: 'pending',
          applyDate: '2024-01-09',
          actionLabel: '三坑',
        },
        {
          id: '8',
          name: '高风险没选中',
          avatar: '',
          platform: 'wechat',
          role: 'costume',
          status: 'pending',
          applyDate: '2024-01-08',
          actionLabel: '兽装',
        },
        {
          id: '9',
          name: '杰瑞',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'makeup',
          status: 'pending',
          applyDate: '2024-01-07',
          actionLabel: '兽装',
        },
        {
          id: '10',
          name: '杰瑞',
          avatar: '',
          platform: 'kuaishou',
          role: 'costume',
          status: 'pending',
          applyDate: '2024-01-06',
          actionLabel: '兽装',
        },
        {
          id: '11',
          name: '金属',
          avatar: '',
          platform: 'wechat',
          role: 'coser',
          status: 'pending',
          applyDate: '2024-01-05',
          actionLabel: 'COSER',
        },
        {
          id: '12',
          name: '啊林林林林林...',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'costume',
          status: 'pending',
          applyDate: '2024-01-04',
          actionLabel: '兽装',
        },
        {
          id: '13',
          name: '念安',
          avatar: '',
          platform: 'kuaishou',
          role: 'coser',
          status: 'pending',
          applyDate: '2024-01-03',
          actionLabel: 'COSER',
        },
        // 已通过状态的用户
        {
          id: '14',
          name: '用户1',
          avatar: '',
          platform: 'wechat',
          role: 'photographer',
          status: 'approved',
          applyDate: '2024-01-11',
          actionLabel: '摄影',
        },
        {
          id: '15',
          name: '用户2',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'coser',
          status: 'approved',
          applyDate: '2024-01-10',
          actionLabel: 'COSER',
        },
        {
          id: '16',
          name: '用户3',
          avatar: '',
          platform: 'kuaishou',
          role: 'model',
          status: 'approved',
          applyDate: '2024-01-09',
          actionLabel: '模特',
        },
        // 未通过状态的用户
        {
          id: '17',
          name: '被拒绝用户1',
          avatar: '',
          platform: 'wechat',
          role: 'makeup',
          status: 'rejected',
          applyDate: '2024-01-08',
          actionLabel: '化妆',
        },
        {
          id: '18',
          name: '被拒绝用户2',
          avatar: '',
          platform: 'xiaohongshu',
          role: 'costume',
          status: 'rejected',
          applyDate: '2024-01-07',
          actionLabel: '服装',
        },
      ];
    } catch (error) {
      console.error('加载用户列表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 生命周期
  onMounted(() => {
    loadUsers();
  });
</script>

<script lang="ts">
  export default {
    name: 'AuditList',
  };
</script>

<style scoped>
  .registration-manage {
    height: 100%;
    width: 276px;
    /* overflow: hidden; */
  }
</style>
