import { DefineComponent } from 'vue';
import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import {
  ComponentUnifiedAttributeModel,
  KVLModel,
} from '@/components/module/type/attribute-model-unified';
import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';

export interface ICustomComponent {
  /**  获取组件名称 */
  getComponentName(): string;
  /**  获取组件模型类型 */
  getComponentStructType(): EnumComponentStructType;
  /**  获取组件类型 */
  getComponentType(): EnumComponentFormatType;
  /**  获取组件显示实例 */
  getShowModuleRef(): DefineComponent | undefined;
  /**  获取组件设置实例 */
  getSettingModuleRef(): DefineComponent | undefined;
  /**  获取组件属性 */
  getComponentUnifiedAttribute(): ComponentUnifiedAttributeModel;

  getComponentExampleAttribute(): any;

  /**  获取组件属性 - 值属性的值 */
  getComponentValueAttribute(): any;

  /** 设置组件属性 */
  setComponentExpandAttribute(key: string, value: any): void;
  /** 设置组件格式 */
  setComponentFormatType(type: EnumComponentFormatType): void;
  /** 设置组件占位符 */
  setComponentPlaceholder(placeholder: string | undefined): void;
  /** 设置组件最小限制长度 */
  setComponentMinLength(minLength: number | undefined): void;
  /** 获取组件最大限制长度 */
  setComponentMaxLength(maxLength: number | undefined): void;
  /** 设置组件最小值 */
  setComponentMin(min: number | undefined): void;
  /** 设置组件最大值 */
  setComponentMax(max: number | undefined): void;
  /** 设置组件统一限制长度 */
  setComponentLength(length: number | undefined): void;
  /** 设置组件文件类型限制 */
  setComponentFileMaxSize(fileMaxSize: number | undefined): void;
  /** 设置组件文件类型限制 */
  setComponentFileTypes(fileTypes: string[] | undefined): void;
  /** 设置组件选项 */
  setComponentOptions(options: KVLModel[] | undefined): void;
  /** 设置组件是否开启图片原图上传 */
  setComponentOriginalImage(originalImage: boolean | undefined): void;
  /** 设置组件是否开启文件重命名 */
  setComponentRename(rename: boolean | undefined): void;
  /** 设置组件值 */
  setComponentValue(value: unknown): void;
  /** 设置组件模版 */
  setComponentTemplates(templates: KVLModel[] | undefined): void;
  /** 设置组件示例图片 */
  setComponentExampleImages(exampleImages: unknown): void;

  verifyComponentAttribute(): string[];
}
