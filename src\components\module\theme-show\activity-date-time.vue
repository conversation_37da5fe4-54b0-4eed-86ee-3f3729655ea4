<script setup lang="ts">
  import { computed, defineProps, PropType } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  // 父组件传参

  const props = defineProps({
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    // 是否可编辑
    // enabled: {
    //   type: Boolean,
    //   required: true,
    //   default: false,
    // },
  });

  const itemAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  const placeholderAttr = computed(
    () => itemAttribute.value?.placeholder ?? ''
  );
</script>

<template>
  <div class="activity-input">
    <div class="body">
      <span>{{ placeholderAttr }}</span>
      <icon-calendar :size="18" />
    </div>
  </div>
</template>

<style scoped lang="less">
  .activity-input {
    width: 100%;
    display: flex;
    padding: 5px 12px;
    align-items: center;
    flex: 1 0 0;
    border-radius: 2px;
    background: #fff;
    .body {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      flex: 1 0 0;
      color: #86909c;
      font-family: 'PingFang SC', Arial, sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      span {
        height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
</style>
