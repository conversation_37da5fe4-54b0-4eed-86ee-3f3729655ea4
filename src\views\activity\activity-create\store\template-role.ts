import { EnumTemplateRole } from '@/views/activity/activity-create/enum/enum-template-role';
import { ITemplateRoleItem } from '@/views/activity/activity-create/type/model-template-role';
import { reactive } from 'vue';

export class ClassTemplateRole {
  private _defaultRoleMap = new Map<EnumTemplateRole, ITemplateRoleItem>();

  constructor() {
    this.initDefaultRoleMap();
  }

  // 初始化 Map
  private initDefaultRoleMap = () => {
    this._defaultRoleMap = new Map<EnumTemplateRole, ITemplateRoleItem>([
      [
        EnumTemplateRole.Coser,
        {
          roleId: EnumTemplateRole.Coser,
          roleName: 'Cos',
        },
      ],
      [
        EnumTemplateRole.Photographer,
        {
          roleId: EnumTemplateRole.Photographer,
          roleName: '摄影',
        },
      ],
      [
        EnumTemplateRole.Hanfu,
        {
          roleId: EnumTemplateRole.Hanfu,
          roleName: '汉服',
        },
      ],
      [
        EnumTemplateRole.Lolita,
        {
          roleId: EnumTemplateRole.Lolita,
          roleName: 'Lolita',
        },
      ],
      [
        EnumTemplateRole.Tokusatsu,
        {
          roleId: EnumTemplateRole.Tokusatsu,
          roleName: '特摄',
        },
      ],
      [
        EnumTemplateRole.Fursuit,
        {
          roleId: EnumTemplateRole.Fursuit,
          roleName: '兽装',
        },
      ],
    ]);
  };

  //  获取角色列表
  public getTemplateRoleList(): ITemplateRoleItem[] {
    // 使用 Array.from 展开 Map.values() 迭代器
    return Array.from(this._defaultRoleMap.values());
  }
}
