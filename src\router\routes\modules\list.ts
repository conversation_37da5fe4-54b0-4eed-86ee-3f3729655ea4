import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const LIST: AppRouteRecordRaw = {
  path: '/list',
  name: 'list',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: 'menu.list',
    requiresAuth: true,
    icon: 'icon-list',
    order: 2,
  },
  children: [
    // {
    //   path: 'grant', // The midline path complies with SEO specifications
    //   name: '<PERSON>',
    //   component: () => import('@/views/list/grant/index.vue'),
    //   meta: {
    //     locale: 'menu.list.grant',
    //     requiresAuth: true,
    //     roles: ['*'],
    //   },
    // },
    // {
    //   path: 'order-details',
    //   name: 'OrderDetails',
    //   component: () => import('@/views/list/order-details/index.vue'),
    //   meta: {
    //     locale: 'menu.list.orderDetails',
    //     requiresAuth: true,
    //     roles: ['*'],
    //   },
    // },
  ],
};

export default LIST;
