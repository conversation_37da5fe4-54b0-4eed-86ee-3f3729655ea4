<template>
  <a-popover position="right">
    <template #content>
      <component :is="currentComponent" v-if="currentComponent" />
    </template>
    <slot name="content"></slot>
  </a-popover>
</template>

<script setup lang="ts">
  import { computed } from 'vue';
  import RadioSelect from './radio-select.vue';
  import CheckboxSelect from './checkbox-select.vue';
  import TextInput from './text-input.vue';
  import FileUpload from './file-upload.vue';
  import TaskDescription from './task-description.vue';
  import CopyText from './copy-text.vue';
  import OfficialImage from './official-image.vue';
  import TaskUpload from './task-upload.vue';
  import PhotoUpload from './photo-upload.vue';
  import VideoUpload from './video-upload.vue';
  import LinkUpload from './link-upload.vue';
  import CostumeNote from './costume-note.vue';

  const props = defineProps({
    name: {
      type: String,
      default: 'RadioSelect',
    },
  });

  const componentMap = {
    RadioSelect,
    CheckboxSelect,
    TextInput,
    FileUpload,
    TaskDescription,
    CopyText,
    OfficialImage,
    TaskUpload,
    PhotoUpload,
    VideoUpload,
    LinkUpload,
    CostumeNote,
  };

  const currentComponent = computed(() => componentMap[props.name]);
</script>
