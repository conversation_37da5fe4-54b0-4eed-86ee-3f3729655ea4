<script setup lang="ts">
  import {
    computed,
    defineProps,
    nextTick,
    onUpdated,
    PropType,
    ref,
  } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { ValidateUtils } from '@/utils/validate';
  import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
  import { ConvertUtils } from '@/utils/convert';
  import { KVLModel } from '@/components/module/type/attribute-model-unified';
  import { Message } from '@arco-design/web-vue';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

  // 父组件传参
  const props = defineProps({
    /** 模块实体 */
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    /** 当前组，组内参数是否可修改 */
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  // 计算属性简化访问
  const moduleAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  /** 格式属性 */
  const formatAttr = computed({
    get: () => moduleAttribute.value?.format,
    set: (val) => {
      props.item?.setComponentFormatType(val);
    },
  });

  const minAttr = computed({
    get: () => moduleAttribute.value?.min,
    set: (val) => {
      if (!val) {
        val = -1;
        props.item?.setComponentExpandAttribute('min', val);
      }
      nextTick(() => {
        props.item?.setComponentMin(val);
      });
    },
  });
  const maxAttr = computed({
    get: () => moduleAttribute.value?.max,
    set: (val) => {
      if (!val) {
        val = -1;
        props.item?.setComponentExpandAttribute('max', val);
      }
      nextTick(() => {
        props.item?.setComponentMax(val);
      });
    },
  });

  /** 选项属性 */
  const optionsAttr = computed({
    get: () => moduleAttribute.value?.options,
    set: (val) => {
      props.item?.setComponentOptions(val);
    },
  });

  /** 选项数量属性 */
  const OptionsCount = computed({
    get: () => optionsAttr.value?.length ?? 2,
    set: (val) => {
      console.log('OptionsCount', val);
      if (!val) {
        val = 2;
      }
      props.item?.setComponentLength(val);
    },
  });

  /** 选项数量属性 - 本地 */
  const localOptionsCount = ref(
    props.item?.getComponentUnifiedAttribute().options?.length ?? 0
  );
  /**
   * 选项属性 - 本地
   */
  const localOptions = ref(
    props.item?.getComponentUnifiedAttribute().options ?? [
      {
        key: '选项1',
        label: '选项1',
        value: '选项1',
      },
      {
        key: '选项2',
        label: '选项2',
        value: '选项2',
      },
    ]
  );

  onUpdated(() => {
    localOptionsCount.value =
      props.item?.getComponentUnifiedAttribute().options?.length ?? 2;
    localOptions.value = props.item?.getComponentUnifiedAttribute().options ?? [
      {
        key: '选项1',
        label: '选项1',
        value: '选项1',
      },
      {
        key: '选项2',
        label: '选项2',
        value: '选项2',
      },
    ];
  });

  /**
   * 计算选项属性中最大选项ID并加1
   * 此代码块的目的是在给定的选项属性数组中找到最大的选项ID，
   * 然后将其加1，通常用于生成新的选项ID
   *
   * @returns {number} - 最大选项ID加1后的值，如果数组为空或无有效数字，则返回0
   */
  const getCheckOptionLengthMaxId = (): number => {
    if (!optionsAttr.value || optionsAttr.value.length === 0) {
      return 0; // 数组为空，直接返回默认值
    }
    // 过滤出所有可以转为数字的项，并提取其数值
    const numericValues = optionsAttr.value
      .map((item: KVLModel) => Number(item.value))
      .filter((val: number) => !Number.isNaN(val));

    if (numericValues.length === 0) {
      return 0; // 没有有效数字，返回默认值
    }
    const maxValue = Math.max(...numericValues);
    return maxValue + 1;
  };

  interface CheckOptionModelInSetting {
    optionId: number; // 对应key
    optionLabel: string; // 对应label
    showError: boolean;
  }

  const formInputContent = ref<CheckOptionModelInSetting[]>(
    [] as CheckOptionModelInSetting[]
  );

  const addPanelVisible = ref(false);
  /**
   * 选项数量改变 - 数字输入添加
   * @param e
   */
  const optionsCountChanged = (e: number) => {
    if (!props.enabled) {
      localOptionsCount.value = OptionsCount.value;
      return;
    }
    if (ValidateUtils.isNullOrEmpty(e)) {
      nextTick(() => {
        localOptionsCount.value = OptionsCount.value;
      });
    } else {
      e = Math.floor(e);
      const dev = e - OptionsCount.value;
      console.log(e, dev);
      if (dev <= 0) {
        localOptionsCount.value = OptionsCount.value;
        return;
      }
      const idPlusOne = getCheckOptionLengthMaxId();
      formInputContent.value = [] as CheckOptionModelInSetting[];
      for (let i = 0; i < dev; i += 1) {
        formInputContent.value.push({
          optionId: idPlusOne + i,
          optionLabel: '',
          showError: false,
        });
      }
      addPanelVisible.value = true;
    }
  };

  const onAddItem = () => {
    if (!props.enabled) return;
    formInputContent.value = [] as CheckOptionModelInSetting[];
    formInputContent.value.push({
      optionId: getCheckOptionLengthMaxId(),
      optionLabel: '',
      showError: false,
    });
    addPanelVisible.value = true;
  };

  const removeInputItem = (index: number) => {
    if (!props.enabled) return;
    formInputContent.value.splice(index, 1);
  };

  /**
   * 合并或更新选项列表：
   * - id 相同则替换 label，保留原 id
   * - label 相同则去重
   * - 最终按 optionId 升序排列
   */
  const mergeAndDeduplicateOptions = (
    newList: CheckOptionModelInSetting[],
    originalList?: KVLModel[]
  ): KVLModel[] => {
    const idMap = new Map<string, KVLModel>(); // key: optionId -> item
    const labelSet = new Set<string>(); // key: optionLabel.trim() -> exists

    // 创建 KVLModel 对象的统一方法
    const createKVLModel = (id: string, label: string): KVLModel => ({
      key: id,
      value: id,
      label,
    });

    // Step 1: 先处理原始数据
    if (originalList) {
      originalList.forEach((item: KVLModel) => {
        const trimmedLabel = item.label.trim();
        const id = item.value;

        if (!id || !trimmedLabel) return; // 忽略无效项

        if (!labelSet.has(trimmedLabel)) {
          labelSet.add(trimmedLabel);
          idMap.set(id, item);
        }
      });
    }

    // Step 2: 处理新数据：id相同则替换label，否则合并
    newList.forEach((item) => {
      const trimmedLabel = item.optionLabel?.trim() ?? '';
      const id = item.optionId?.toString();

      if (!id || !trimmedLabel) return; // 忽略无效项

      const existingItem = idMap.get(id);

      if (existingItem) {
        // ID 存在，更新 label
        const oldLabel = existingItem.label.trim();
        if (oldLabel !== trimmedLabel) {
          labelSet.delete(oldLabel);
          labelSet.add(trimmedLabel);
          idMap.set(id, createKVLModel(id, trimmedLabel));
        }
      } else {
        // ID 不存在，检查 label 是否已存在
        // eslint-disable-next-line no-lonely-if
        if (!labelSet.has(trimmedLabel)) {
          labelSet.add(trimmedLabel);
          idMap.set(id, createKVLModel(id, trimmedLabel));
        }
      }
    });

    // Step 3: 按 optionId 排序后返回
    return [...idMap.values()].sort(
      (a, b) => Number(a.value) - Number(b.value)
    );
  };

  const handleOk = (e: any) => {
    if (!props.enabled) return;
    let anyEmpty = false;
    formInputContent.value.forEach((el) => {
      el.showError = !ValidateUtils.isNotEmpty(el.optionLabel);
      if (!anyEmpty) {
        anyEmpty = el.showError;
      }
    });
    if (anyEmpty) return;

    optionsAttr.value = mergeAndDeduplicateOptions(
      formInputContent.value,
      optionsAttr.value
    );
    localOptionsCount.value = optionsAttr.value?.length ?? 0;
    localOptions.value = optionsAttr.value;
    addPanelVisible.value = false;
  };

  const handleCancel = (e: any) => {
    if (!props.enabled) return;
    formInputContent.value.length = 0;
    localOptionsCount.value = optionsAttr.value?.length ?? 0;
    addPanelVisible.value = false;
  };

  /**
   * 校验 item 是否有效
   * @param item
   */
  const isValidItem = (item: KVLModel): boolean => {
    if (!item || ValidateUtils.isNullOrEmpty(item.key)) {
      Message.warning(`尝试操作无效选项:${item}`);
      return false;
    }
    if (Number.isNaN(Number(item.key))) {
      Message.warning(`${item.key} 不是合法数字`);
      return false;
    }
    return true;
  };

  /**
   * 编辑选项
   * @param item
   */
  const onItemEdit = (item: KVLModel) => {
    if (!props.enabled) return;
    if (!isValidItem(item)) return;

    const findItem = optionsAttr.value?.find(
      (lItem: KVLModel) => lItem.key === item.key
    );
    if (findItem) {
      formInputContent.value.push({
        optionId: Number(findItem.key),
        optionLabel: findItem.label,
        showError: false,
      });
      addPanelVisible.value = true;
    }
  };

  /**
   * 删除选项
   * @param item
   */
  const onItemDel = (item: KVLModel) => {
    if (!props.enabled) return;
    if (!isValidItem(item)) return;

    if (localOptionsCount.value <= 2 || localOptions.value.length <= 2) {
      Message.error('选项至少要有2个');
      return;
    }

    let newOptions = optionsAttr.value?.filter(
      (lItem: KVLModel) => lItem.key !== item.key
    );

    if (newOptions && newOptions.length > 0) {
      newOptions = [...newOptions.values()].sort(
        (a, b) => Number(a.key) - Number(b.key)
      );
    }

    optionsAttr.value = newOptions;
    localOptionsCount.value = optionsAttr.value?.length ?? 0;
    localOptions.value = optionsAttr.value ?? [
      {
        key: '选项1',
        label: '选项1',
        value: '选项1',
      },
      {
        key: '选项2',
        label: '选项2',
        value: '选项2',
      },
    ];
  };
</script>

<template>
  <div class="form-format-content">
    <a-form layout="vertical">
      <!-- 格式选择 -->
      <a-form-item v-if="formatAttr" label="格式选择">
        <a-select v-model="formatAttr" :disabled="!props.enabled">
          <a-option
            v-for="(
              optionItem, optionIndex
            ) in ServiceEnumModuleFormatType.GetSingleMultipleFormatTypeOptionList()"
            :key="optionIndex"
            :value="optionItem.itemKey"
            :label="optionItem.itemLabel"
          />
        </a-select>
      </a-form-item>
      <!-- 最少需选 -->
      <a-form-item v-if="minAttr" label="最少需选">
        <a-input-number
          v-model="minAttr"
          :max="maxAttr ?? localOptionsCount"
          :min="1"
          :precision="0"
          :disabled="!props.enabled"
        />
      </a-form-item>
      <!-- 最多可选 -->
      <a-form-item v-if="maxAttr" label="最多可选">
        <a-input-number
          v-model="maxAttr"
          :max="localOptionsCount"
          :min="minAttr ?? 1"
          :precision="0"
          :disabled="!props.enabled"
        />
      </a-form-item>

      <!-- 选项数量 -->
      <a-form-item v-if="optionsAttr" label="选项数量">
        <a-input-number
          v-model="localOptionsCount"
          :min="localOptionsCount"
          :disabled="!props.enabled"
          :precision="0"
          @change="optionsCountChanged"
        />
      </a-form-item>

      <!-- 选项项目 -->
      <a-form-item v-if="optionsAttr">
        <a-list class="item-list">
          <template #header>
            <div class="item-title-block">
              <div class="title">选项项目</div>
              <a-button
                :disabled="!props.enabled"
                type="primary"
                class="add-item"
                @click="onAddItem"
              >
                <template #icon>
                  <icon-plus />
                </template>
                <template #default>添加项目</template>
              </a-button>
            </div>
          </template>
          <a-list-item v-for="(item, index) in localOptions" :key="index">
            {{ item.label }}
            <template v-if="props.enabled && localOptions.length > 2" #actions>
              <icon-edit @click="onItemEdit(item)" />
              <icon-delete @click="onItemDel(item)" />
            </template>
          </a-list-item>
        </a-list>
      </a-form-item>
    </a-form>
  </div>
  <a-modal
    :visible="addPanelVisible"
    title="请输入待添加的项目名称"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div
      v-for="(inputItem, index) in formInputContent"
      :key="index"
      class="input-block"
    >
      <div class="input-inline" style="display: flex; align-items: center">
        <a-input
          v-model="inputItem.optionLabel"
          :max-length="
            ActivitySettingLimitValue.activityModuleSingleMultipleLabelMaxCount
          "
          show-word-limit
          @focus="inputItem.showError = false"
          @press-enter="handleOk"
        />
        <icon-minus-circle
          v-if="formInputContent.length > 1"
          size="24"
          style="margin-left: 15px"
          @click="removeInputItem(index)"
        />
      </div>
      <div v-if="inputItem.showError" class="warning">请输入项目名称</div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
  .item-list {
    width: 100%;

    .item-title-block {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
    }
  }
  .warning {
    font-size: 12px;
    margin-top: 5px;
    color: red;
  }
</style>
