import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import { CustomDateTimeModule } from '@/components/module/entity/component/date-time';
import { CustomSingleMultipleModule } from '@/components/module/entity/component/single-multiple';
import { CustomInputTextModule } from '@/components/module/entity/component/input-text';
import { CustomUploadComponent } from '@/components/module/entity/component/upload';
import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';

export const ComponentFactory = {
  getModule: (attr: ComponentUnifiedAttributeModel): ICustomComponent => {
    let module: ICustomComponent;
    switch (attr.type) {
      case EnumComponentStructType.TEXT:
        module = new CustomInputTextModule(attr);
        break;
      case EnumComponentStructType.TEXTAREA:
        module = new CustomInputTextModule(attr);
        break;
      case EnumComponentStructType.RADIO:
        module = new CustomSingleMultipleModule(attr);
        break;
      case EnumComponentStructType.CHECKBOX:
        module = new CustomSingleMultipleModule(attr);
        break;
      case EnumComponentStructType.UPLOAD:
        module = new CustomUploadComponent(attr);
        break;
      case EnumComponentStructType.DATETIME:
        module = new CustomDateTimeModule(attr);
        break;
      default:
        throw new Error('模板类型错误');
    }
    console.log('module', module);
    return module;
  },
};
