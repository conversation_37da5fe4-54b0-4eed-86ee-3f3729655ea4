<template>
  <div class="text-demo-tip-card">
    <div class="text-demo-tip-title">文本</div>
    <div class="text-demo-tip-desc">
      文本答题时能在文本框内输入文字，适用于收集用户的想法或回答。也可用于做规则描述。
    </div>
    <div class="text-demo-tip-scene"> 用法一：自由输入，适合收集用户想法 </div>
    <div class="text-demo-tip-example">
      <div class="text-demo-tip-input demo-first"></div>
      <textarea class="text-demo-tip-textarea" placeholder="|" />
    </div>
    <div class="text-demo-tip-scene" style="margin-top: 18px">
      用法二：仅作文本展示，无需用户填写，适合说明规则
    </div>
    <div class="text-demo-tip-example">
      <div class="text-demo-tip-input demo-first"></div>
      <textarea
        class="text-demo-tip-textarea"
        placeholder="规则说明：&#10;1.xxxx"
        disabled
      />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .text-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .text-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .text-demo-tip-desc {
    font-size: 15px;
    margin-bottom: 10px;
  }
  .text-demo-tip-scene {
    font-size: 14px;
    margin-bottom: 8px;
  }
  .text-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .text-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 4px;
  }
  .text-demo-tip-textarea {
    width: 100%;
    min-height: 50px;
    border: none;
    border-radius: 6px;
    background: #fff;
    resize: none;
    padding: 8px 12px;
    font-size: 10px;
    color: #94BFFF;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #94bfff;
  }
  .text-demo-tip-textarea:disabled {
    color: #94BFFF;
    background: #fff;
  }
</style>
