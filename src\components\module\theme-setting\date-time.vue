<script setup lang="ts">
  import { defineProps, PropType, computed } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
  import { ValidateUtils } from '@/utils/validate';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

  // 父组件传参
  const props = defineProps({
    /** 模块实体 */
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    /** 当前组，组内参数是否可修改 */
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  // 计算属性简化访问
  const moduleAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  const formatAttr = computed({
    get: () => moduleAttribute.value?.format,

    set: (value) => {
      props.item?.setComponentFormatType(value);
    },
  });

  const placeholderAttr = computed({
    get: () => moduleAttribute.value?.placeholder,
    set: (value) => {
      if (!value) {
        value = '请输入...';
      }
      props.item?.setComponentPlaceholder(value);
    },
  });
</script>

<template>
  <div class="form-format-content">
    <a-form layout="vertical">
      <!-- 格式选择 -->
      <a-form-item v-if="formatAttr" label="格式选择">
        <a-select v-model="formatAttr" :disabled="!props.enabled">
          <a-option
            v-for="(
              optionItem, optionIndex
            ) in ServiceEnumModuleFormatType.GetDateFormatTypeOptionList()"
            :key="optionIndex"
            :value="optionItem.itemKey"
            :label="optionItem.itemLabel"
          />
        </a-select>
      </a-form-item>

      <!-- 占位符输入 -->
      <a-form-item v-if="placeholderAttr != undefined" label="提示文案">
        <a-input
          v-model="placeholderAttr"
          placeholder="请输入提示文案"
          :disabled="!props.enabled"
          :max-length="
            ActivitySettingLimitValue.activityModuleInputPlaceholderMaxLength
          "
          show-word-limit
        />
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="less"></style>
