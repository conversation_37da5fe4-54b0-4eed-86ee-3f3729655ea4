<template>
  <div class="copy-demo-tip-card">
    <div class="copy-demo-tip-title">复制文案</div>
    <div class="copy-demo-tip-desc">
      复制文案支持主办方设置需要用户转发的内容，并支持用户在报名端口一键快捷复制。
    </div>
    <div class="copy-demo-tip-example">
      <textarea
        class="copy-demo-tip-textarea"
        disabled
        placeholder="自由行分享文案&#10;我是xxx，我想去#xxxx漫展，快来帮我助力吧~"
      />
      <button class="copy-demo-tip-btn">一键复制</button>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .copy-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .copy-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .copy-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .copy-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .copy-demo-tip-textarea {
    width: 100%;
    min-height: 70px;
    border: none;
    border-radius: 6px;
    background: #fff;
    resize: none;
    padding: 8px;
    font-size: 10px;
    color: #222;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #94bfff;
  }
  .copy-demo-tip-textarea:disabled {
    color: #94bfff;
    background: #fff;
  }
  .copy-demo-tip-btn {
    width: 100%;
    height: 20px;
    background: #6aa1ff;
    border: none;
    border-radius: 6px;
    color: #fff;
    font-size: 10px;
    cursor: pointer;
    margin-top: 4px;
    transition: background 0.2s;
  }
  .copy-demo-tip-btn:hover {
    background: #6aa1ff;
    color: #fff;
  }
</style>
