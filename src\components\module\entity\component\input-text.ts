import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { DefineComponent } from 'vue';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import {
  ComponentUnifiedAttributeModel,
  KVLModel,
} from '@/components/module/type/attribute-model-unified';
import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
import { ServiceEnumModuleStructType } from '@/components/module/service/service-enum-module-struct-type';
import { ConvertUtils } from '@/utils/convert';
import useComponentStore from '@/components/module/store/component';
import { ValidateUtils } from '@/utils/validate';
import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
import { UploadUtils } from '@/components/module/tools/upload-utils';

export class CustomInputTextModule implements ICustomComponent {
  /**  模块标题 */
  private readonly _moduleName: string = '输入框';

  /**  模块结构类型 */
  private _structType: EnumComponentStructType = EnumComponentStructType.TEXT;

  /**  模块格式类型 */
  private _formatType: EnumComponentFormatType =
    EnumComponentFormatType.InputText;

  /**  模块显示模版实例 */
  private readonly _showModuleRef: DefineComponent | undefined;

  /**  模块设置模版实例 */
  private readonly _settingModuleRef: DefineComponent | undefined;

  /**  模块属性 */
  private readonly _moduleExpandAttribute: ComponentUnifiedAttributeModel;

  /** 构造函数 */
  constructor(attribute: ComponentUnifiedAttributeModel) {
    // 判断结构是否正确
    if (
      attribute.type !== EnumComponentStructType.TEXT &&
      attribute.type !== EnumComponentStructType.TEXTAREA
    ) {
      throw new Error('组件结构类型错误,应使用TEXT类型');
    }
    this._structType = attribute.type;
    // 判断格式是否正确
    if (!ServiceEnumModuleFormatType.ValidateInputType(attribute.format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    this._formatType = attribute.format;

    // 获取显示组件实例
    this._showModuleRef = ServiceEnumModuleStructType.GetShowModuleComponent(
      this._structType
    );
    // 获取设置组件实例
    this._settingModuleRef =
      ServiceEnumModuleStructType.GetSettingModuleComponent(this._structType);

    // 赋值属性
    this._moduleExpandAttribute = ConvertUtils.DeepCopy(attribute);

    // 校验属性
    this.validateParams();
  }

  /**
   * 验证参数
   */
  private validateParams = (): void => {
    if (this._moduleExpandAttribute.maxLength) {
      const dMinLength = this._moduleExpandAttribute.minLength ?? 0;
      if (this._moduleExpandAttribute.maxLength < dMinLength) {
        this._moduleExpandAttribute.maxLength = dMinLength;
      }
    }
    if (this._moduleExpandAttribute.max) {
      const dMinCount = this._moduleExpandAttribute.min ?? 0;
      if (this._moduleExpandAttribute.max < dMinCount) {
        this._moduleExpandAttribute.min = dMinCount;
      }
    }
  };

  public getComponentStructType(): EnumComponentStructType {
    return this._structType;
  }

  /**  获取模块名称 */
  public getComponentName(): string {
    return this._moduleName;
  }

  /**  获取模块类型 */
  public getComponentType(): EnumComponentFormatType {
    return this._formatType;
  }

  /**  获取模块显示页面实例 */
  public getShowModuleRef(): DefineComponent | undefined {
    return this._showModuleRef;
  }

  /**  获取模块设置页面实例 */
  public getSettingModuleRef(): DefineComponent | undefined {
    return this._settingModuleRef;
  }

  /**  获取模块属性 */
  public getComponentUnifiedAttribute(): ComponentUnifiedAttributeModel {
    return this._moduleExpandAttribute;
  }

  private async getDifferentTypeData(type: EnumComponentStructType) {
    await useComponentStore()
      .getMetaComponent(type)
      .then((res: ComponentUnifiedAttributeModel | undefined) => {
        if (res) {
          // 创建一个新的自定义文本输入模块，用于展示只读内容
          Object.assign(this._moduleExpandAttribute, res);
        } else {
          throw new Error('无法找到TEXTAREA元组件属性信息');
        }
      });
  }

  /**
   * 设置模块结构模型
   * @param type
   */
  public setModuleStructType(type: EnumComponentStructType) {
    if (
      type !== EnumComponentStructType.TEXT &&
      type !== EnumComponentStructType.TEXTAREA
    ) {
      throw new Error('模板类型错误');
    }
    const format = type as unknown as EnumComponentFormatType;
    if (!ServiceEnumModuleFormatType.ValidateInputType(format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }

    this._structType = type;

    this._formatType = format;

    this._moduleExpandAttribute.format = format;
    this._moduleExpandAttribute.type = type;
    this.getDifferentTypeData(type);
  }

  /**
   * 获取输入模块属性 -> 单属性
   * @param key
   * @param value
   */
  public setComponentExpandAttribute(key: string, value: any) {
    // 验证属性是否存在
    if (!(key in this._moduleExpandAttribute)) {
      throw new Error(`模块属性 ${key} 不存在`);
    }
    const typeKey = typeof (this._moduleExpandAttribute as any)[key];
    // 验证属性类型
    if (typeKey !== 'undefined' && typeof value !== typeKey) {
      throw new TypeError(`模块属性 ${key} 的类型与值不匹配`);
    }
    // 设置属性值
    (this._moduleExpandAttribute as any)[key] = ConvertUtils.DeepCopy(value);
  }

  // eslint-disable-next-line class-methods-use-this
  public getComponentValueAttribute(): string {
    return this._moduleExpandAttribute.value ?? '';
  }

  /**
   * 设置模块格式类型
   * @param format
   */
  public setComponentFormatType(format: EnumComponentFormatType) {
    if (!ServiceEnumModuleFormatType.ValidateInputType(format)) {
      throw new Error('组件格式类型错误,应使用输入类型');
    }
    let type = format as unknown as EnumComponentStructType;
    if (type !== EnumComponentStructType.TEXTAREA) {
      type = EnumComponentStructType.TEXT;
    }

    this._structType = type;

    this._moduleExpandAttribute.type = type;

    this.getDifferentTypeData(type).then(() => {
      this._formatType = format;
      this._moduleExpandAttribute.format = format;
    });
  }

  private verifyFormatType(): string[] {
    return ServiceEnumModuleFormatType.ValidateInputType(this._formatType)
      ? []
      : ['format'];
  }

  public getComponentExampleAttribute(): ShowComponentUploadFileItemModel[] {
    return ValidateUtils.validateAndMapArray<
      string,
      ShowComponentUploadFileItemModel
    >(
      this._moduleExpandAttribute.exampleImages,
      [ValidateUtils.isString],
      undefined,
      UploadUtils.convertStringToShowComponentUploadFileItem
    );
  }

  /**
   * 设置示例图片
   * @param exampleImages
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentExampleImages(exampleImages: string[] | undefined): void {
    this._moduleExpandAttribute.exampleImages =
      ValidateUtils.validateAndMapArray<
        ShowComponentUploadFileItemModel,
        string
      >(
        exampleImages,
        [UploadUtils.isShowComponentUploadFileItemModel],
        [UploadUtils.canSavedConditionWithShowComponentUploadFileItem],
        UploadUtils.convertShowComponentUploadFileItemToString
      );
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyExampleImages(): string[] {
    return [];
  }

  /**
   * 获取文件最大大小限制
   * @param fileMaxSize
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentFileMaxSize(fileMaxSize: number | undefined): void {
    throw new Error('该类型组件没有【文件大小限制】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileMaxSize(): string[] {
    return [];
  }

  /**
   * 获取文件类型限制
   * @param fileTypes
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentFileTypes(fileTypes: string[] | undefined): void {
    throw new Error('该类型组件没有【文件类型】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyFileTypes(): string[] {
    return [];
  }

  /**
   * 获取数量统一限制
   * @param length
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentLength(length: number | undefined): void {
    throw new Error('该类型组件没有【长度/数量限制】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyLength(): string[] {
    return [];
  }

  /**
   * 获取数量最大限制
   * @param max
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMax(max: number | undefined): void {
    const { min } = this._moduleExpandAttribute;

    this._moduleExpandAttribute.max = Math.min(
      Math.max(min ?? 0, max ?? 0),
      ActivitySettingLimitValue.activityModuleInputValueMax
    );
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMax(): string[] {
    const { max, min } = this._moduleExpandAttribute;

    // 否则验证 max 是否大于等于 min（若存在）
    return max == null || max >= (min ?? 0) ? [] : ['max'];
  }

  /**
   * 设置组件最大长度
   * @param maxLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMaxLength(maxLength: number | undefined): void {
    const { minLength } = this._moduleExpandAttribute;

    this._moduleExpandAttribute.maxLength = Math.min(
      Math.max(minLength ?? 0, maxLength ?? 0),
      ActivitySettingLimitValue.activityModuleInputValueMax
    );
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMaxLength(): string[] {
    const { maxLength, minLength } = this._moduleExpandAttribute;

    // 否则验证 max 是否大于等于 min（若存在）
    return maxLength == null || maxLength >= (minLength ?? 0)
      ? []
      : ['maxLength'];
  }

  // eslint-disable-next-line class-methods-use-this
  public setComponentMin(min: number | undefined): void {
    const { max } = this._moduleExpandAttribute;
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const safeMax = ValidateUtils.isValuePositiveNumber(max) ? max! : 0;
    this._moduleExpandAttribute.min = Math.min(min ?? 0, safeMax);
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMin(): string[] {
    const { max, min } = this._moduleExpandAttribute;
    const safeMax = ValidateUtils.isValuePositiveNumber(max) ? max! : 0;
    // 否则验证 max 是否大于等于 min（若存在）
    return min == null || safeMax >= (min ?? 0) ? [] : ['min'];
  }

  /**
   * 设置组件最小长度
   * @param minLength
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentMinLength(minLength: number | undefined): void {
    const { maxLength } = this._moduleExpandAttribute;
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const safeMaxLength = ValidateUtils.isValuePositiveNumber(maxLength)
      ? maxLength!
      : 0;
    this._moduleExpandAttribute.minLength = Math.min(
      minLength ?? 0,
      safeMaxLength
    );
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyMinLength(): string[] {
    const { maxLength, minLength } = this._moduleExpandAttribute;
    const safeMaxLength = ValidateUtils.isValuePositiveNumber(maxLength)
      ? maxLength!
      : 0;
    // 否则验证 max 是否大于等于 min（若存在）
    return minLength == null || safeMaxLength >= (minLength ?? 0)
      ? []
      : ['minLength'];
  }

  /**
   * 设置组件选项
   * @param options
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentOptions(options: KVLModel[] | undefined): void {
    throw new Error('该类型组件没有【组件选项】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOptions(): string[] {
    return [];
  }

  /**
   * 设置组件是否开启图片原图上传
   * @param originalImage
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentOriginalImage(originalImage: boolean | undefined): void {
    this._moduleExpandAttribute.originalImage = originalImage;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyOriginalImage(): string[] {
    return [];
  }

  /**
   * 设置组件的placeholder
   * @param placeholder
   */
  // eslint-disable-next-line class-methods-use-this,no-shadow
  public setComponentPlaceholder(placeholder: string | undefined): void {
    this._moduleExpandAttribute.placeholder = placeholder;
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyPlaceholder(): string[] {
    return [];
  }

  /**
   * 设置组件的图片是否可重命名
   * @param rename
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentRename(rename: boolean | undefined): void {
    throw new Error('该类型组件没有【重命名】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyRename(): string[] {
    return [];
  }

  /**
   * 设置组件的模版
   * @param templates
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentTemplates(templates: KVLModel[] | undefined): void {
    throw new Error('该类型组件不可设置【模版】属性.');
  }

  // eslint-disable-next-line class-methods-use-this
  private verifyTemplates(): string[] {
    return [];
  }

  /**
   * 设置组件的值
   * @param value
   */
  // eslint-disable-next-line class-methods-use-this
  public setComponentValue(value: unknown): void {
    this._moduleExpandAttribute.value =
      ValidateUtils.isString(value) || ValidateUtils.isNumber(value)
        ? value.toString()
        : undefined;
  }

  /**
   * 校验组件的值的有效性
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  private verifyValue(): string[] {
    if (ValidateUtils.isString(this._moduleExpandAttribute.value)) {
      const valueLength = this._moduleExpandAttribute.value.trim().length;
      const { minLength, maxLength } = this._moduleExpandAttribute;
      return valueLength >= (minLength ?? 0) &&
        valueLength <=
          (maxLength ?? ActivitySettingLimitValue.activityModuleInputValueMax)
        ? []
        : ['value'];
    }
    if (ValidateUtils.isNumber(this._moduleExpandAttribute.value)) {
      const valueNumber = this._moduleExpandAttribute.value;
      const { min, max } = this._moduleExpandAttribute;
      return ValidateUtils.isValuePositiveNumber(valueNumber) &&
        valueNumber >= (min ?? 0) &&
        valueNumber <=
          (max ?? ActivitySettingLimitValue.activityModuleInputValueMax)
        ? []
        : ['value'];
    }
    if (this._moduleExpandAttribute.value == null) {
      return [];
    }
    return ['value'];
  }

  /**
   * 清理组件值属性的值，将无效数据清理出去
   * @private
   */
  // eslint-disable-next-line class-methods-use-this
  private cleanValue(): void {}

  public verifyComponentAttribute(): string[] {
    const attr = this._moduleExpandAttribute;

    if (!attr) {
      console.warn('Module expand attribute is not defined.');
      return ['attr'];
    }

    return [
      ...this.verifyFormatType(),
      ...this.verifyPlaceholder(),
      ...this.verifyMaxLength(),
      ...this.verifyMinLength(),
      ...this.verifyLength(),
      ...this.verifyMax(),
      ...this.verifyMin(),
      ...this.verifyFileMaxSize(),
      ...this.verifyFileTypes(),
      ...this.verifyOptions(),
      ...this.verifyOriginalImage(),
      ...this.verifyRename(),
      ...this.verifyValue(),
      ...this.verifyTemplates(),
      ...this.verifyExampleImages(),
    ];
  }
}
