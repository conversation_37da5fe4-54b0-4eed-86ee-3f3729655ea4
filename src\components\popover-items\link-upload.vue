<template>
  <div class="upload-demo-tip-card">
    <div class="upload-demo-tip-title">链接上传</div>
    <div class="upload-demo-tip-desc">
      用于收集各类型链接，如舞台报名时可要求用户上传表演视频链接或bgm链接，自由行等其他宣发活动报名时可要求用户上传抖音、小红书发布的链接等。在审核时可通过链接快速下载、查看其链接内容。
    </div>
    <div class="upload-demo-tip-example">
      <div class="upload-demo-tip-input demo-first"></div>
      <input class="link-demo-tip-input" disabled placeholder="https://" />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .upload-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .upload-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .upload-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .upload-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .upload-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
  .link-demo-tip-input {
    width: 100%;
    min-height: 22px;
    border: none;
    border-radius: 6px;
    background: #fff;
    resize: none;
    padding: 0 0 0 12px;
    font-size: 12px;
    color: #222;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #94bfff;
  }
  .link-demo-tip-input:disabled {
    color: #94bfff;
    background: #fff;
  }
</style>
