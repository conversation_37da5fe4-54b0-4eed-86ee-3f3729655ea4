<template>
  <div class="video-demo-tip-card">
    <div class="video-demo-tip-title">视频上传</div>
    <div class="video-demo-tip-desc">
      用于收集用户提交的视频文件，如：舞台报名时提交的表演样片，自由行报名时上传的星选官视频等。
    </div>
    <div class="video-demo-tip-example">
      <div class="video-demo-tip-input demo-first"></div>
      <div class="video-demo-tip-upload">
        <icon-upload :style="{ color: '#94BFFF', fontSize: '20px' }" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .video-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .video-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .video-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .video-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .video-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
  .video-demo-tip-upload {
    width: 100%;
    min-height: 50px;
    border: 1.5px solid #94bfff;
    border-radius: 6px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
