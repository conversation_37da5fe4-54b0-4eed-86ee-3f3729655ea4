<script setup lang="ts">
  import { computed, defineProps, PropType } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import {
    KVLModel,
    LVModel,
  } from '@/components/module/type/attribute-model-unified';
  import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';

  const props = defineProps({
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    // 是否可编辑
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  const itemStruct = computed(() => props.item?.getComponentStructType());

  const itemOptions = computed(
    () => props.item?.getComponentUnifiedAttribute().options
  );
</script>

<template>
  <div v-if="item" class="activity-radio">
    <div class="body">
      <template
        v-for="(optionItem, index) in itemOptions as KVLModel[]"
        :key="index"
      >
        <div class="item">
          <div
            v-if="itemStruct === EnumComponentStructType.RADIO"
            class="radio"
          />
          <div v-else class="checkbox" />
          <div class="label">
            <a-input
              v-model="optionItem.label"
              :disabled="!props.enabled"
              :max-length="
                ActivitySettingLimitValue.activityModuleSingleMultipleLabelMaxLength
              "
              show-word-limit
            />
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
  .activity-radio {
    display: flex;
    align-items: center;

    .body {
      display: flex;
      padding: 5px 0;
      align-items: center;
      flex-direction: column;
      gap: 24px;
      align-self: stretch;
      width: 100%;

      .item {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .radio {
          width: 14px;
          height: 14px;
          border-radius: 7px;
          border: 2px solid #e5e6eb;
          background: #fff;
        }
        .checkbox {
          width: 14px;
          height: 14px;
          border: 2px solid #e5e6eb;
          background: #fff;
        }

        .label {
          color: #1d2129;
          /* 14/CN-Regular */
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          width: 100%;
        }
      }
    }
  }
</style>
