<template>
  <div class="activity-input">
    <div class="body">
      <span>{{ placeholder }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
  defineProps({
    placeholder: String,
  });
</script>

<style scoped lang="less">
  .activity-input {
    width: 100%;
    display: flex;
    padding: 5px 12px;
    align-items: center;
    flex: 1 0 0;
    border-radius: 2px;
    background: var(--bg-white, #fff);

    .body {
      width: 100%;

      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1 0 0;
      color: var(--text-3, #86909c);
      /* 14/CN-Regular */
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 157.143% */

      span {
        height: 22px;
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 添加省略号 */
      }
    }
  }
</style>
