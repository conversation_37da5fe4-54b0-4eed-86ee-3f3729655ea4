import { DefineComponent } from 'vue';
import { MapEnumModuleStructTypeComponent } from '@/components/module/maps/map-enum-module-struct-type-component';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';

/**
 * 控件-组件实例 服务
 */
export const ServiceEnumModuleStructType = {
  /** 获取模块显示组件实例 */
  GetShowModuleComponent: (
    type?: EnumComponentStructType
  ): DefineComponent | undefined => {
    if (!type) {
      throw new Error(`未定义显示组件类型！`);
    }
    const loader =
      MapEnumModuleStructTypeComponent.MapOfShowComponent.get(type);
    if (!loader) {
      throw new Error(`显示模块 ${type} 未注册`);
    }
    return loader;
  },
  /** 获取模块设置组件实例 */
  GetSettingModuleComponent: (
    type?: EnumComponentStructType
  ): DefineComponent | undefined => {
    if (!type) {
      throw new Error(`未定义设置组件类型！`);
    }
    const loader =
      MapEnumModuleStructTypeComponent.MapOfSettingComponent.get(type);
    if (!loader) {
      throw new Error(`设置模块 ${type} 未注册`);
    }
    return loader;
  },
};
