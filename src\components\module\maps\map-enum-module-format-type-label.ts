import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';

/**
 * 输入格式类型 - 名称映射
 */
export const MapEnumModuleFormatTypeLabel = {
  /**
   * 输入格式类型-输入格式类型标签
   * 单行文本、多行文本、数字文本、邮箱、身份证号、链接、手机、QQ
   */
  MapOfInputFormatTypeLabel: new Map<EnumComponentFormatType, string>([
    [EnumComponentFormatType.InputText, '单行文本'],
    [EnumComponentFormatType.InputArea, '多行文本'],
    [EnumComponentFormatType.InputNumber, '数字文本'],
    [EnumComponentFormatType.InputEmail, '电子邮箱'],
    [EnumComponentFormatType.InputIDCard, '身份证号码'],
    [EnumComponentFormatType.InputLink, '链接'],
    [EnumComponentFormatType.InputMobile, '手机号码'],
    [EnumComponentFormatType.InputQQ, 'QQ号码'],
    [EnumComponentFormatType.InputName, '姓名'],
    [EnumComponentFormatType.InputAreaCopy, '复制'],
  ]),
  /**
   * 选择类型-选择格式类型标签
   * 单选、多选
   */
  MapOfSingleMultipleFormatTypeLabel: new Map<EnumComponentFormatType, string>([
    [EnumComponentFormatType.CheckBox, '多选'],
    [EnumComponentFormatType.Radio, '单选'],
  ]),
  /**
   * 上传类型-上传格式类型标签
   * 上传文件、上传图片、上传视频
   */
  MapOfUploadFormatTypeLabel: new Map<EnumComponentFormatType, string>([
    [EnumComponentFormatType.UploadFile, '文件上传'],
    [EnumComponentFormatType.Image, '官方图片文件上传'],
    // [EnumModuleFormatType.UploadVideo, '视频文件上传'],
  ]),
  /**
   * 时间类型-时间格式类型标签
   * 日期、时间、日期时间
   */
  MapOfDateFormatTypeLabel: new Map<EnumComponentFormatType, string>([
    // [EnumComponentFormatType.Date, '日期'],
    [EnumComponentFormatType.DateTime, '日期时间'],
    // [EnumComponentFormatType.Time, '时间'],
  ]),
  /**
   * 时间范围类型-时间格式类型标签
   * 日期范围、时间范围、日期时间范围
   */
  MapOfDateRangeFormatTypeLabel: new Map<EnumComponentFormatType, string>([
    [EnumComponentFormatType.DateRange, '日期范围'],
    [EnumComponentFormatType.DateTimeRange, '日期时间范围'],
    [EnumComponentFormatType.TimeRange, '时间范围'],
  ]),
};
