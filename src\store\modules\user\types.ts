export type RoleType = '' | '*' | 'admin' | 'user';
export interface UserState {
  name?: string;
  avatar?: string;
  job?: string;
  organization?: string;
  location?: string;
  email?: string;
  introduction?: string;
  personalWebsite?: string;
  jobName?: string;
  organizationName?: string;
  locationName?: string;
  phone?: string;
  registrationDate?: string;
  accountId?: string;
  certification?: number;
  role: RoleType;
}

export interface UserStoreDataInfoModel {
  user: UserStore_UserInfoModel;
  auth?: UserStore_AuthInfoModel[];
}


export interface UserStore_UserInfoModel {
  /**
   * 商户用户id
   */
  proprietorId: string,
  /**
   * 商户用户名称
   */
  proprietorName: string,
  /**
   * 商户用户手机号
   */
  mobile: string,
  /**
   * 商户用户创建时间
   */
  createTime: string,
  /**
   * 商户id
   */
  merchantId: string,
  /**
   * 商户名称
   */
  principalName: string

  /**
   * 商户用户授权商户数量
   */
  merchantRoleKey: string;
}


export interface UserStore_AuthInfoModel {};