/**
 * 控件格式类型 枚举
 */
// eslint-disable-next-line no-shadow
export enum EnumComponentFormatType {
  /** 字符串输入框 */
  InputText = 'text',

  /** 文本输入框 */
  InputArea = 'textarea',

  /** 数字输入框 */
  InputNumber = 'number',

  /** 邮箱输入框 */
  InputEmail = 'email',

  /** 身份证输入框 */
  InputIDCard = 'identity',

  /** QQ输入框 */
  InputQQ = 'qq',

  /** 手机输入框 */
  InputMobile = 'mobile',

  /** 链接输入框 */
  InputLink = 'link',

  /** 姓名输入框 */
  InputName = 'name',

  /** 文本输入框 */
  InputAreaCopy = 'copy',

  /** 单选 */
  Radio = 'radio',

  /** 复选/多选 */
  CheckBox = 'checkbox',

  /** 图片展示 */
  Image = 'image',

  /** 文件上传 */
  UploadFile = 'file',

  /** 图像文件上传 */
  UploadImage = 'file-image',

  /** 视频文件上传 */
  UploadVideo = 'file-video',

  /** 日期时间选择 */
  DateTime = 'dateTime',

  /** 日期选择 yyyy-MM-dd HH:mm */
  Date = 'date',

  /** 时间选择 */
  Time = 'time',

  /** 日期时间选择 */
  DateTimeRange = 'dateTimeRange',

  /** 日期选择 */
  DateRange = 'dateRange',

  /** 时间选择 */
  TimeRange = 'timeRange',

  /** 下拉选择 */
  Select = 'select',

  /** 列表 */
  List = 'list',
}
