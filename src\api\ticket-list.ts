import axios from 'axios';
import qs from 'query-string';

export interface InvitationTicketQueryParams {
  merchantId?: string;
  goodsName?: string;
  goodsItemName?: string;
  providedStartTime?: string;
  providedEndTime?: string;
  page?: number;
  limit?: number;
}

export interface InvitationTicketItem {
  merchantInvitationId: string;
  goodsName: string;
  goodsItemName: string;
  analysisTotalCount: number;
  analysisFailCount: number;
  analysisSuccessCount: number;
  ticketProvidedCount: number;
  createTime: string;
  operationName: string;
  // 其他字段可根据实际返回补充
}

export interface InvitationTicketListResult {
  page: number;
  limit: number;
  total: number;
  list: InvitationTicketItem[];
}

export interface InvitationTicketApiResponse {
  code: string;
  message: string;
  data: InvitationTicketListResult;
  [key: string]: any;
}

/**
 * 查询邀请票列表
 * @param params 查询参数
 */
export function queryInvitationTicketList(params: InvitationTicketQueryParams) {
  return axios
    .get<InvitationTicketApiResponse>('/api/pd/v2/merchant/invitation/page', {
      params,
      paramsSerializer: (obj: Record<string, any>) => qs.stringify(obj),
    })
    .then((response) => {
      // Return the response data directly
      return response.data;
    })
    .catch((error) => {
      console.error('Error fetching invitation ticket list:', error);
      throw error;
    });
}

export function querySpecificTicket(merchantInvitationId: String) {
  return axios.get<InvitationTicketApiResponse>(
    `/api/pd/v2/merchant/invitation/detail/${merchantInvitationId}`
  );
}
