<template>
  <div class="costume-demo-tip-card">
    <div class="costume-demo-tip-title">着装登记</div>
    <div class="costume-demo-tip-desc">
      专为自由行活动设计，添加后可在用户选择日期时收集其当日着装/摄影设备等信息，同时也支持收集当日着装照片，便于审核时快速查看其着装
      着装信息讲会登记在通过用户的门票信息内，便于现场审核核验。
    </div>
    <div class="costume-demo-tip-example">
      <div class="costume-demo-tip-input demo-first"></div>
      <input
        class="costume-demo-tip-inputbox"
        disabled
        placeholder="出席IP-出席角色"
      />
      <div class="costume-demo-tip-upload">
        <icon-upload :style="{ color: '#94BFFF', fontSize: '20px' }" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .costume-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .costume-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .costume-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .costume-demo-tip-example {
    background: #e8f3ff;
    border-radius: 12px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .costume-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
  .costume-demo-tip-inputbox {
    width: 100%;
    min-height: 22px;
    border: none;
    border-radius: 6px;
    background: #f7fbff;
    resize: none;
    padding: 0 0 0 12px;
    font-size: 12px;
    color: #222;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #94bfff;
    margin-bottom: 4px;
  }
  .costume-demo-tip-inputbox:disabled {
    color: #94bfff;
    background: #f7fbff;
  }
  .costume-demo-tip-upload {
    width: 100%;
    min-height: 48px;
    border: 1.5px solid #94bfff;
    border-radius: 6px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
