import {
  ShowComponentUploadFileItemModel,
  ShowComponentUploadProgressModel,
} from '@/components/module/type/show/activity-upload-component-type';
import { ValidateUtils } from '@/utils/validate';
import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';
import { LVModel } from '@/components/module/type/attribute-model-unified';

export class UploadUtils {
  /**
   * 守卫模型 - 判断是否是上传进度模型
   * @param progress
   */
  public static isShowComponentUploadProgressModel = (
    progress: any
  ): progress is ShowComponentUploadProgressModel => {
    return (
      ValidateUtils.isObject(progress) &&
      'status' in progress &&
      typeof progress.status === 'string' &&
      'percent' in progress &&
      typeof progress.percent === 'number'
    );
  };

  /**
   * 判断一个对象是否满足 { label: string }
   * @param item
   * @private
   */
  private static objectHasLabel(item: any): item is { label: string } {
    return 'label' in item && typeof item.label === 'string';
  }

  /**
   * 判断一个对象是否满足 { url: string }
   * @param item
   * @private
   */
  private static objectHasUrl(item: any): item is { url: string } {
    return 'url' in item && typeof item.url === 'string';
  }

  /**
   * 判断一个对象是否满足 { progress: ShowComponentUploadProgressModel }
   * @param item
   * @private
   */
  private static objectHasProgress(
    item: any
  ): item is { progress: ShowComponentUploadProgressModel } {
    return (
      'progress' in item &&
      this.isShowComponentUploadProgressModel(item.progress)
    );
  }

  /**
   * 守卫模型 - 判断是否是上传文件项
   * @param item
   */
  public static isShowComponentUploadFileItemModel = (
    item: any
  ): item is ShowComponentUploadFileItemModel => {
    return (
      ValidateUtils.isObject(item) &&
      this.objectHasLabel(item) &&
      this.objectHasUrl(item) &&
      this.objectHasProgress(item)
    );
  };

  /**
   * 条件方法 - 判断是否可以暂存
   * @param item
   */
  public static canSavedConditionWithShowComponentUploadFileItem = (
    item: ShowComponentUploadFileItemModel
  ): boolean =>
    item.progress.status === EnumFileUploadStatue.Success ||
    item.progress.status === EnumFileUploadStatue.Empty;

  /**
   * 条件方法 - 判断是否可以记录-有效存储
   * @param item
   */
  public static canRecordedConditionWithShowComponentUploadFileItem = (
    item: ShowComponentUploadFileItemModel
  ) => item.progress.status === EnumFileUploadStatue.Success;

  /**
   * 转换方法 - 转换为LVModel
   * @param item
   */
  public static convertShowComponentUploadFileItemToLVModel = (
    item?: ShowComponentUploadFileItemModel
  ): LVModel =>
    item
      ? {
          label: item.label,
          value:
            item.progress.status === EnumFileUploadStatue.Success
              ? item.url
              : '',
        }
      : {
          label: '',
          value: '',
        };

  /**
   * 转换方法 - ShowComponentUploadFileItemModel 转换为 string
   * @param item
   */
  public static convertShowComponentUploadFileItemToString = (
    item?: ShowComponentUploadFileItemModel
  ): string => (item ? item.url : '');

  /**
   * 转换方法 - LVModel 转换为 ShowComponentUploadFileItemModel
   * @param item
   */
  public static convertLVModelToShowComponentUploadFileItem = (
    item?: LVModel
  ): ShowComponentUploadFileItemModel => {
    const progress: ShowComponentUploadProgressModel = {
      status: EnumFileUploadStatue.Empty,
      percent: 0,
    };
    if (!item) {
      return {
        label: '',
        url: '',
        progress,
      };
    }
    return {
      label: item.label,
      url: item.value,
      progress: ValidateUtils.isNullOrEmpty(item.value)
        ? progress
        : {
            status: EnumFileUploadStatue.Success,
            percent: 100,
          },
    };
  };

  /**
   * 转换方法 - string 转换为 ShowComponentUploadFileItemModel
   * @param item
   */
  public static convertStringToShowComponentUploadFileItem = (
    item?: string
  ): ShowComponentUploadFileItemModel => {
    if (ValidateUtils.isNotEmpty(item)) {
      return {
        label: '',
        url: item,
        progress: {
          status: EnumFileUploadStatue.Success,
          percent: 1,
        },
      };
    }
    return {
      label: '',
      url: '',
      progress: {
        status: EnumFileUploadStatue.Empty,
        percent: 0,
      },
    };
  };

  /**
   * 验证方法 - 验证暂存的上传文件项，值是否有空
   * @param item
   */
  public static uploadValueAttrValidator = (item: LVModel) => {
    return ValidateUtils.isNotEmpty(item.value);
  };

  /**
   * 清空方法 - 清空上传文件项的label
   * @param item
   */
  public static uploadValueLabelClean = (item: LVModel): LVModel => {
    return {
      label: '',
      value: item.value,
    };
  };
}
