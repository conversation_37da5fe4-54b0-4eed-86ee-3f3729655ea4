import axios from 'axios';

export interface BaseInfoModel {
  goodsId: string;
  activityName: string;
  recruitmentDate: string[];
  registrationDate: string[];
  publicationDate: string;
  mainImage: string;
  showImage: string;
}

export interface RoleBaseInfoTextModel {
  placeholder?: string;
  model?: any;
  maxLength?: number;
  rule?: string;
}

export interface RoleBaseInfoRadioModel {
  options?: any[];
  model?: any;
  rule?: string;
  number?: number;
}

export interface RoleBaseInfoCheckBoxModel {
  options?: any[];
  model?: any;
  rule?: string;
  number?: number;
  min: number;
  max: number;
}

export interface RoleBaseInfoDateModel {
  placeholder?: string;
  model?: any;
  rule?: string;
}

export interface RoleBaseInfoModel {
  uuid: string;
  key: string;
  label: string;
  desc: string;
  descEnable: boolean;
  type: string;
  required: boolean;
  value: any;
}

export interface RoleTaskPlatformModel {
  key: string;
  label: string;
  value: any;
}

export interface RoleTaskModel {
  role: string;
  baseInfo: RoleBaseInfoModel[];
  task: RoleTaskPlatformModel[];
}

export type UnitChannelModel = BaseInfoModel &
  RoleTaskModel &
  RoleBaseInfoModel &
  RoleTaskPlatformModel &
  RoleBaseInfoTextModel;

export function submitChannelForm(data: UnitChannelModel) {
  return axios.post('/api/channel-form/submit', { data });
}

export function getUserList(params: { current: number; pageSize: number }) {
  return axios.get('/api/form/step/user-list', { params });
}
