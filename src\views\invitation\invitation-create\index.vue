<template>
  <div class="container">
    <Breadcrumb :items="['邀请票订单管理', '新建发放卷码']" />
    <a-card class="general-card">
      <template #title>
        <div class="title-block">新建发放券码</div>
      </template>

      <a-form
        :model="formData"
        style="width: 1000px; margin: auto"
        :rules="rules"
        auto-label-width
      >
        <a-form-item field="goodsId" label="商品名称" style="width: 640px">
          <a-select
            v-model="formData.goodsId"
            placeholder="选择需要发券的商品"
            allow-clear
            :disabled="goodsChooseDisable"
          >
            <a-option
              v-for="(item, index) in goodsOption"
              :key="index"
              :value="item.goodsId"
              >{{ item.goodsName }}</a-option
            >
          </a-select>
        </a-form-item>
        <a-form-item field="ticketName" label="卷名称" style="width: 640px">
          <a-input
            v-model="formData.ticketName"
            placeholder="为发放的券码取个名字吧，例：邀请函、特邀嘉宾"
          />
        </a-form-item>

        <a-form-item field="uploadFile" label="上传发放用户信息">
          <a-space direction="vertical" fill>
            <a-button
              type="outline"
              class="down-template"
              @click="handleDownTemplateFile"
            >
              <template #icon>
                <icon-download size="16" />
              </template>
              导入文件模版下载
            </a-button>
            <a-upload
              ref="upLoadRef"
              v-model="formData.uploadFile"
              draggable
              action="/"
              :custom-request="customRequest"
              :show-remove-button="false"
              :show-cancel-button="false"
              :show-file-list="false"
            >
              <template #upload-button>
                <div class="upload-panel">
                  <p class="ant-upload-drag-icon">
                    <a-image
                      class="carousel-image"
                      :src="excelPng"
                      :preview="false"
                      width="40"
                    />
                  </p>
                  <div class="content">
                    <p class="c-text">直接拖转文件至此</p>
                    <p class="c-text c-mid">或</p>
                    <p class="c-text c-button">点击上传本地文件</p>
                  </div>
                </div>
              </template>
            </a-upload>
            <div class="info-block">
              <p class="i-text">建议您下载标准模版</p>
              <p class="i-text i-mid">Excel文件后缀名.xsl或.xlsx</p>
              <div v-if="nowLoading" class="loading">
                <p class="l_text">解析表格中，请稍后</p>
                <icon-loading size="13" style="color: #4080ff" />
              </div>
            </div>
          </a-space>
        </a-form-item>
        <a-form-item v-if="resultCount > 0">
          <div class="result-block">
            <div class="result-contain">
              已解析成功{{ validExcel.successItemCount }}条记录，预计发{{
                validExcel.whileSendCount
              }}张券码，失败{{ validExcel.failItemCount }}条
            </div>
            <a-table
              :columns="tableColumns"
              :data="resultList"
              :bordered="{ cell: true }"
              :pagination="{
                total: resultCount,
                pageSize: 10,
                showTotal: true,
                showPageSize: true,
              }"
            />
            <a-button type="primary" @click="handleSubmit">发放二维码</a-button>
          </div>
        </a-form-item>
      </a-form>
    </a-card>
    <a-modal
      :visible="modalVisible"
      message-type="warning"
      width="464px"
      @ok="handleOk"
      @cancel="modalVisible = false"
    >
      <template #title> 是否确认发放券码 </template>
      <div class="modal-container-text"
        >发放券码后对应电话号码的漫星账号将会收到对应日期的门票券码，此操作无法撤回！</div
      >
      <div class="modal-container-text"
        >您可以在<span style="font-weight: 500">订单管理-邀请票订单详情</span
        >内查看用户的券码状态或作废其券码。</div
      >
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { computed, nextTick, onMounted, reactive, ref } from 'vue';
  import excelPng from '@/assets/images/excel.png';
  import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { ValidateUtils } from '@/utils/validate';
  import { InvitationAddApi } from '@/api/invitation';
  import {
    ExcelItemModel,
    Response_InvitationExcelInfoModel,
    Response_MerchantValidGoods,
  } from '@/types/api-type/invitation';
  import { Message } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';
  import { useRouter } from 'vue-router';

  interface FormDataModel {
    ticketName: string;
    uploadFile: File[];
    goodsId?: string;
  }

  const formData = reactive<FormDataModel>({
    goodsId: undefined,
    ticketName: '',
    uploadFile: [],
  });

  const rules = {
    goodsId: [{ required: true, message: '请选择一个商品', trigger: 'change' }],
    ticketName: [
      { required: true, message: '请输入卷名称', trigger: 'blur' },
      {
        minLength: 1,
        maxLength: 6,
        message: '长度在 1 到 6 个字符',
        trigger: 'blur',
      },
    ],
    uploadFile: [{ required: true }, { minLength: 0 }],
  };

  const goodsOption = ref<Response_MerchantValidGoods[]>(
    [] as Response_MerchantValidGoods[]
  );

  const nowLoading = ref(false);

  const goodsChooseDisable = ref(false);

  const handleDownTemplateFile = async () => {
    InvitationAddApi.getMerchantInvitationTicketTemplateFile()
      .then((res) => {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', '邀请票模板.xlsx'); // 设置默认下载文件名
        document.body.appendChild(link);
        link.click();
        link.remove(); // 下载完成后移除元素
        window.URL.revokeObjectURL(url); // 释放掉 blob 对象
      })
      .catch((err) => {
        Message.error(ValidateValidateUtils.isNotEmpty(err) ? err : '请求失败');
      });
  };

  const customRequest = async (options: RequestOption) => {
    if (!nowLoading.value) {
      nowLoading.value = true;
      if (!formData.goodsId) {
        alert('请选择一个商品');
        formData.uploadFile.length = 0;
        nowLoading.value = false;
        return;
      }

      const fileName = options.fileItem.name;
      const { file } = options.fileItem;
      if (!file || !fileName) {
        alert('未找到文件');
        formData.uploadFile.length = 0;
        nowLoading.value = false;
        return;
      }
      const fileType = ValidateUtils.getFileExtension(fileName);
      if (!['xls', 'xlsx'].includes(fileType)) {
        alert('只能上传 Excel 文件');
        formData.uploadFile.length = 0;
        nowLoading.value = false;
        return;
      }
      formData.uploadFile.push(file!);

      InvitationAddApi.analysisExcel(formData.goodsId, file)
        .then((res) => {
          goodsChooseDisable.value = true;
          merchantInvitationId.value = res.data.merchantInvitationId;
          validExcel.failItemCount = res.data.analysisFailCount;
          validExcel.successItemCount = res.data.analysisSuccessCount;
          validExcel.whileSendCount = res.data.provideCouponCount;
          resultList.value = res.data.excelDTOS;
          resultList.value.forEach((item: ExcelItemModel) => {
            if (!item.name) {
              item.name = '/';
            }
            if (!item.idCard) {
              item.idCard = '/';
            }
          });
        })
        .finally(() => {
          nowLoading.value = false;
        });
    }
  };

  const tableColumns = [
    {
      title: '用户CN',
      dataIndex: 'cn',
      key: 'cn',
      align: 'left',
      width: 100,
      minWidth: 100,
      ellipsis: true,
    },
    {
      title: '发卷手机号',
      dataIndex: 'mobile',
      key: 'mobile',
      align: 'center',
      width: 140,
      minWidth: 140,
      ellipsis: true,
    },
    {
      title: '发放日期',
      dataIndex: 'signUpDate',
      key: 'signUpDate',
      align: 'right',
      width: 200,
      minWidth: 140,
      ellipsis: true,
    },
    {
      title: '用户姓名(选填)',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 160,
      minWidth: 160,
      ellipsis: true,
    },
    {
      title: '身份证号(选填)',
      dataIndex: 'idCard',
      key: 'idCard',
      align: 'center',
      width: 180,
      minWidth: 160,
    },
  ];

  const resultList = ref<ExcelItemModel[]>([]);
  const resultCount = computed(() => {
    return resultList.value.length;
  });
  const validExcel = reactive({
    successItemCount: 0,
    failItemCount: 0,
    whileSendCount: 0,
  });

  const merchantInvitationId = ref<string>('');

  const modalVisible = ref(false);

  const handleSubmit = () => {
    if (!formData.goodsId) {
      Message.warning('请选择一个商品');
      return;
    }
    if (!formData.ticketName) {
      Message.warning('请输入券名称');
      return;
    }
    if (!merchantInvitationId.value) {
      Message.warning('未有解析完成的excel文件，请选择有效文件上传解析');
      return;
    }
    modalVisible.value = true;
  };
  const router = useRouter();
  const handleOk = () => {
    useLoading(true);
    modalVisible.value = false;
    const query = {
      goodsId: formData.goodsId!,
      merchantInvitationId: merchantInvitationId.value,
      goodsItemName: formData.ticketName,
    };
    InvitationAddApi.submitCreateMerchantInvitationTicket(query)
      .then(() => {
        Message.success('发放成功');
        nextTick(() => {
          router.push({
            path: '/invitation/invitation-list',
          });
        });
      })
      .finally(() => {
        useLoading(false);
      });
  };

  onMounted(() => {
    InvitationAddApi.getMerchantValidGoods()
      .then((res) => {
        goodsOption.value = res.data;
      })
      .finally(() => {
        nowLoading.value = false;
      });
  });
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;

    .title-block {
      font-size: 20px;
      line-height: 28px;
      margin: 10px;
      font-weight: 500;
      font-family: 'PingFang SC';
    }

    .upload-panel {
      width: 323px;
      height: 194px;
      border-radius: 2px;
      background: #f2f3f5;
      border: 1px dashed #e5e6eb;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 50px 32px;

      .down-template {
        width: 148px;
        height: 30px;
        font-size: 14px;
        line-height: 22px;
        font-weight: 500;
        font-family: 'PingFang SC';
      }

      .content {
        margin-top: 20px;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: 30px;

        .c-text {
          font-size: 14px;
          line-height: 22px;
          font-weight: 500;
          color: #4080ff;
          font-family: 'PingFang SC';
        }
        .c-mid {
          margin: 0 3px;
        }
        .c-button {
          width: 124px;
          height: 30px;
          font-size: 14px;
          font-weight: 500;
          font-family: 'PingFang SC';
          border: 1px solid #4080ff;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    .info-block {
      margin-top: -8px;
      .i-text {
        margin: 0;
        font-size: 12px;
        line-height: 20px;
        font-weight: 400;
        font-family: 'PingFang SC';
        color: #86909c;
      }
      .loading {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        height: 36px;
        width: 137px;
        .l_text {
          font-weight: 400;
          font-family: 'PingFang SC';
          font-size: 12px;
          line-height: 20px;
          margin: 8px;
          color: #4080ff;
        }
      }
    }
  }

  .result-block {
    margin-top: -20px;
  }

  .modal-container-text {
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    font-family: 'PingFang SC';
  }

  .result-contain {
    font-weight: 400;
    font-family: 'PingFang SC';
    font-size: 12px;
    line-height: 20px;
    margin-bottom: 8px;
    color: #4e5969;
  }
</style>
