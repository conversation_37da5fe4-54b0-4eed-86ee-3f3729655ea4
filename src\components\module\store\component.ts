import { defineStore } from 'pinia';
import { StoreComponentStateModel } from '@/components/module/store/type';
import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';
import { ValidateUtils } from '@/utils/validate';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import { ComponentTemplateData } from '@/components/module/store/template';

const useComponentStore = defineStore('component', {
  state: (): StoreComponentStateModel => ({
    metaComponentMap: new Map<string, ComponentUnifiedAttributeModel>(),
  }),

  getters: {
    metaComponentList(): Map<string, ComponentUnifiedAttributeModel> {
      return this.metaComponentMap;
    },
  },
  actions: {
    setInfo(partial: Partial<ComponentUnifiedAttributeModel[]>) {
      const map = new Map<string, ComponentUnifiedAttributeModel>();
      if (partial) {
        partial.forEach((item) => {
          if (item && item.type) {
            map.set(item.type, item);
          }
        });
      }
      this.$patch({ metaComponentMap: map });
    },
    /**
     * 获取模组信息
     */
    async getMetaComponentList() {
      if (ValidateUtils.isNullOrEmpty(this.metaComponentList)) {
        const localComponent = localStorage.getItem('component');
        if (ValidateUtils.isNullOrEmpty(localComponent)) {
          // const res = await ModuleApi.getModules();
          // const arr = res.data.map((item) =>
          //   JSON.parse(item!)
          // ) as unknown as AttributeModuleGroup[];
          // this.setInfo(arr);
          // localStorage.setItem('module', JSON.stringify(res.data));

          localStorage.setItem(
            'component',
            JSON.stringify(ComponentTemplateData)
          );
          this.setInfo(ComponentTemplateData);
        } else {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          this.setInfo(JSON.parse(localComponent!));
        }
      }
    },

    async getMetaComponent(
      type: EnumComponentStructType
    ): Promise<ComponentUnifiedAttributeModel | undefined> {
      let componentAttr: ComponentUnifiedAttributeModel | undefined;
      await this.getMetaComponentList().then(() => {
        componentAttr = this.metaComponentMap.get(type);
      });
      return componentAttr;
    },
  },
});

export default useComponentStore;
