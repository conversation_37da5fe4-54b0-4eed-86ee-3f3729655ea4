<template>
  <div class="container">
    <div class="logo">
      <img
        alt="logo"
        src="//p3-armor.byteimg.com/tos-cn-i-49unhts6dw/dfdba5317c0c20ce20e64fac803d52bc.svg~tplv-49unhts6dw-image.image"
      />
      <div class="logo-text">Arco Design Pro</div>
    </div>
    <LoginBanner />
    <div class="content">
      <div class="content-inner">
        <LoginForm v-if="step === 1" @next='stepNext'/>
        <SelectMerchant  v-if="step === 2" :mobile="mobile" @back="stepBack"/>
      </div>
      <div class="footer">
        <Footer />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import Footer from '@/components/footer/index.vue';
  import SelectMerchant from "@/views/login/components/select-merchant.vue";
  import LoginBanner from './components/banner.vue';
  import LoginForm from './components/login-form.vue';
  import { ref } from 'vue';


  const step = ref(1);
  const mobile = ref('')

  const stepNext = (arg:any) => {
    console.log( arg)
    step.value = 2;
    mobile.value = arg.mobile
  }
  const stepBack = () => {
    step.value = 1;
  }
</script>

<style lang="less" scoped>
  .container {
    display: flex;
    height: 100vh;

    .banner {
      width: 550px;
      background: linear-gradient(163.85deg, #1d2129 0%, #00308f 100%);
    }

    .content {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      padding-bottom: 40px;
    }

    .footer {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 100%;
    }
  }

  .logo {
    position: fixed;
    top: 24px;
    left: 22px;
    z-index: 1;
    display: inline-flex;
    align-items: center;

    &-text {
      margin-right: 4px;
      margin-left: 4px;
      color: var(--color-fill-1);
      font-size: 20px;
    }
  }

  .content-inner{
    padding: 40px;
    border-radius: 6px;
    box-shadow: 1px 1px 8px #aaa6a6;
    box-sizing: border-box;
    width: 400px;
  }
</style>

<style lang="less" scoped>
  // responsive
  @media (max-width: @screen-lg) {
    .container {
      .banner {
        width: 25%;
      }
    }
  }
</style>
