/**
 * 导出一个配置对象，用于规范和配置JavaScript代码的格式化标准
 * 这些配置项是针对某代码格式化工具（如Prettier）的设置，旨在保持代码风格的一致性
 */
module.exports = {
  // 设置缩进使用的空格数为2个
  tabWidth: 2,
  // 启用语句末尾的分号
  semi: true,
  // 设置每行打印的字符宽度为80个字符
  printWidth: 80,
  // 使用单引号而不是双引号
  singleQuote: true,
  // 对象属性的引号风格设置为一致
  quoteProps: 'consistent',
  // 设置HTML白空间的敏感度为严格
  htmlWhitespaceSensitivity: 'strict',
  // 对于Vue文件中的<script>和<style>标签，启用缩进
  vueIndentScriptAndStyle: true,
  // 设置尾随逗号的使用规则为ES5语法兼容
  trailingComma: 'es5',
};
