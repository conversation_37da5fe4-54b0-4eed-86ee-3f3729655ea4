<template>
  <div class="role-task">
    <div class="content">
      <div v-if="formData.length == 0" class="add-button">
        <a-button
          type="primary"
          size="large"
          style="width: 100%; height: 54px; font-size: 24px"
          @click="addRoleClick"
        >
          <template #icon>
            <icon-plus />
          </template>
          创建角色
        </a-button>
      </div>
      <div v-else class="body">
        <div class="role-list">
          <div
            v-for="(item, index) in formData"
            :key="index"
            class="role-item"
            :class="index == currentRole ? 'currentRole' : ''"
            @click="roleChange(index)"
          >
            <div class="role-name">{{ item.role }}</div>
            <div @click.stop="roleItemRemove(index)">
              <icon-close size="20" />
            </div>
          </div>
          <div v-if="formData.length < 8" class="role-add">
            <a-button
              type="primary"
              size="large"
              style="
                width: 100%;
                height: 46px;
                font-size: 16px;
                border-radius: 4px;
              "
              @click="addRoleClick"
            >
              <template #icon>
                <icon-plus />
              </template>
              创建新角色({{ formData.length }}/8)
            </a-button>
          </div>
        </div>
        <a-divider type="dashed">以下为角色&任务设置</a-divider>
        <div class="role-task-body">
          <div class="components">
            <a-affix offset-top="80">
              <div class="components-body">
                <div class="components-base">
                  <div class="components-title">基础信息</div>
                  <div class="components-content">
                    <div class="personal—details">
                      <div class="subtitle">个人信息</div>
                      <div class="sub-frame">
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'personal', 'sex')"
                        >
                          性别
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'personal', 'real_name')"
                        >
                          实名信息
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'personal', 'birthday')"
                        >
                          出生日期
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'personal', 'email')"
                        >
                          邮箱
                        </a-button>
                      </div>
                    </div>
                    <div class="general-tools">
                      <div class="subtitle">通用工具</div>
                      <div class="sub-frame">
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'general', 'radio')"
                        >
                          <template #icon>
                            <icon-check-circle />
                          </template>
                          单选
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'general', 'checkBox')"
                        >
                          <template #icon>
                            <icon-check-square />
                          </template>
                          多选
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'general', 'text')"
                        >
                          <template #icon>
                            <icon-file />
                          </template>

                          文本
                        </a-button>
                        <a-button
                          size="small"
                          @click="addItem('baseInfo', 'general', 'upload')"
                        >
                          <template #icon>
                            <icon-link />
                          </template>
                          文件上传
                        </a-button>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="components-task"></div>
              </div>
            </a-affix>
          </div>
          <div class="form">
            <div class="title">
              <div class="text">
                <div>
                  <div v-if="!roleNameEditVisible"
                    >{{ formData[currentRole].role }}
                  </div>
                  <div v-else>
                    <a-input
                      ref="roleNameEditRef"
                      v-model="formData[currentRole].role"
                      show-word-limit
                      :max-length="8"
                      @blur="roleNameEditVisible = false"
                    ></a-input>
                  </div>
                </div>
                <div @click="roleNameEdit">
                  <icon-edit></icon-edit>
                </div>
              </div>
              <div>
                <a-button type="text" @click="copyRole">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制角色
                </a-button>
              </div>
            </div>
            <a-divider></a-divider>
            <div class="base-info">
              <div class="base-info-title">基本信息</div>
              <div ref="baseInfoItemListRef" class="base-info-form">
                <div
                  v-for="(item, index) in formData[currentRole].baseInfo"
                  :key="item.uuid"
                  class="form-item"
                  :class="
                    currentItem.type == 'baseInfo' && index == currentItem.index
                      ? 'is-current'
                      : ''
                  "
                  @click="itemChange(index, 'baseInfo')"
                >
                  <div class="item-title">
                    <img
                      v-if="item.required"
                      src="/src/assets/icons/required.svg"
                      alt="icon"
                    />
                    {{ item.label }}
                  </div>
                  <div class="item-desc">
                    <template v-if="item.descEnable">
                      <template v-if="item.desc">{{ item.desc }}</template>
                      <template v-else>请输入题目说明（选填）</template>
                    </template>
                  </div>
                  <div class="item-input">
                    <template v-if="item.type == 'text'">
                      <template
                        v-if="
                          item.key == 'cn' ||
                          item.key == 'mobile' ||
                          item.key == 'qq' ||
                          item.key == 'email'
                        "
                      >
                        <activity-input
                          :placeholder="item.value.placeholder"
                        ></activity-input>
                      </template>
                      <template v-else-if="item.key == 'real_name'">
                        <activity-input
                          :placeholder="item.value[0].placeholder"
                        ></activity-input>
                        <activity-input
                          :placeholder="item.value[1].placeholder"
                        ></activity-input>
                      </template>
                      <template v-else>
                        <activity-input
                          :placeholder="item.value.placeholder"
                        ></activity-input>
                      </template>
                    </template>
                    <template v-else-if="item.type == 'radio'">
                      <activity-radio
                        :disabled="item.key == 'sex'"
                        :options="item.value.options"
                      ></activity-radio>
                    </template>
                    <template v-else-if="item.type == 'checkBox'">
                      <activity-checkbox
                        :options="item.value.options"
                      ></activity-checkbox>
                    </template>
                    <template v-else-if="item.type == 'date'">
                      <template v-if="item.key == 'birthday'">
                        <a-date-picker
                          :readonly="true"
                          :placeholder="item.value.placeholder"
                          style="width: 200px"
                        />
                      </template>
                    </template>
                  </div>
                </div>
              </div>
            </div>
            <a-divider></a-divider>
            <div v-if="formData[currentRole].task.length > 0" class="task">
              <div class="text">
                <div>任务要求</div>
              </div>
              <div>
                <a-button type="text">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制任务
                </a-button>
              </div>
            </div>
            <div v-if="formData[currentRole].task.length > 0" class="button">
              <div class="copy">
                <a-button type="outline">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制该任务
                </a-button>
              </div>
              <div class="add">
                <a-button type="primary">
                  <template #icon>
                    <icon-plus />
                  </template>
                  新增任务平台
                </a-button>
              </div>
            </div>
            <div
              v-if="formData[currentRole].task.length === 0"
              style="width: 100%"
            >
              <a-button type="primary" style="width: 100%; border-radius: 4px">
                添加任务平台（可选）
              </a-button>
            </div>
          </div>
          <div class="setting">
            <a-affix offset-top="80">
              <div class="setting-body">
                <div class="setting-frame">
                  <div class="setting-title">题目设置</div>
                  <div class="setting-form">
                    <div class="form-required">
                      <a-checkbox
                        v-model="currentItemObj.required"
                        :value="true"
                        :disabled="
                          currentItemObj.key == 'cn' ||
                          currentItemObj.key == 'qq' ||
                          currentItemObj.key == 'mobile'
                        "
                      >
                        <span class="form-required-label">必填项</span>
                      </a-checkbox>
                    </div>
                    <div class="form-format">
                      <div class="form-format-title">格式</div>
                      <div class="form-format-content">
                        <a-form layout="vertical">
                          <template v-if="currentItemObj.type == 'text'">
                            <template v-if="currentItemObj.key == 'real_name'">
                              <a-form-item label="姓名格式">
                                <a-select
                                  v-model="currentItemObj.value[0].rule"
                                  :disabled="true"
                                >
                                  <a-option value="text">单行文本</a-option>
                                </a-select>
                              </a-form-item>
                              <a-form-item label="姓名提示文案">
                                <a-input
                                  v-model="currentItemObj.value[0].placeholder"
                                  :max-length="20"
                                  show-word-limit
                                ></a-input>
                              </a-form-item>
                              <a-form-item label="身份证格式">
                                <a-select
                                  v-model="currentItemObj.value[1].rule"
                                  :disabled="true"
                                >
                                  <a-option value="id_number"
                                    >身份证号码
                                  </a-option>
                                </a-select>
                              </a-form-item>
                              <a-form-item label="身份证提示文案">
                                <a-input
                                  v-model="currentItemObj.value[1].placeholder"
                                  :max-length="20"
                                  show-word-limit
                                ></a-input>
                              </a-form-item>
                            </template>
                            <template v-else>
                              <a-form-item label="题目格式">
                                <a-select
                                  v-model="currentItemObj.value.rule"
                                  :disabled="true"
                                >
                                  <a-option value="text">单行文本</a-option>
                                  <a-option value="mobile">手机号码</a-option>
                                  <a-option value="qq">QQ号码</a-option>
                                  <a-option value="email">电子邮箱</a-option>
                                  <a-option value="radio">单选</a-option>
                                  <a-option value="checkBox">多选</a-option>
                                  <a-option value="textarea">多行文本</a-option>
                                  <a-option value="upload">图片/文件</a-option>
                                  <a-option value="date">日期/时间</a-option>
                                </a-select>
                              </a-form-item>
                              <a-form-item
                                v-if="currentItemObj.value.rule == 'text'"
                                label="最多填写字数"
                              >
                                <a-input-number
                                  v-model="currentItemObj.value.maxLength"
                                  :max="currentItemObj.key == 'cn' ? 8 : 20"
                                  min="1"
                                ></a-input-number>
                              </a-form-item>
                              <a-form-item label="提示文案">
                                <a-input
                                  v-model="currentItemObj.value.placeholder"
                                  :max-length="20"
                                  show-word-limit
                                ></a-input>
                              </a-form-item>
                            </template>
                          </template>
                          <template v-else-if="currentItemObj.type === 'radio'">
                            <a-form-item label="题目格式">
                              <a-select
                                v-model="currentItemObj.value.rule"
                                :disabled="true"
                              >
                                <a-option value="text">单行文本</a-option>
                                <a-option value="mobile">手机号码</a-option>
                                <a-option value="qq">QQ号码</a-option>
                                <a-option value="email">电子邮箱</a-option>
                                <a-option value="radio">单选</a-option>
                                <a-option value="checkBox">多选</a-option>
                                <a-option value="textarea">多行文本</a-option>
                                <a-option value="upload">图片/文件</a-option>
                                <a-option value="date">日期/时间</a-option>
                              </a-select>
                            </a-form-item>
                            <a-form-item label="选项数量">
                              <a-input-number
                                v-model="currentItemObj.value.number"
                                :disabled="currentItemObj.key == 'sex'"
                                :max="20"
                                :min="2"
                                @change="radioNumberChange"
                              ></a-input-number>
                            </a-form-item>
                          </template>
                          <template
                            v-else-if="currentItemObj.type === 'checkBox'"
                          >
                            <a-form-item label="题目格式">
                              <a-select
                                v-model="currentItemObj.value.rule"
                                :disabled="true"
                              >
                                <a-option value="text">单行文本</a-option>
                                <a-option value="mobile">手机号码</a-option>
                                <a-option value="qq">QQ号码</a-option>
                                <a-option value="email">电子邮箱</a-option>
                                <a-option value="radio">单选</a-option>
                                <a-option value="checkBox">多选</a-option>
                                <a-option value="textarea">多行文本</a-option>
                                <a-option value="upload">图片/文件</a-option>
                                <a-option value="date">日期/时间</a-option>
                              </a-select>
                            </a-form-item>
                            <a-form-item label="选项数量">
                              <a-input-number
                                v-model="currentItemObj.value.number"
                                :disabled="currentItemObj.key == 'sex'"
                                :max="20"
                                :min="2"
                                @change="radioNumberChange"
                              ></a-input-number>
                            </a-form-item>
                            <a-form-item label="最多可选">
                              <a-input-number
                                v-model="currentItemObj.value.max"
                                :max="currentItemObj.value.options.length"
                                :min="1"
                              ></a-input-number>
                            </a-form-item>
                            <a-form-item label="最少可选">
                              <a-input-number
                                v-model="currentItemObj.value.min"
                                :max="currentItemObj.value.options.length"
                                :min="1"
                              ></a-input-number>
                            </a-form-item>
                          </template>
                          <template v-else-if="currentItemObj.type == 'date'">
                            <a-form-item label="题目格式">
                              <a-select
                                v-model="currentItemObj.value.rule"
                                :disabled="true"
                              >
                                <a-option value="text">单行文本</a-option>
                                <a-option value="mobile">手机号码</a-option>
                                <a-option value="qq">QQ号码</a-option>
                                <a-option value="email">电子邮箱</a-option>
                                <a-option value="radio">单选</a-option>
                                <a-option value="checkBox">多选</a-option>
                                <a-option value="textarea">多行文本</a-option>
                                <a-option value="upload">图片/文件</a-option>
                                <a-option value="date">日期/时间</a-option>
                              </a-select>
                            </a-form-item>
                          </template>
                        </a-form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </a-affix>
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <a-space>
      <a-button type="secondary" @click="goPrev"> 上一步</a-button>
      <a-button type="primary" @click="onNextClick"> 下一步</a-button>
    </a-space>
    <a-modal
      v-model:visible="addRolModalVisible"
      :hide-title="true"
      :footer="false"
      modal-class="role-modal-body"
    >
      <div class="template">
        <div class="title">从模板开始快速创建</div>
        <div class="tags">
          <a-tag v-for="item in 5" :key="item">Coser</a-tag>
        </div>
      </div>
      <div class="custom">
        <div class="title">自定义角色名称</div>
        <div class="input">
          <a-input
            v-model="roleName"
            max-length="8"
            show-word-limit
            placeholder="请输入角色名称"
          ></a-input>
        </div>
      </div>
      <div class="button">
        <a-button type="primary" style="width: 100%" @click="addRole()">
          确认
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { Modal } from '@arco-design/web-vue';
  import {
    RoleBaseInfoCheckBoxModel,
    RoleBaseInfoDateModel,
    RoleBaseInfoRadioModel,
    RoleBaseInfoTextModel,
    RoleTaskModel,
  } from '@/api/form';
  import ActivityInput from '@/views/activity/activity-create/components/customer/activity-input.vue';
  import ActivityRadio from '@/views/activity/manage-create/components/customer/activity-radio.vue';
  import Sortable from 'sortablejs';
  import { guid } from '@/utils/js/index';
  import ActivityCheckbox from '@/views/activity/activity-create/components/customer/activity-checkbox.vue';

  const emits = defineEmits(['changeStep']);

  const formData = ref<RoleTaskModel[]>([
    {
      role: '兽装',
      baseInfo: [
        {
          uuid: guid(),
          key: 'cn',
          label: 'CN',
          descEnable: false,
          desc: '',
          required: true,
          value: {
            placeholder: '请输入',
            maxLength: 8,
            rule: 'text',
          } as RoleBaseInfoTextModel,
          type: 'text',
        },
        {
          uuid: guid(),
          key: 'mobile',
          label: '联系电话',
          desc: '',
          descEnable: false,
          required: true,
          value: {
            placeholder: '请输入',
            rule: 'mobile',
          } as RoleBaseInfoTextModel,
          type: 'text',
        },
        {
          uuid: guid(),
          key: 'qq',
          label: 'QQ',
          descEnable: false,
          desc: '',
          required: true,
          value: {
            placeholder: '请输入',
            rule: 'qq',
          } as RoleBaseInfoTextModel,
          type: 'text',
        },
      ],
      task: [],
    },
  ]);

  const currentRole = ref(0);

  const roleChange = (index: number) => {
    currentRole.value = index;
  };

  const roleItemRemove = (index: number) => {
    Modal.confirm({
      title: '确定删除该角色吗？',
      content: '删除后，该角色下的信息将被删除，且无法恢复!',
      okText: '确定',
      cancelText: '取消',
      simple: true,
      onOk: () => {
        formData.value.splice(index, 1);
        if (currentRole.value === index) {
          currentRole.value = index - 1;
        } else if (currentRole.value > formData.value.length - 1) {
          currentRole.value = formData.value.length - 1;
        }
      },
    });
  };

  const addRolModalVisible = ref(false);

  const addRoleClick = () => {
    addRolModalVisible.value = true;
  };
  const roleNameEditVisible = ref(false);
  const roleNameEditRef = ref();
  const roleName = ref('');

  const addRole = () => {
    formData.value.push({
      role: roleName.value,
      baseInfo: [],
      task: [],
    });
    addRolModalVisible.value = false;
    roleName.value = '';
    currentRole.value = formData.value.length - 1;
  };

  const roleNameEdit = () => {
    roleNameEditVisible.value = true;
    nextTick(() => {
      roleNameEditRef.value.focus();
    });
  };

  const copyRole = () => {
    if (formData.value.length === 8) {
      Modal.error({
        title: '角色数量已达上限',
        content: '最多只能创建8个角色',
        okText: '确定',
        simple: true,
      });
      return;
    }
    formData.value.push({
      role: `${formData.value[currentRole.value].role}`,
      baseInfo: formData.value[currentRole.value].baseInfo,
      task: formData.value[currentRole.value].task,
    });
    currentRole.value = formData.value.length - 1;
    roleNameEdit();
  };

  const currentItem = ref({
    type: 'baseInfo',
    index: 0,
  });

  const itemChange = (index: number, type: string) => {
    currentItem.value.type = type;
    currentItem.value.index = index;
  };

  const currentItemObj = computed(() => {
    if (currentItem.value.type === 'baseInfo') {
      return formData.value[currentRole.value].baseInfo[
        currentItem.value.index
      ];
    }
    return null;
  });

  const radioNumberChange = (value: number) => {
    if (currentItemObj.value?.value.options.length > value) {
      currentItemObj.value!.value!.options!.length = value;
    } else if (currentItemObj.value?.value.options.length < value) {
      if (currentItemObj.value?.value?.options) {
        for (
          let i = currentItemObj.value.value.options.length;
          i < value;
          i += 1
        ) {
          // 循环体逻辑
          currentItemObj.value?.value.options.push({
            value: currentItemObj.value.value.options.length + 1,
            label: `选项${currentItemObj.value.value.options.length + 1}`,
          });
        }
      }
    }
  };

  const addItem = (region: string, type: string, component: string) => {
    if (region === 'baseInfo') {
      if (type === 'general') {
        /* empty */
        if (component === 'text') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: '',
            label: '文本',
            required: true,
            descEnable: true,
            desc: '',
            value: {
              placeholder: '请输入',
              maxLength: 8,
              rule: 'textarea',
            } as RoleBaseInfoTextModel,
            type: 'text',
          });
        } else if (component === 'radio') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: '',
            label: '单选',
            required: true,
            descEnable: true,
            desc: '',
            value: {
              options: [
                { value: 1, label: '选项1' },
                { value: 2, label: '选项2' },
              ],
              number: 2,
              rule: 'radio',
            } as RoleBaseInfoRadioModel,
            type: 'radio',
          });
        } else if (component === 'checkBox') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: '',
            label: '多选',
            required: true,
            descEnable: true,
            desc: '',
            value: {
              options: [
                { value: 1, label: '选项1' },
                { value: 2, label: '选项2' },
              ],
              number: 2,
              rule: 'checkBox',
              min: 1,
              max: 2,
            } as RoleBaseInfoCheckBoxModel,
            type: 'checkBox',
          });
        }
      } else if (type === 'personal') {
        /* empty */
        if (component === 'sex') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: 'sex',
            label: '性别',
            required: true,
            descEnable: false,
            desc: '',
            value: {
              options: [
                { value: 1, label: '男' },
                { value: 2, label: '女' },
              ],
              number: 2,
              rule: 'radio',
            } as RoleBaseInfoRadioModel,
            type: 'radio',
          });
        } else if (component === 'birthday') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: 'birthday',
            label: '出生日期',
            required: true,
            descEnable: false,
            desc: '',
            value: {
              placeholder: '请选择出生日期',
              rule: 'date',
            } as RoleBaseInfoDateModel,
            type: 'date',
          });
        } else if (component === 'email') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: 'email',
            label: '邮箱',
            descEnable: false,
            desc: '',
            required: true,
            value: {
              placeholder: '请输入邮箱',
              rule: 'email',
            } as RoleBaseInfoTextModel,
            type: 'text',
          });
        } else if (component === 'real_name') {
          formData.value[currentRole.value].baseInfo.push({
            uuid: guid(),
            key: 'real_name',
            label: '实名信息',
            descEnable: false,
            desc: '',
            required: true,
            value: [
              {
                placeholder: '请输入姓名',
                rule: 'text',
              },
              {
                placeholder: '请输入身份证号',
                rule: 'id_number',
              },
            ] as RoleBaseInfoTextModel[],
            type: 'text',
          });
        }
      }
      currentItem.value = {
        type: 'baseInfo',
        index: formData.value[currentRole.value].baseInfo.length - 1,
      };
      console.log(formData.value[currentRole.value].baseInfo);
    } else if (region === 'task') {
      /* empty */
    }
  };

  const onNextClick = async () => {
    emits('changeStep', 'submit', { ...formData.value });
  };
  const goPrev = () => {
    emits('changeStep', 'backward');
  };

  const baseInfoItemListRef = ref();

  onMounted(() => {
    // 拖动排序  具体参数可查看官网文档
    // eslint-disable-next-line no-new
    new Sortable(baseInfoItemListRef.value, {
      sort: true,
      animation: 150,
      draggable: '.form-item',
      dataIdAttr: 'data-id',
      // 拖拽完成后更新数据的位置
      onChoose({ oldIndex }: any) {
        currentItem.value = {
          type: 'baseInfo',
          index: oldIndex,
        };
      },
      async onEnd({ oldIndex, newIndex }: any) {
        const arr = [...formData.value[currentRole.value].baseInfo];
        const old = formData.value[currentRole.value].baseInfo[oldIndex];
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, old);
        formData.value[currentRole.value].baseInfo = arr;
        currentItem.value = {
          type: 'baseInfo',
          index: newIndex,
        };
      },
    });
  });
</script>

<style scoped lang="less">
  .role-task {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;

    .content {
      min-height: 500px;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;

      .add-button {
        width: 60%;
      }

      .body {
        width: 100%;
        min-width: 1140px;
        padding: 0 40px;

        .role-list {
          display: flex;
          flex-wrap: wrap;
          grid-gap: 6px;

          .role-item {
            display: flex;
            height: 46px;
            padding: 16px 22px;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
            border-radius: 4px;
            background: #f7f8fa;
            border: 2px solid #f7f8fa;

            .role-name {
              min-width: 68px;
              color: var(--text-1, #1d2129);
              /* 16/CN-Medium */
              font-family: 'PingFang SC';
              font-size: 16px;
              font-style: normal;
              font-weight: 500;
              line-height: 24px; /* 150% */
            }
          }

          .currentRole {
            border: 2px solid #6aa1ff;
            background: #e8f3ff;
          }
        }

        :deep(.arco-divider-text) {
          color: var(--text-3, #86909c);
          /* 14/CN-Regular */
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
        }

        .role-task-body {
          display: grid;
          align-items: flex-start;
          gap: 12px;
          align-self: stretch;
          grid-template-columns: 3fr 4fr 3fr;

          .components {
            width: 100%;

            .components-body {
              display: flex;
              padding: 16px 22px;
              flex-direction: column;
              align-items: flex-start;
              gap: 33px;
              border-radius: 4px;
              background: var(--fill-1, #f7f8fa);

              .components-base {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 14px;
                align-self: stretch;

                .components-title {
                  color: var(--text-1, #1d2129);
                  /* 20/CN-Medium */
                  font-family: 'PingFang SC';
                  font-size: 20px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 28px; /* 140% */
                }

                .components-content {
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 14px;
                  align-self: stretch;

                  .general-tools,
                  .personal—details {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                    align-self: stretch;

                    .subtitle {
                      color: var(--text-1, #1d2129);
                      /* 16/CN-Medium */
                      font-family: 'PingFang SC';
                      font-size: 16px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 24px; /* 150% */
                    }

                    .sub-frame {
                      display: flex;
                      align-items: flex-start;
                      align-content: flex-start;
                      gap: 6px;
                      align-self: stretch;
                      flex-wrap: wrap;
                    }
                  }
                }
              }
            }
          }

          .form {
            display: flex;
            padding: 16px 0;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            border-radius: 4px;
            background: var(--fill-1, #f7f8fa);

            .title,
            .task {
              padding: 0 22px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              align-self: stretch;

              .text {
                color: var(--text-1, #1d2129);
                /* 20/CN-Medium */
                font-family: 'PingFang SC';
                font-size: 20px;
                font-style: normal;
                font-weight: 500;
                line-height: 28px; /* 140% */
                display: flex;
                align-items: center;
                gap: 6px;
              }
            }

            .base-info {
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              gap: 12px;
              align-self: stretch;

              .base-info-title {
                padding: 0 22px;
                color: var(--text-1, #1d2129);
                /* 16/CN-Medium */
                font-family: 'PingFang SC';
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: 24px; /* 150% */
              }

              .base-info-form {
                max-width: 100%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                align-self: stretch;
                gap: 20px;

                .is-current {
                  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.07);
                  border: 1.5px solid #165dff;
                  border-radius: 4px;
                }

                .form-item:hover {
                  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.07);
                }

                .form-item {
                  padding: 10px 22px;
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                  align-self: stretch;
                  gap: 4px;
                  cursor: move;

                  .item-title {
                    display: flex;
                    padding-bottom: 8px;
                    align-items: center;
                    gap: 4px;
                    align-self: stretch;
                    color: var(--text-2, #4e5969);
                    /* 14/CN-Regular */
                    font-family: 'PingFang SC';
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px; /* 157.143% */
                  }

                  .item-desc {
                    color: var(--text-3, #86909c);

                    /* 12/CN-Regular */
                    font-family: 'PingFang SC';
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 20px; /* 166.667% */
                  }

                  .item-input {
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    grid-gap: 12px;
                  }
                }
              }
            }

            .button {
              display: flex;
              align-items: flex-start;
              gap: 2%;
              align-self: stretch;

              div {
                width: 49%;

                :deep(.arco-btn) {
                  width: 100%;
                }
              }
            }
          }

          .setting {
            .setting-body {
              display: flex;
              padding: 16px 22px;
              flex-direction: column;
              align-items: flex-start;
              gap: 33px;
              border-radius: 4px;
              background: var(--fill-1, #f7f8fa);

              .setting-frame {
                width: 100%;
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 14px;

                .setting-title {
                  color: var(--text-1, #1d2129);
                  /* 20/CN-Medium */
                  font-family: 'PingFang SC';
                  font-size: 20px;
                  font-style: normal;
                  font-weight: 500;
                  line-height: 28px; /* 140% */
                }

                .setting-form {
                  min-height: 500px;
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                  gap: 12px;
                  align-self: stretch;

                  .form-required {
                    .form-required-label {
                      color: var(--text-1, #1d2129);
                      /* 16/CN-Regular */
                      font-family: 'PingFang SC';
                      font-size: 16px;
                      font-style: normal;
                      font-weight: 400;
                      line-height: 24px; /* 150% */
                    }
                  }

                  .form-format {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                    align-self: stretch;

                    .form-format-title {
                      color: var(--text-1, #1d2129);

                      /* 16/CN-Medium */
                      font-family: 'PingFang SC';
                      font-size: 16px;
                      font-style: normal;
                      font-weight: 500;
                      line-height: 24px; /* 150% */
                    }

                    .form-format-content {
                      display: flex;
                      flex-direction: column;
                      align-items: flex-start;
                      align-self: stretch;
                    }
                  }
                }
              }
            }
          }

          :deep(.arco-divider) {
            margin: 0;
          }
        }
      }
    }
  }
</style>
