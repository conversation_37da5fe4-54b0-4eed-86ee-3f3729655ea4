<script setup lang="ts">
  // 子组件事件
  import { computed, PropType, ref } from 'vue';
  import { AttributeModuleGroup } from '@/components/module/type/attribute-module-group';
  import useModuleStore from '@/components/module/store/module';
  import IconUseRef from '@/components/module/icon-ref/icon-use-ref.vue';
  import { ResponseGetModuleItemModel } from '@/api/module/type';

  const useModule = useModuleStore();

  const emits = defineEmits(['itemAdd']);
  // 父组件传参
  const props = defineProps({
    /** 已使用的模块 */
    usedModule: {
      type: Object as PropType<AttributeModuleGroup[]>,
      required: true,
    },
  });

  /**
   * 模块是否禁用/已经存在
   * @param item
   */
  const moduleDisable = (item: ResponseGetModuleItemModel): boolean => {
    // 查找已经使用的模块的标题中是否有传入的模块标题
    const findIndex = props.usedModule?.findIndex(
      (module: AttributeModuleGroup) => module.title === item.title
    );
    // 存在则返回true, 否则false
    return findIndex !== -1;
  };

  /**
   * 添加模块
   * @param item
   */
  const addItem = (item: ResponseGetModuleItemModel) => {
    if (!moduleDisable(item)) {
      emits('itemAdd', item);
    }
  };

  const taskInfoModulesList = ref<ResponseGetModuleItemModel[]>([]);

  (async () => {
    taskInfoModulesList.value = await useModule.getTaskInfoModuleList();
  })();

  const taskInfoModules = computed(() => taskInfoModulesList.value);
</script>

<template>
  <div class="personal—details">
    <div class="subtitle">任务说明</div>
    <div class="sub-frame">
      <a-button
        v-for="(item, index) in taskInfoModules"
        :key="index"
        size="small"
        :disabled="moduleDisable(item)"
        @click="addItem(item)"
      >
        {{ item.title }}
        <template #icon>
          <IconUseRef :icon-name="item.icon" />
        </template>
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
  .personal—details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;

    .subtitle {
      color: #1d2129;
      /* 16/CN-Medium */
      font-family: 'PingFang SC', sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px; /* 150% */
    }

    .sub-frame {
      display: flex;
      align-items: flex-start;
      align-content: flex-start;
      gap: 6px;
      align-self: stretch;
      flex-wrap: wrap;
    }
  }
</style>
