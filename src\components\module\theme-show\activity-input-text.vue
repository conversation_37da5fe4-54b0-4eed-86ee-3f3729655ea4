<script setup lang="ts">
  import { computed, defineProps, PropType } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
  import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
  // 父组件传参
  const props = defineProps({
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    // 是否可编辑
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },
  });

  const itemStruct = computed(() => props.item?.getComponentStructType());

  const itemAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  const placeholderAttr = computed({
    get: () => itemAttribute.value?.placeholder ?? '请输入内容',
    set: (value) => {
      props.item?.setComponentPlaceholder(value);
    },
  });

  const valueAttr = computed({
    get: () => itemAttribute.value?.value,
    set: (value) => {
      props.item?.setComponentValue(value);
    },
  });

  const maxLengthAttr = computed(() => itemAttribute.value?.maxLength);
</script>

<template>
  <div v-if="item" class="activity-input">
    <!--    :disabled="!enabled"-->
    <div class="body">
      <a-textarea
        v-if="itemStruct === EnumComponentStructType.TEXTAREA"
        v-model="valueAttr"
        :placeholder="placeholderAttr"
        :max-length="maxLengthAttr ?? Infinity"
        show-word-limit
        :auto-size="{ minRows: 3 }"
      />
      <span v-else>{{ placeholderAttr }}</span>
    </div>
  </div>
</template>

<style scoped lang="less">
  .activity-input {
    width: 100%;
    display: flex;
    padding: 5px 12px;
    align-items: center;
    flex: 1 0 0;
    border-radius: 2px;
    background: #fff;
    .body {
      width: 100%;
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1 0 0;
      color: #86909c;
      font-family: 'PingFang SC', Arial, sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      span {
        height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  :deep(.arco-textarea-focus) {
    border-color: transparent !important;
  }
  :deep(.arco-textarea-wrapper) {
    background-color: transparent !important;
  }
</style>
