import { IFormItemModel } from '@/views/activity/activity-create/type/model-role-task';
import { v4 as uuidV4 } from 'uuid';
import useModuleStore from '@/components/module/store/module';
import { CustomGroupModule } from '@/components/module/entity/struct/group';
import { EnumRoleContentType } from '@/components/module/enum/enum-role-content-type';
import { AttributeModuleGroup } from '@/components/module/type/attribute-module-group';
import { ResponseGetModuleItemModel } from '@/api/module/type';
import { ICustomComponent } from '@/components/module/type/interface-custom-module';
import { ComponentFactory } from '@/components/module/factory/component-factory';

const useModule = useModuleStore();

const ResponseGetModuleItemModelToAttributeModuleGroup = (
  attr: ResponseGetModuleItemModel
): AttributeModuleGroup => {
  const items: ICustomComponent[] = [];
  attr.list.forEach((item) => {
    items.push(ComponentFactory.getModule(item));
  });
  console.log('item', items);
  return {
    id: attr.id,
    uuid: attr.uuid,
    key: attr.key,
    title: attr.title,
    description: attr.description,
    required: attr.required,
    readonly: attr.readonly,
    list: items,
  };
};

export const GroupFactory = {
  /**
   * 获取组模块
   * @param attribute
   * @param whereInner
   */
  getGroupModuleByAttribute: (
    attribute: ResponseGetModuleItemModel,
    whereInner: EnumRoleContentType = EnumRoleContentType.BASE
  ): IFormItemModel => {
    return {
      uuid: uuidV4(),
      entity: new CustomGroupModule(
        ResponseGetModuleItemModelToAttributeModuleGroup(attribute),
        whereInner
      ),
    };
  },
  /**
   * 获取组模块
   * @param tittle
   * @param whereInner
   */
  getGroupModuleByTitle: async (
    tittle: string,
    whereInner: EnumRoleContentType = EnumRoleContentType.BASE
  ): Promise<IFormItemModel> => {
    const formItem: IFormItemModel = {} as IFormItemModel;
    await useModule
      .getModule(tittle)
      .then((res: ResponseGetModuleItemModel | undefined) => {
        if (res) {
          formItem.uuid = uuidV4();
          formItem.entity = new CustomGroupModule(
            ResponseGetModuleItemModelToAttributeModuleGroup(res),
            whereInner
          );
        }
      });
    return formItem;
  },
  /**
   * 获取内置组模块
   * @param whereInner
   */
  getInherentGroupModule: async (
    whereInner: EnumRoleContentType = EnumRoleContentType.BASE
  ): Promise<IFormItemModel[]> => {
    const modules: IFormItemModel[] = [];
    await useModule
      .getInherentModuleList()
      .then((res: ResponseGetModuleItemModel[]) => {
        res.forEach((item: ResponseGetModuleItemModel) => {
          modules.push({
            uuid: uuidV4(),
            entity: new CustomGroupModule(
              ResponseGetModuleItemModelToAttributeModuleGroup(item),
              whereInner
            ),
          });
        });
      });
    return modules;
  },
};
