<template>
  <div class="container">
    <Breadcrumb :items="['menu.list', 'menu.list.grant']" />
    <div style="width: 100%; display: flex; justify-content: center">
      <a-card class="general-card" title="邀请票发放列表" style="width: 95%">
        <a-row :gutter="110">
          <a-col :flex="1">
            <a-form
              :model="formSearch"
              label-align="left"
              auto-label-width
              ref="formSearchRef"
            >
              <a-row :gutter="24">
                <a-col :span="8">
                  <a-form-item field="name" label="商品名称">
                    <a-input
                      v-model="formSearch.name"
                      placeholder="请输入商品名称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="coupons" label="卷名称">
                    <a-input
                      v-model="formSearch.coupons"
                      placeholder="请输入卷名称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item field="providedTime" label="发放时间">
                    <a-range-picker v-model="formSearch.providedTime" />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-col :flex="'86px'" style="text-align: right">
            <a-space direction="horizontal" :size="18">
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-row style="margin-bottom: 16px">
          <a-col :span="12">
            <a-space>
              <a-button type="primary" @click="handleAdd">
                <template #icon>
                  <icon-plus />
                </template>
                发放卷码
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <a-table :data="listData" style="margin-top: 30px">
          <template #columns>
            <a-table-column
              title="批次编号"
              data-index="merchantInvitationId"
            ></a-table-column>
            <a-table-column
              title="商品名称"
              data-index="goodsName"
            ></a-table-column>
            <a-table-column
              title="卷名称"
              data-index="goodsItemName"
            ></a-table-column>
            <a-table-column
              title="上传总数量"
              data-index="analysisTotalCount"
            ></a-table-column>
            <a-table-column
              title="上传失败"
              data-index="analysisFailCount"
            ></a-table-column>
            <a-table-column
              title="上传成功"
              data-index="analysisSuccessCount"
            ></a-table-column>
            <a-table-column
              title="发布卷码数量"
              data-index="ticketProvidedCount"
            ></a-table-column>
            <a-table-column
              title="发放时间"
              data-index="createTime"
            ></a-table-column>
            <a-table-column
              title="操作员"
              data-index="operationName"
            ></a-table-column>
            <a-table-column title="Optional">
              <template #cell="{ record }">
                <a-button @click="handleSeeDetail(record)" type="text"
                  >查看</a-button
                >
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </div>
    <a-modal
      :visible="detailVisible"
      hide-title
      :footer="false"
      :width="1020"
      :height="620"
      @cancel="detailVisible = false"
    >
      <div
        style="
          display: flex;
          flex-direction: column;
          justify-content: start;
          width: 100%;
          margin: 0 0 20px 0;
        "
      >
        <div style="font-weight: 700; font-size: 18px">
          {{ detailInfo.goodsName }}
        </div>
        <div
          style="
            color: #999;
            font-size: 14px;
            font-weight: 400;
            margin-top: 6px;
          "
        >
          发放时间：{{ detailInfo.createTime }}
        </div>
      </div>

      <a-table
        :data="detailList"
        style="margin-top: 30px; border: 1px solid rgba(0, 0, 0, 0.01)"
      >
        <template #columns>
          <a-table-column
            title="用户CN"
            data-index="userCn"
            width="100"
          ></a-table-column>
          <a-table-column
            title="发卷手机号"
            data-index="mobile"
            width="130"
          ></a-table-column>
          <a-table-column title="发放日期" width="340">
            <template #cell="{ record }">
              <a-tag
                v-for="(ir, index) in record.signUpDates"
                style="margin: auto 5px"
                :key="index"
                >{{ ir }}</a-tag
              >
            </template>
          </a-table-column>
          <a-table-column title="用户姓名" data-index="userName" width="180">
            <template #cell="{ record }">
              {{ record.userName.length > 0 ? record.userName : '/' }}
            </template>
          </a-table-column>
          <a-table-column title="身份证号码" data-index="idCard" width="320">
            <template #cell="{ record }">
              {{ record.idCard.length > 0 ? record.idCard : '/' }}
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import useLoading from '@/hooks/loading';
  import {
    FormSearchModel,
    pageParams,
    ListAPI,
    DetailPageItemModel,
    GrantRecord,
  } from '@/api/list';

  const { setLoading } = useLoading(true);

  const listData = ref<GrantRecord[]>([] as GrantRecord[]);

  const formSearch = reactive<FormSearchModel>({} as FormSearchModel);
  const formSearchRef = ref();
  const handleReset = () => {
    formSearchRef.value.resetFields();
    handleSearch();
  };
  const handleSearch = () => {
    let providedStartTime, providedEndTime;
    if (
      formSearch.providedTime !== undefined &&
      formSearch.providedTime.length > 0
    ) {
      providedStartTime = formSearch.providedTime[0];
      providedEndTime = formSearch.providedTime[1];
    }

    const params = {
      // 接口需要的字段
      merchantId: formSearch.merchantId,
      goodsName: formSearch.name,
      goodsItemName: formSearch.coupons,
      providedStartTime,
      providedEndTime,
      // 分页参数
      page: formSearch.page,
      limit: formSearch.limit,
      cursor: formSearch.cursor,
    };
    setLoading(true);
    ListAPI.queryInspectionPageList(params)
      .then((res) => {
        listData.value = res.data.list;
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleAdd = () => {
    // TODO 跳转发卷页面
  };

  const detailVisible = ref(false);
  const detailInfo = reactive({
    goodsName: '',
    goodsItemName: '',
    createTime: '',
  });
  const detailList = ref<DetailPageItemModel[]>([] as DetailPageItemModel[]);
  const detailPageParam = reactive<pageParams>({
    page: 1,
    limit: 10,
    cursor: undefined,
  });

  const handleSeeDetail = (record: GrantRecord) => {
    let merchantInvitationId = record.merchantInvitationId;
    merchantInvitationId = '1639975174959947780';
    setLoading(true);
    ListAPI.queryInspectionDetailPageList(merchantInvitationId, detailPageParam)
      .then((res) => {
        detailInfo.goodsName = record.goodsName;
        detailInfo.goodsItemName = record.goodsItemName;
        detailInfo.createTime = record.createTime;
        detailList.value = res.data.detailPage.list;
        detailVisible.value = true;
      })
      .finally(() => {
        setLoading(false);
      });
  };

  onMounted(() => {
    formSearch.merchantId = '6296657803784216401';
    handleSearch();
  });
</script>

<script lang="ts">
  export default {
    name: 'SearchTable',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
    position: relative;
    //left: -20px;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  @media screen and (max-width: 768px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 180px);
    }
  }
  @media screen and (max-width: 1920px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 400px);
    }
  }
  @media screen and (min-width: 1921px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 300px);
    }
  }
  :deep(.arco-table-container) {
    overflow: auto;
  }
</style>
