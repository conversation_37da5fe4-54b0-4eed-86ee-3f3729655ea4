// 用户状态类型
export type UserStatus = 'pending' | 'approved' | 'rejected';

// 平台类型
export type PlatformType = 'xiaohongshu' | 'wechat' | 'kuaishou';

// 角色类型 - 根据图片中的标签更新
export type RoleType =
  | 'photographer'
  | 'coser'
  | 'model'
  | 'makeup'
  | 'costume'
  | 'lolita'
  | 'hanfu'
  | 'sankeng'
  | 'fursuit';

// 用户信息接口
export interface User {
  id: string;
  name: string;
  avatar?: string;
  platform: PlatformType;
  role: RoleType;
  status: UserStatus;
  applyDate: string;
  actionLabel?: string; // 自定义操作按钮标签
  phone?: string;
  email?: string;
  description?: string;
  works?: string[]; // 作品链接
  experience?: string; // 经验描述
}

// 状态标签接口
export interface StatusTab {
  key: string;
  label: string;
  count?: number;
}

// 平台选项接口
export interface Platform {
  value: string;
  label: string;
}

// 角色选项接口
export interface Role {
  value: string;
  label: string;
}

// 搜索筛选参数接口
export interface SearchFilters {
  searchText?: string;
  platform?: string;
  role?: string;
  date?: string;
  status?: UserStatus;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// API 响应接口
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

// 获取报名列表请求参数
export interface GetRegistrationListRequest
  extends SearchFilters,
    PaginationParams {
  activityId?: string;
}

// 获取报名列表响应
export interface GetRegistrationListResponse {
  users: User[];
  total: number;
  page: number;
  pageSize: number;
}

// 审批操作请求参数
export interface ApprovalRequest {
  userId: string;
  activityId: string;
  action: 'approve' | 'reject';
  reason?: string; // 拒绝原因
}

// 审批操作响应
export interface ApprovalResponse {
  success: boolean;
  message: string;
}

// 用户详情接口
export interface UserDetail extends User {
  submittedAt: string; // 提交时间
  reviewedAt?: string; // 审核时间
  reviewedBy?: string; // 审核人
  reviewReason?: string; // 审核意见
  attachments?: {
    type: 'image' | 'video' | 'document';
    url: string;
    name: string;
  }[];
}

// 统计数据接口
export interface RegistrationStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  todayNew: number;
  platformStats: {
    platform: PlatformType;
    count: number;
  }[];
  roleStats: {
    role: RoleType;
    count: number;
  }[];
}
