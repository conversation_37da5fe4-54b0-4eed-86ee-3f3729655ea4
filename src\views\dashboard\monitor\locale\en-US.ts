export default {
  'menu.dashboard.monitor': 'Real-time Monitor',
  'monitor.title.chatPanel': 'Chat Window',
  'monitor.title.quickOperation': 'Quick Operation',
  'monitor.title.studioInfo': 'Studio Information',
  'monitor.title.studioPreview': 'Studio Preview',
  'monitor.chat.options.all': 'All',
  'monitor.chat.placeholder.searchCategory': 'Search Category',
  'monitor.chat.update': 'Update',
  'monitor.list.title.order': 'Order',
  'monitor.list.title.cover': 'Cover',
  'monitor.list.title.name': 'Name',
  'monitor.list.title.duration': 'Duration',
  'monitor.list.title.id': 'ID',
  'monitor.list.tip.rotations': 'Rotations ',
  'monitor.list.tip.rest': ', The program list is not visible to viewers',
  'monitor.list.tag.auditFailed': 'Audit Failed',
  'monitor.tab.title.liveMethod': 'Live Method',
  'monitor.tab.title.onlinePopulation': 'Online Population',
  'monitor.liveMethod.normal': 'Normal Live',
  'monitor.liveMethod.flowControl': 'Flow Control Live',
  'monitor.liveMethod.video': 'Video Live',
  'monitor.liveMethod.web': 'Web Live',
  'monitor.editCarousel': 'Edit',
  'monitor.startCarousel': 'Start',
  'monitor.quickOperation.changeClarity': 'Change the Clarity',
  'monitor.quickOperation.switchStream': 'Switch Stream',
  'monitor.quickOperation.removeClarity': 'Remove the Clarity',
  'monitor.quickOperation.pushFlowGasket': 'Push Flow Gasket',
  'monitor.studioInfo.label.studioTitle': 'Studio Title',
  'monitor.studioInfo.label.onlineNotification': 'Online Notification',
  'monitor.studioInfo.label.studioCategory': 'Studio Category',
  'monitor.studioInfo.placeholder.studioTitle': "'s Studio",
  'monitor.studioInfo.btn.fresh': 'Fresh',
  'monitor.studioStatus.title.studioStatus': 'Studio Status',
  'monitor.studioStatus.title.pictureInfo': 'Picture Information',
  'monitor.studioStatus.smooth': 'Smooth',
  'monitor.studioStatus.frameRate': 'Frame',
  'monitor.studioStatus.bitRate': 'Bit',
  'monitor.studioStatus.mainstream': 'Main',
  'monitor.studioStatus.hotStandby': 'Hot',
  'monitor.studioStatus.coldStandby': 'Cold',
  'monitor.studioStatus.line': 'Line',
  'monitor.studioStatus.play': 'Format',
  'monitor.studioStatus.pictureQuality': 'Quality',
  'monitor.studioPreview.studio': 'Studio',
  'monitor.studioPreview.watching': 'watching',
};
