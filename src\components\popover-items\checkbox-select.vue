<template>
  <div class="checkbox-demo-tip-card">
    <div class="checkbox-demo-tip-title">多选</div>
    <div class="checkbox-demo-tip-desc">
      多选题功能允许参与者在预设选项中选择多个合适答案，是收集复合型反馈的高效工具。<br />
      使用场景：活动评估调研、需求收集、活动意向调研等。
    </div>
    <div class="checkbox-demo-tip-example">
      <div class="checkbox-demo-tip-checkbox demo-first"></div>
      <div
        :class="[
          'checkbox-demo-tip-checkbox',
          checkedList.includes('1') && 'selected',
        ]"
      >
        <input type="checkbox" value="1" v-model="checkedList" />
        <span style="flex: 1"></span>
      </div>
      <div
        :class="[
          'checkbox-demo-tip-checkbox',
          checkedList.includes('2') && 'selected',
        ]"
      >
        <input type="checkbox" value="2" v-model="checkedList" />
        <span style="flex: 1"></span>
      </div>
      <div
        :class="[
          'checkbox-demo-tip-checkbox',
          checkedList.includes('3') && 'selected',
        ]"
      >
        <input type="checkbox" value="3" v-model="checkedList" />
        <span style="flex: 1"></span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';

  const checkedList = ref(['2', '3']);
</script>

<style scoped lang="less">
  .checkbox-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .checkbox-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .checkbox-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .checkbox-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .checkbox-demo-tip-checkbox {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 6px;
    margin-bottom: 2px;
    padding: 0 8px;
    height: 20px;
    width: 66%;
    box-sizing: border-box;
    border: none;
  }
  .demo-first {
    width: 100%;
    margin-bottom: 8px;
    background: #6aa1ff;
  }
  .checkbox-demo-tip-checkbox.selected {
    background: #94bfff;
  }
  .checkbox-demo-tip-checkbox input[type='checkbox'] {
    accent-color: #4080ff;
  }
</style>
