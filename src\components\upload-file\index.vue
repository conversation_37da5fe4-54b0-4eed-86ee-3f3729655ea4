<script setup lang="ts">
  import { ref, computed, defineProps, defineEmits, watchEffect } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import { AliOSS } from '@/utils/AliOSS';
  import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';
  import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
  import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';

  const props = defineProps({
    modelValue: {
      type: Array as () => ShowComponentUploadFileItemModel[],
      required: true,
    },
    width: {
      type: Number,
      default: 80,
      required: true,
    },
    height: {
      type: Number,
      default: 80,
      required: true,
    },
    rename: {
      type: Boolean,
      default: false,
    },
    maxCount: {
      type: Number,
      default: ActivitySettingLimitValue.activityModuleUploadFileCountMax,
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const maxLimit = computed(() => {
    return (
      props.maxCount ??
      ActivitySettingLimitValue.activityModuleUploadFileCountMax
    );
  });

  const localFilList = ref<ShowComponentUploadFileItemModel[]>(
    props.modelValue
  );

  // 图片预览相关
  const imagePreview = ref({
    url: '',
    visible: false,
  });

  // 创建阿里云 OSS 实例
  const aliOSS = new AliOSS();

  // 文件上传方法
  const uploadFile = async (option: any) => {
    try {
      await aliOSS.uploadFile(
        option,
        localFilList.value,
        (percent) => {},
        () => {
          Message.success('✅ 上传成功');
          console.log('图片上传成功', localFilList.value, [
            ...localFilList.value,
          ]);
          emit('update:modelValue', [...localFilList.value]);
        },
        (error) => {
          Modal.error({
            title: '⚠️ 文件上传失败',
            content: `文件上传失败，原因为：${error}`,
          });
        },
        (reason) => {
          Modal.warning({
            title: '❌ 上传失败',
            content: `文件上传失败，原因为：${reason}`,
          });
        }
      );
    } catch (e) {
      Modal.error({
        title: '😓 文件上传出错',
        content: `文件上传失败，原因为：${e}`,
      });
    }
  };

  // 删除文件
  const fileDelete = (index: number) => {
    localFilList.value.splice(index, 1);
    emit('update:modelValue', [...localFilList.value]);
  };

  // 预览
  const imageShowPreview = (url: string) => {
    imagePreview.value = {
      url,
      visible: true,
    };
  };

  // 关闭预览
  const imagePreviewClose = () => {
    imagePreview.value.visible = false;
  };

  // 监听外部数据变化
  watchEffect(() => {
    localFilList.value = props.modelValue || [];
  });
</script>

<template>
  <div class="upload-file-block">
    <div
      v-for="(llItem, index) in localFilList"
      :key="index"
      class="image-item-block"
    >
      <div
        v-if="llItem.progress.status === EnumFileUploadStatue.Success"
        class="upload-success-block file-block"
        :style="`width:${props.width}px;height:${props.height}px`"
      >
        <div class="image-space">
          <img
            :src="llItem.url"
            :width="props.width"
            :height="props.height"
            loading="lazy"
          />
          <div class="overlay">
            <icon-eye
              size="20"
              style="color: #fff"
              @click="imageShowPreview(llItem.url)"
            />
            <IconDelete
              size="20"
              style="color: #fff; margin-left: 10px"
              @click="fileDelete(index)"
            />
          </div>
        </div>
      </div>
      <div
        v-else-if="llItem.progress.status === EnumFileUploadStatue.UpLoading"
        class="upload-load-block file-block"
        :style="`width:${props.width}px;height:${props.height}px`"
      >
        <a-progress
          :percent="llItem.progress.percent"
          type="circle"
          animation
        />
      </div>
      <div
        v-else-if="llItem.progress.status === EnumFileUploadStatue.Empty"
        class="upload-empty-block file-block"
        :style="`width:${props.width}px;height:${props.height}px`"
      >
        <a-upload
          action="/"
          :data="index"
          :custom-request="uploadFile"
          :show-file-list="false"
          :auto-upload="true"
        >
          <template #upload-button>
            <div class="arco-upload-list-item">
              <div class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconUpload size="20" />
                  <div class="add-content">添加图片</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </div>
      <!--      v-if="llItem.progress.status === EnumFileUploadStatue.Fail"-->
      <div
        v-else
        class="upload-fail-block file-block"
        :style="`width:${props.width}px;height:${props.height}px`"
      >
        <div class="image-space">
          <img
            src="/"
            :width="props.width"
            :height="props.height"
            loading="lazy"
          />
          <div class="overlay">
            <IconDelete
              size="20"
              style="color: #fff"
              @click="fileDelete(index)"
            />
          </div>
        </div>
      </div>
      <!-- 重命名输入框 -->
      <div
        v-if="props.rename"
        class="input-block"
        :style="`width:${props.width + 10}px`"
      >
        <a-input v-model="llItem.label" placeholder="图片名称" />
      </div>
    </div>

    <!-- 添加按钮 -->
    <div v-if="localFilList.length < maxLimit" class="image-item-block">
      <div class="image-space">
        <a-upload
          action="/"
          :custom-request="uploadFile"
          :show-file-list="false"
          :auto-upload="true"
          multiple
          :limit="maxLimit - localFilList.length"
        >
          <template #upload-button>
            <div class="arco-upload-list-item">
              <div class="arco-upload-picture-card">
                <div class="arco-upload-picture-card-text">
                  <IconUpload size="20" />
                  <div>添加图片</div>
                </div>
              </div>
            </div>
          </template>
        </a-upload>
      </div>
    </div>
  </div>
  <a-image-preview
    v-model:visible="imagePreview.visible"
    :src="imagePreview.url"
    @close="imagePreviewClose"
  />
</template>

<style scoped lang="less">
  @import './index.scss';
</style>
