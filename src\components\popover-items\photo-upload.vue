<template>
  <div class="photo-upload-demo-tip-card">
    <div class="photo-upload-demo-tip-title">正片上传</div>
    <div class="photo-upload-demo-tip-desc">
      正片上传组件的适用场景主要在于自由行活动的报名，用户可通过该组件上传其报名时发布的高清图片，同时在主办方的审批端口可查看、下载其高清图片用于公示名单、或活动现场物料制作。支持灵活设置需上传的数量
    </div>
    <div class="photo-upload-demo-tip-example">
      <div class="photo-upload-demo-tip-input demo-first"></div>
      <div class="photo-upload-demo-tip-imgs">
        <div class="photo-upload-demo-tip-img img-selected">
          <div class="photo-upload-demo-tip-img-plus">+</div>
        </div>
        <div class="photo-upload-demo-tip-img">
          <div class="photo-upload-demo-tip-img-plus">+</div>
        </div>
        <div class="photo-upload-demo-tip-img">
          <div class="photo-upload-demo-tip-img-plus">+</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .photo-upload-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .photo-upload-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .photo-upload-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .photo-upload-demo-tip-example {
    background: #e8f3ff;
    border-radius: 12px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
  .photo-upload-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
  .photo-upload-demo-tip-imgs {
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%;
    justify-content: flex-start;
    background: #e8f3ff;
    border-radius: 12px;
  }
  .photo-upload-demo-tip-img {
    width: 68px;
    height: 68px;
    background: #fff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.08);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: none;
  }
  .img-selected {
    border: 2.5px solid #4080ff;
    box-sizing: border-box;
  }
  .photo-upload-demo-tip-img-plus {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 38px;
    color: #94bfff;
    font-weight: 600;
  }
</style>
