<template>
  <div class="container">
    <Breadcrumb :items="['邀请票订单管理', '邀请票订单']" />
    <div style="width: 100%; display: flex; justify-content: left">
      <a-card class="general-card" title="邀请票订单" style="width: 100%">
        <a-row>
          <a-col :flex="1">
            <a-form
              ref="searchFormRef"
              :model="searchModel"
              label-align="left"
              auto-label-width
              :label-col-props="{ span: 6 }"
              :wrapper-col-props="{ span: 18 }"
            >
              <a-row :gutter="16">
                <a-col :span="span">
                  <a-form-item field="orderItemId" label="卷码">
                    <a-input
                      v-model="searchModel.orderItemId"
                      placeholder="请输入卷码"
                      style="width: 90%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="status" label="订单状态">
                    <a-select
                      v-model="searchModel.orderStatus"
                      placeholder="请选择订单状态"
                      style="width: 90%"
                    >
                      <a-option
                        v-for="(tagItem, tagIndex) in orderStatus"
                        :key="tagIndex"
                        :value="tagItem.value"
                        >{{ tagItem.label }}</a-option
                      >
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col v-if="false" :span="span">
                  <a-form-item field="userCn" label="用户CN">
                    <a-input
                      v-model="searchModel.userCn"
                      placeholder="请输入用户CN"
                      style="width: 90%"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col v-if="false" :span="span">
                  <a-form-item field="mobile" label="联系方式">
                    <a-input
                      v-model="searchModel.mobile"
                      placeholder="请输入联系方式"
                      style="width: 90%"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="loginMobile" label="登录手机号">
                    <a-input
                      v-model="searchModel.loginMobile"
                      placeholder="请输入登录手机号"
                      style="width: 90%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="goodsName" label="商品名称">
                    <a-input
                      v-model="searchModel.goodsName"
                      placeholder="请输入商品名称"
                      style="width: 90%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="goodsItemName" label="卷名称">
                    <a-input
                      v-model="searchModel.goodsItemName"
                      placeholder="请输入卷名称"
                      style="width: 90%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="startDate" label="场次日期">
                    <a-range-picker
                      v-model="searchModel.signUpDate"
                      style="width: 90%"
                      @change="handleDateRangeChange"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="span">
                  <a-form-item field="createTime" label="发放时间">
                    <a-range-picker
                      v-model="searchModel.createTime"
                      style="width: 90%"
                      @change="handleCreateTimeChange"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-col>
          <a-col :flex="'80px'" style="text-align: right">
            <a-space direction="vertical" :size="18">
              <a-button type="primary" @click="handleSearch">
                <template #icon>
                  <icon-search />
                </template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon>
                  <icon-refresh />
                </template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
        <a-divider style="margin-top: 0" />
        <a-table
          row-key="orderItemId"
          :pagination="listTablePagination"
          :data="renderData"
          :bordered="false"
        >
          <template #columns>
            <a-table-column title="卷码" data-index="orderItemId" />
            <a-table-column title="订单状态" data-index="status">
              <template #cell="{ record }">
                <div
                  class="d-tag"
                  :style="getOrderStatusInfo(record.status)?.style"
                  >{{ getOrderStatusInfo(record.status)?.label }}</div
                >
              </template>
            </a-table-column>
            <a-table-column title="用户CN" data-index="userCn"></a-table-column>
            <a-table-column title="联系方式" data-index="mobile" />
            <a-table-column title="登录手机号" data-index="loginMobile" />
            <a-table-column title="商品名称" data-index="goodsName" />
            <a-table-column title="卷名称" data-index="goodsItemName" />
            <a-table-column title="场次日期" data-index="signUpDate" />
            <a-table-column title="发放时间" data-index="createTime" />
            <a-table-column title="作废原因" data-index="cancellationReason" />
            <a-table-column title="操作员" data-index="operationId" />
            <a-table-column title="操作" data-index="operation">
              <template #cell="{ record }">
                <a-link v-if="record.status == 1" @click="handleClick(record)"
                  >作废</a-link
                >
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-card>
    </div>
    <a-modal :visible="visible" :footer="false">
      <template #title>
        <div style="display: flex; align-items: center; gap: 8px">
          <icon-exclamation-circle style="color: #faad14; font-size: 18px" />
          <span style="font-size: 16px; font-weight: 500"
            >确认要作废该券码吗</span
          >
        </div>
      </template>
      <div style="padding: 0 8px 8px 8px">
        <div style="color: #666; font-size: 14px; margin-bottom: 20px"
          >作废后此券码将作废，无法正常核验，请选择作废原因</div
        >
        <div style="display: flex; gap: 12px; margin-bottom: 16px">
          <a-button
            v-for="item in reasonList"
            :key="item"
            :status="selectedReason === item ? 'normal' : undefined"
            style="
              min-width: 110px;
              border-radius: 6px;
              font-size: 14px;
              padding: 0 16px;
              height: 36px;
            "
            @click="selectReason(item)"
          >
            <template v-if="selectedReason === item">
              <icon-check-circle-fill />
            </template>
            {{ item }}
          </a-button>
        </div>
        <div style="position: relative; margin-bottom: 24px">
          <a-input
            v-model="otherReason"
            :max-length="20"
            placeholder="其他原因"
            style="
              height: 40px;
              border-radius: 6px;
              font-size: 14px;
              background: #f7f8fa;
              border: none;
              padding-right: 48px;
            "
            @focus="selectedReason = ''"
          />
        </div>
        <div style="display: flex; justify-content: flex-end; gap: 16px">
          <a-button
            style="min-width: 72px; height: 36px; border-radius: 6px"
            @click="handleCancel"
          >
            取消
          </a-button>
          <a-button
            type="primary"
            style="min-width: 72px; height: 36px; border-radius: 6px"
            @click="handleOk"
          >
            确定
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted, onUnmounted, computed } from 'vue';
  import { Message, Pagination } from '@arco-design/web-vue';
  import useLoading from '@/hooks/loading';
  import { OrderListAPI, pageParams } from '@/api/list';
  import { ValidateUtils } from '@/utils/validate';

  const { loading, setLoading } = useLoading(true);

  const searchFormRef = ref();

  interface SearchModel extends pageParams {
    orderItemId?: string; // 卷码
    orderStatus?: number; // 订单状态
    userCn?: string; // 用户CN
    mobile?: string; // 联系方式
    loginMobile?: string; // 登录手机号
    goodsName?: string; // 商品名称
    goodsItemName?: string; // 卷名称
    signUpDate?: string[]; // 场次日期
    startDate?: string; // 场次开始日期
    endDate?: string; // 场次结束日期
    createTime?: string[]; // 发放时间
    provideStartTime?: string; // 发放开始
    provideEndTime?: string; // 发放结束
    cancellationReason?: string;
    operationId?: string; // 操作员
  }

  /**
   * 搜索参数
   */
  const searchModel = reactive<SearchModel>({} as SearchModel);
  /**
   * 分页数据
   */
  const renderData = ref<any[]>([]);

  /**
   * 列表分页
   */
  const listTablePagination = reactive<Pagination>({
    current: 1,
    pageSize: 10,
    total: 0,
    showTotal: true,
    hideOnSinglePage: true,
    onChange: (current: number, pageSize: number) => {
      searchModel.page = current;
      searchModel.limit = pageSize;
      handleSearch();
    },
    onPageSizeChange: (pageSize: number) => {
      searchModel.page = 1;
      searchModel.limit = pageSize;
      handleSearch();
    },
  });

  /**
   * 搜索
   */
  const handleSearch = () => {
    setLoading(true);
    OrderListAPI.queryInspectionOrderItemPageList(searchModel)
      .then((res) => {
        renderData.value = res.data.list;
        listTablePagination.current = res.data.page;
        listTablePagination.total = res.data.total;
        listTablePagination.pageSize = res.data.limit;
      })
      .catch((err) => {
        Message.error(ValidateUtils.isNotEmpty(err) ? err : '请求失败');
      })
      .finally(() => {
        setLoading(false);
      });
  };
  /**
   * 重置
   */
  const handleReset = () => {
    console.log('handleReset');
    searchFormRef.value.resetFields();
    console.log(searchFormRef.value);
    handleSearch();
  };

  const getOrderStatusInfo = (status: number) => {
    return orderStatus.find((item) => item.value == status);
  };

  const orderStatus = [
    {
      label: '待核销',
      value: 1,
      style: {
        backgroundColor: '#E8F7FF',
        color: '#3491FA',
      },
    },
    {
      label: '已核销',
      value: 3,
      style: {
        backgroundColor: '#E8FFEA',
        color: '#00B42A',
      },
    },
    {
      label: '请假',
      value: 5,
      style: {
        backgroundColor: '#FFFCE8',
        color: '#F7BA1E',
      },
    },
    {
      label: '已过期',
      value: 6,
      style: {
        backgroundColor: '#F2F3F5',
        color: '#1D2129',
      },
    },
    {
      label: '已作废',
      value: 7,
      style: {
        backgroundColor: '#FFECE8',
        color: '#F53F3F',
      },
    },
  ];

  // 作废面板显示
  const visible = ref(false);
  // 作废选择的订单项id
  const currentOrderItemId = ref<undefined | string>(); // 添加当前选中的orderItemId

  /**
   * 作废
   * @param record
   */
  const handleClick = (record: any) => {
    if (record.status === 1) {
      // 修改：接收record参数
      currentOrderItemId.value = record.orderItemId; // 保存当前orderItemId
      visible.value = true;
    }
  };

  // 作废原因相关变量和方法
  const reasonList = ['发错卷码', '用户无法到场'];
  // 作废原因 - 通过选择
  const selectedReason = ref<undefined | string>();
  // 作废原因 - 通过输入
  const otherReason = ref<undefined | string>();

  const restDelInfo = () => {
    currentOrderItemId.value = undefined;
    selectedReason.value = undefined;
    otherReason.value = undefined;
  };

  /**
   * 选择作废原因
   * @param item
   */
  function selectReason(item: string) {
    selectedReason.value = item;
    otherReason.value = undefined;
  }

  /**
   * 确定作废
   */
  const handleOk = () => {
    // 确定最终的作废原因
    const finalReason = selectedReason.value || otherReason.value;

    if (!finalReason) {
      // 可以添加提示用户选择或输入原因
      console.warn('请选择或输入作废原因');
      return;
    }
    setLoading(true);
    // 调用作废接口，传入orderItemId和reason
    OrderListAPI.queryCancelInspectionOrderItem({
      orderItemId: currentOrderItemId.value!,
      reason: finalReason,
    })
      .then(() => {
        Message.success('作废成功');
      })
      .catch((err) => {
        Message.error(`作废失败：${err}`);
      })
      .finally(() => {
        visible.value = false;
        restDelInfo();
        setLoading(true);
        handleReset();
      });
  };

  const handleCancel = () => {
    visible.value = false;
    restDelInfo();
  };

  const handleDateRangeChange = () => {
    if (searchModel.signUpDate) {
      searchModel.startDate = `${searchModel.signUpDate[0]} 00:00:00`;
      searchModel.endDate = `${searchModel.signUpDate[1]} 23:59:59`;
    }
  };
  const handleCreateTimeChange = () => {
    if (searchModel.createTime) {
      searchModel.provideStartTime = `${searchModel.createTime[0]} 00:00:00`;
      searchModel.provideEndTime = `${searchModel.createTime[1]} 23:59:59`;
    }
  };

  const span = ref(6);
  const handleResize = () => {
    const width = window.innerWidth;
    if (width < 768) {
      span.value = 24;
    } else if (width < 992) {
      span.value = 12;
    } else if (width < 1200) {
      span.value = 8;
    } else if (width < 1921) {
      span.value = 7;
    } else {
      span.value = 6;
    }
  };
  onMounted(() => {
    console.log('init');
    handleSearch();
    window.addEventListener('resize', handleResize);
    handleResize();
  });

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
  });
</script>

<script lang="ts">
  export default {
    name: 'SearchTable',
  };
</script>

<style scoped lang="less">
  .container {
    padding: 0 20px 20px 20px;
  }
  :deep(.arco-table-th) {
    &:last-child {
      .arco-table-th-item-title {
        margin-left: 16px;
      }
    }
  }
  .action-icon {
    margin-left: 12px;
    cursor: pointer;
  }
  .active {
    color: #0960bd;
    background-color: #e3f4fc;
  }
  .setting {
    display: flex;
    align-items: center;
    width: 200px;
    .title {
      margin-left: 12px;
      cursor: pointer;
    }
  }
  @media screen and (max-width: 768px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 280px);
    }
  }

  @media screen and (max-width: 1920px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 500px);
    }
  }
  @media screen and (min-width: 1921px) {
    :deep(.arco-table-container) {
      max-height: calc(100vh - 500px);
    }
  }
  :deep(.arco-table-container) {
    overflow: auto;
  }
  :deep(.arco-form) {
    flex-wrap: wrap;
    display: flex;
    .arco-row {
      flex-direction: row;
    }
  }
  .void-modal {
    .void-modal-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    .void-modal-content {
      .void-modal-desc {
        font-size: 14px;
        margin-bottom: 16px;
      }
      .void-modal-reason-group {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 16px;
        .void-modal-reason-btn {
          margin-right: 8px;
          margin-bottom: 8px;
        }
      }
      .void-modal-input-wrap {
        position: relative;
        margin-bottom: 16px;
        .void-modal-input {
          width: 100%;
        }
        .void-modal-count {
          position: absolute;
          right: 10px;
          bottom: 10px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
      .void-modal-footer {
        display: flex;
        justify-content: flex-end;
        .void-modal-cancel {
          margin-right: 8px;
        }
      }
    }
  }

  .d-tag {
    width: 58px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
