// eslint-disable-next-line @typescript-eslint/no-var-requires
// 引入 Node.js 内置的 path 模块，用于处理文件路径
const path = require('path');

// ESLint 配置导出模块
module.exports = {
  // 设置当前目录为项目根目录
  root: true,

  // 使用 vue-eslint-parser 解析 Vue 单文件组件
  parser: 'vue-eslint-parser',

  // 解析器选项配置
  parserOptions: {
    // 使用 @typescript-eslint/parser 来解析 <script> 中的 TypeScript 代码
    parser: '@typescript-eslint/parser',
    // 设置模块化类型为 module
    sourceType: 'module',
    // 设置 ECMAScript 版本为 2020
    ecmaVersion: 2020,
    // 启用 JSX 支持（如果需要）
    ecmaFeatures: {
      jsx: true,
    },
  },

  // 定义代码运行环境
  env: {
    'browser': true, // 浏览器环境
    'node': true, // Node.js 环境
    'vue/setup-compiler-macros': true, // 支持 Vue 编译宏
  },

  // 加载 TypeScript 相关的插件
  plugins: ['@typescript-eslint'],

  // 继承多个 ESLint 配置规范
  extends: [
    // Airbnb JavaScript 规范 https://github.com/airbnb/javascript
    'airbnb-base',
    // TypeScript 推荐规则集
    'plugin:@typescript-eslint/recommended',
    // import 插件推荐规则
    'plugin:import/recommended',
    // TypeScript 的 import 规则支持
    'plugin:import/typescript',
    // Prettier 格式化规则集成
    'plugin:prettier/recommended',
  ],

  // 导入解析器设置
  settings: {
    'import/resolver': {
      // 配置 TypeScript 的解析器选项
      typescript: {
        // 指定 tsconfig.json 文件路径
        project: path.resolve(__dirname, './tsconfig.json'),
      },
    },
  },

  // 自定义 ESLint 规则覆盖默认配置
  rules: {
    // 开启 prettier 校验规则（警告级别）
    'prettier/prettier': 1,

    // Vue 规则：关闭或修改部分默认规则
    'vue/require-default-prop': 0, // 允许不给 props 设置默认值
    'vue/singleline-html-element-content-newline': 0, // 不强制要求单行元素内容换行
    'vue/max-attributes-per-line': 0, // 不限制每行属性数量

    // Vue 新增规则
    'vue/custom-event-name-casing': [2, 'camelCase'], // 自定义事件名必须使用驼峰命名
    'vue/no-v-text': 1, // 警告使用 v-text，建议使用 {{ }} 替代
    'vue/padding-line-between-blocks': 1, // 建议在 Vue 文件的块之间添加空行
    'vue/require-direct-export': 1, // 要求直接导出组件，而非包裹在变量中
    'vue/multi-word-component-names': 0, // 允许单单词组件名

    // 允许使用 @ts-ignore 注释忽略类型检查
    '@typescript-eslint/ban-ts-comment': 0,

    // TypeScript 规则设置
    '@typescript-eslint/no-unused-vars': 1, // 警告未使用的变量
    '@typescript-eslint/no-empty-function': 1, // 警告空函数
    '@typescript-eslint/no-explicit-any': 0, // 允许使用 any 类型
    // 自定义命名规范
    '@typescript-eslint/naming-convention': [
      'error',
      {
        // 所有非私有属性和变量不能以 _ 开头
        selector: [
          'variable',
          'function',
          'parameter',
          'property',
          'enumMember',
        ],
        format: ['camelCase', 'PascalCase'],
        leadingUnderscore: 'forbid',
      },
      {
        // 允许类私有属性使用 _ 开头
        selector: 'classProperty',
        modifiers: ['private'],
        format: ['camelCase'],
        leadingUnderscore: 'require',
      },
    ],

    // 导入规则设置，不强制写文件扩展名
    'import/extensions': [
      2,
      'ignorePackages',
      {
        js: 'never',
        jsx: 'never',
        ts: 'never',
        tsx: 'never',
      },
    ],

    // 生产环境禁止使用 debugger
    'no-debugger': process.env.NODE_ENV === 'production' ? 2 : 0,

    // 允许对函数参数重新赋值
    'no-param-reassign': 0,

    // 允许使用正则字面量
    'prefer-regex-literals': 0,

    // 不检查导入的依赖是否为开发依赖
    'import/no-extraneous-dependencies': 0,

    // 关闭 import/prefer-default-export 规则
    'import/prefer-default-export': 'off',

    // 👇 禁用 no-underscore-dangle 规则
    'no-underscore-dangle': 'off',
  },
};
