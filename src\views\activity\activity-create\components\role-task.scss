.role-task {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    min-height: 500px;

    .add-button {
      width: 60%;
    }

    .body {
      width: 100%;
      min-width: 1140px;
      padding: 0 40px;

      .role-list {
        display: flex;
        flex-wrap: wrap;
        grid-gap: 6px;

        .role-item {
          display: flex;
          gap: 16px;
          align-items: center;
          justify-content: space-between;
          height: 46px;
          padding: 16px 22px;
          background: #f7f8fa;
          border: 2px solid #f7f8fa;
          border-radius: 4px;

          .role-name {
            min-width: 68px;
            color: var(--text-1, #1d2129);
            font-weight: 500;
            font-size: 16px;

            /* 16/CN-Medium */
            font-family: 'PingFang SC';
            font-style: normal;
            line-height: 24px; /* 150% */
          }
        }

        .currentRole {
          background: #e8f3ff;
          border: 2px solid #6aa1ff;
        }
      }

      :deep(.arco-divider-text) {
        color: var(--text-3, #86909c);
        font-weight: 400;
        font-size: 14px;

        /* 14/CN-Regular */
        font-family: 'PingFang SC';
        font-style: normal;
        line-height: 22px; /* 157.143% */
      }

      .role-task-body {
        display: grid;
        grid-template-columns: 3fr 4fr 3fr;
        gap: 12px;
        align-items: flex-start;
        align-self: stretch;

        .components {
          width: 100%;

          .components-body {
            display: flex;
            flex-direction: column;
            gap: 33px;
            align-items: flex-start;
            padding: 16px 22px;
            background: var(--fill-1, #f7f8fa);
            border-radius: 4px;

            .components-base {
              display: flex;
              flex-direction: column;
              gap: 14px;
              align-items: flex-start;
              align-self: stretch;

              .components-title {
                color: #1d2129;
                font-weight: 500;
                font-size: 20px;

                /* 20/CN-Medium */
                font-family: 'PingFang SC';
                font-style: normal;
                line-height: 28px; /* 140% */
              }

              .components-content {
                display: flex;
                flex-direction: column;
                gap: 14px;
                align-items: flex-start;
                align-self: stretch;

                .general-tools,
                .personal—details {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  align-items: flex-start;
                  align-self: stretch;

                  .subtitle {
                    color: var(--text-1, #1d2129);
                    font-weight: 500;
                    font-size: 16px;

                    /* 16/CN-Medium */
                    font-family: 'PingFang SC';
                    font-style: normal;
                    line-height: 24px; /* 150% */
                  }

                  .sub-frame {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 6px;
                    align-content: flex-start;
                    align-items: flex-start;
                    align-self: stretch;
                  }
                }
              }
            }

            .components-item {
              display: flex;
              flex-direction: column;
              gap: 14px;
              align-items: flex-start;
              align-self: stretch;

              .components-title {
                color: #1d2129;
                font-weight: 500;
                font-size: 20px;

                /* 20/CN-Medium */
                font-family: 'PingFang SC';
                font-style: normal;
                line-height: 28px; /* 140% */
              }

              .components-content {
                display: flex;
                flex-direction: column;
                gap: 14px;
                align-items: flex-start;
                align-self: stretch;
              }
            }
          }
        }

        .form {
          display: flex;
          flex-direction: column;
          gap: 12px;
          align-items: center;
          padding: 16px 0;
          background: var(--fill-1, #f7f8fa);
          border-radius: 4px;

          .title,
          .task {
            display: flex;
            align-items: center;
            align-self: stretch;
            justify-content: space-between;
            padding: 0 22px;

            .text {
              display: flex;
              gap: 6px;
              align-items: center;
              color: var(--text-1, #1d2129);
              font-weight: 500;
              font-size: 20px;

              /* 20/CN-Medium */
              font-family: 'PingFang SC';
              font-style: normal;
              line-height: 28px; /* 140% */
            }
          }

          .base-info {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: flex-start;
            align-self: stretch;

            .base-info-title {
              padding: 0 22px;
              color: var(--text-1, #1d2129);
              font-weight: 500;
              font-size: 16px;

              /* 16/CN-Medium */
              font-family: 'PingFang SC';
              font-style: normal;
              line-height: 24px; /* 150% */
            }

            .base-info-form {
              display: flex;
              flex-direction: column;
              gap: 20px;
              align-items: flex-start;
              align-self: stretch;
              max-width: 100%;

              .is-current {
                border: 1.5px solid #165dff;
                border-radius: 4px;
                box-shadow: 0 4px 16px rgb(0 0 0 / 7%);
              }

              .form-item:hover {
                box-shadow: 0 4px 16px rgb(0 0 0 / 7%);
              }


              .form-item {
                display: flex;
                flex-direction: column;
                gap: 4px;
                align-items: flex-start;
                align-self: stretch;
                padding: 10px 22px;
                cursor: move;

                .item-title {
                  display: flex;
                  gap: 4px;
                  align-items: center;
                  align-self: stretch;
                  padding-bottom: 8px;
                  color: var(--text-2, #4e5969);
                  font-weight: 400;
                  font-size: 14px;

                  /* 14/CN-Regular */
                  font-family: 'PingFang SC';
                  font-style: normal;
                  line-height: 22px; /* 157.143% */
                }

                .item-desc {
                  color: var(--text-3, #86909c);
                  font-weight: 400;
                  font-size: 12px;

                  /* 12/CN-Regular */
                  font-family: 'PingFang SC';
                  font-style: normal;
                  line-height: 20px; /* 166.667% */
                }

                .item-input {
                  display: flex;
                  flex-direction: column;
                  grid-gap: 12px;
                  width: 100%;
                }
              }
            }
          }

          .button {
            display: flex;
            gap: 2%;
            align-items: flex-start;
            align-self: stretch;

            div {
              width: 49%;

              :deep(.arco-btn) {
                width: 100%;
              }
            }
          }
        }

        .setting {
          .setting-body {
            display: flex;
            flex-direction: column;
            gap: 33px;
            align-items: flex-start;
            padding: 16px 22px;
            background: var(--fill-1, #f7f8fa);
            border-radius: 4px;

            .setting-frame {
              display: flex;
              flex-direction: column;
              gap: 14px;
              align-items: flex-start;
              width: 100%;

              .setting-title {
                color: var(--text-1, #1d2129);
                font-weight: 500;
                font-size: 20px;

                /* 20/CN-Medium */
                font-family: 'PingFang SC';
                font-style: normal;
                line-height: 28px; /* 140% */
              }

              .setting-form {
                display: flex;
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
                align-self: stretch;
                min-height: 500px;

                .form-required {
                  .form-required-label {
                    color: var(--text-1, #1d2129);
                    font-weight: 400;
                    font-size: 16px;

                    /* 16/CN-Regular */
                    font-family: 'PingFang SC';
                    font-style: normal;
                    line-height: 24px; /* 150% */
                  }
                }

                .form-format {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  align-items: flex-start;
                  align-self: stretch;

                  .form-format-title {
                    color: var(--text-1, #1d2129);
                    font-weight: 500;
                    font-size: 16px;

                    /* 16/CN-Medium */
                    font-family: 'PingFang SC';
                    font-style: normal;
                    line-height: 24px; /* 150% */
                  }

                  .form-format-content {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    align-self: stretch;
                  }
                }
              }
            }
          }
        }

        :deep(.arco-divider) {
          margin: 0;
        }
      }
    }
  }
}

.task-info{
  width: 100%;
  .task-info-title{
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}

