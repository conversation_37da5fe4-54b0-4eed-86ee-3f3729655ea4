<template>
  <div class="tw-flex tw-flex-col tw-gap-4 tw-h-full">
    <Audit-content-top />
    <Audit-content-middle />
    <Audit-content-bottom />
  </div>
</template>

<script setup lang="ts">
  import AuditContentTop from './components/audit-content-top.vue';
  import AuditContentMiddle from './components/audit-content-middle.vue';
  import AuditContentBottom from './components/audit-content-bottom.vue';
</script>

<script lang="ts">
  export default {
    name: 'AuditContent',
  };
</script>

<style lang="less" scoped></style>
