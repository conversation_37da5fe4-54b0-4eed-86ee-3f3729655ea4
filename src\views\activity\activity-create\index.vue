<template>
  <div class="container">
    <a-card class="general-card">
      <template #title>
        <div class="card-title">
          <div class="title">
            设置招募信息
          </div>
          <div class="steps">
            <a-steps
              v-model:current="step"
              line-less
              class="steps"
            >
              <a-step description="设置基本信息">
                设置招募信息
              </a-step>
              <a-step description="创建活动内包含的所有角色">
                创建角色&任务
              </a-step>
              <a-step description="设置报名详情及公示信息">
                报名设置
              </a-step>
              <a-step description="生成活动报名链接及二维码">
                发布
              </a-step>
            </a-steps>
          </div>
        </div>

      </template>
      <div class="wrapper">
        <div class="form">
          <keep-alive>
            <BaseInfo v-if="step === 1" @change-step="changeStep" />
            <RoleTask v-else-if="step === 2" @change-step="changeStep" />
            <Success v-else-if="step === 3" @change-step="changeStep" />
          </keep-alive>
        </div>
        <div class="preview"></div>
      </div>
    </a-card>
    <div class="phone-preview"></div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import useLoading from '@/hooks/loading';
  import {
    submitChannelForm,
    BaseInfoModel,
    UnitChannelModel
  } from '@/api/form';
  import BaseInfo from './components/base-info.vue';
  import RoleTask from './components/role-task.vue';
  import Success from './components/success.vue';

  const { loading, setLoading } = useLoading(false);
  const step = ref(2);
  const submitModel = ref<UnitChannelModel>({} as UnitChannelModel);
  const submitForm = async () => {
    setLoading(true);
    try {
      await submitChannelForm(submitModel.value); // The mock api default success
      step.value = 3;
      submitModel.value = {} as UnitChannelModel; // init
    } catch (err) {
      // you can report use errorHandler or other
    } finally {
      setLoading(false);
    }
  };
  const changeStep = (
    direction: string | number,
    model: BaseInfoModel
  ) => {
    if (typeof direction === 'number') {
      step.value = direction;
      return;
    }

    if (direction === 'forward' || direction === 'submit') {
      submitModel.value = {
        ...submitModel.value,
        ...model
      };
      if (direction === 'submit') {
        submitForm();
        return;
      }
      step.value += 1;
    } else if (direction === 'backward') {
      step.value -= 1;
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'Step'
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px 20px 20px 20px;
    overflow: hidden;


    display: flex;
    grid-gap: 20px;
    overflow: hidden;

    .general-card {
      width: 100%;
      overflow: hidden;

      .card-title {
        overflow: hidden;
        color: var(---color-text-1, #1D2129);
        display: flex;
        justify-content: start;
        grid-gap: 20px;

        .title {
          /* 20/CN-Medium */
          font-family: "PingFang SC";
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: 28px; /* 140% */

        }

        .steps {
          overflow: hidden;

          :deep(.arco-steps-item-content) {
            div {
              max-width: 200px;
              width: 150px;
            }

          }
        }
      }


      .wrapper {
        display: flex;
        align-items: flex-start;
        grid-gap: 20px;
        padding: 36px 0;
        background-color: var(--color-bg-2);

        .form {
          display: flex;
          justify-content: center;
          align-items: center;
          width: calc(100% - 220px);
        }

        .preview {
          width: 220px;
          height: 481px;
        }
      }
    }

    .phone-preview {
      width: 220px;
      height: 481px;
      background: #e3f4fc;
      position: fixed;
      right: 40px;
      top: 206px;
    }
  }


</style>
