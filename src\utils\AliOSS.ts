import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';
import { UploadApi } from '@/api/upload/api';
import { ValidateUtils } from '@/utils/validate';
import { ShowComponentUploadFileItemModel } from '@/components/module/type/show/activity-upload-component-type';

// 定义回调类型
export type UploadProgressCallback = (
  percent: number,
  event: ProgressEvent
) => void;
export type UploadSuccessCallback = (response: string) => void;
export type UploadErrorCallback = (error: string) => void;
export type UploadFailCallback = (reason: string) => void;

export class AliOSS {
  private readonly _baseURL =
    'https://manxing-myxq.oss-cn-shanghai.aliyuncs.com';

  private readonly _folder = 'merchant/activity/upload/';

  /**
   * 通用文件上传方法
   *
   * @param option 请求参数
   * @param fileListRef 文件列表响应式引用（用于更新状态）
   * @param onProgress 进度回调
   * @param onSuccess 成功回调
   * @param onError 错误回调
   * @param onFail 失败回调
   */
  public async uploadFile(
    option: RequestOption,
    fileListRef: ShowComponentUploadFileItemModel[],
    onProgress?: UploadProgressCallback,
    onSuccess?: UploadSuccessCallback,
    onError?: UploadErrorCallback,
    onFail?: UploadFailCallback
  ) {
    const { fileItem, data } = option;
    const { name: fileName, file: fileRef } = fileItem;

    let fileInnerIndex: number;

    if (!fileListRef) {
      fileListRef = [];
    }

    if (ValidateUtils.isNotEmpty(data)) {
      fileInnerIndex = Number(data?.toString());
    } else {
      fileListRef.push({
        url: '',
        label: '',
        progress: {
          status: EnumFileUploadStatue.UpLoading,
          percent: 0,
        },
      });
      fileInnerIndex = fileListRef.length - 1;
    }

    if (!fileRef || !fileName) {
      alert('未找到文件');
      return;
    }

    try {
      // 获取 OSS 签名信息
      const res = await UploadApi.GetALiOssUploadInfo();
      const resData = JSON.parse(res.data);

      const ossFormData = new FormData();
      ossFormData.append('key', this._folder + fileName);
      ossFormData.append('policy', resData.policy);
      ossFormData.append(
        'x-oss-signature-version',
        resData.x_oss_signature_version
      );
      ossFormData.append('x-oss-credential', resData.x_oss_credential);
      ossFormData.append('x-oss-date', resData.x_oss_date);
      ossFormData.append('x-oss-signature', resData.signature);
      ossFormData.append('x-oss-security-token', resData.security_token);
      ossFormData.append('success_action_status', '200');
      ossFormData.append('file', fileRef);

      const xhr = new XMLHttpRequest();

      // 进度监听
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const percent = e.loaded / e.total;

          const percentFixed = parseInt(percent.toFixed(0), 10);
          if (!fileListRef[fileInnerIndex].progress) {
            fileListRef[fileInnerIndex].progress = {
              percent: percentFixed,
              status: EnumFileUploadStatue.UpLoading,
            };
          } else {
            fileListRef[fileInnerIndex].progress.percent = percentFixed;
          }
          console.log(fileListRef[fileInnerIndex]);
          onProgress?.(percentFixed, e);
        }
      };

      // 成功监听
      xhr.onload = () => {
        if (xhr.status === 200 || xhr.status === 204) {
          fileListRef[fileInnerIndex].progress.status =
            EnumFileUploadStatue.Success;
          fileListRef[
            fileInnerIndex
          ].url = `${this._baseURL}\\${this._folder}${fileName}`;
          onSuccess?.(xhr.response);
        } else {
          fileListRef[fileInnerIndex].progress.status =
            EnumFileUploadStatue.Fail;
          onFail?.(xhr.responseText);
        }
      };

      // 错误监听
      xhr.onerror = () => {
        fileListRef[fileInnerIndex].progress.status =
          EnumFileUploadStatue.Error;
        onError?.(xhr.responseText);
      };

      // 发送请求
      xhr.open('POST', this._baseURL, true);
      xhr.send(ossFormData);
    } catch (error) {
      console.error('获取上传信息失败:', error);
      onError?.('获取上传配置失败');
    }
  }
}
