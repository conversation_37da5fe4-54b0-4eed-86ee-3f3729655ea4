import { ResponseGetModuleItemModel } from '@/api/module/type';
import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';

export interface StoreModuleItemModel {
  icon?: string;
  title: string;
  attribute: ResponseGetModuleItemModel;
}

export interface StoreModuleStateModel {
  modules: ResponseGetModuleItemModel[];
  existModulesMap: Map<string, ResponseGetModuleItemModel>;
  metaModulesMap: Map<string, ResponseGetModuleItemModel>;
}

export interface StoreComponentStateModel {
  metaComponentMap: Map<string, ComponentUnifiedAttributeModel>;
}

export interface StoreFileTypeStateChildItemModel {
  key: string;
  title: string;
}

export interface StoreFileTypeStateItemModel {
  key: string;
  title: string;
  children: StoreFileTypeStateChildItemModel[];
}

export interface StoreFileTypeStateModel {
  fileTypes: StoreFileTypeStateItemModel[];
}
