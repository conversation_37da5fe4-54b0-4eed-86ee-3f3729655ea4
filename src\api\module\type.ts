import { ComponentUnifiedAttributeModel } from '@/components/module/type/attribute-model-unified';

export interface ResponseGetModuleItemModel {
  /** 数据库唯一Id,前端不用考虑 */
  id?: string;
  /** 模组key - uuid */
  uuid: string;
  /** 组件标识。用于区分是包装组件还是用户自定义组件 */
  key?: string;
  /**  模组内标题部分内容 */
  title: string;
  /**  模组内标题描述部分内容 */
  description?: string;
  /**  是否必填 */
  required?: boolean;
  /**  是否仅展示，无需填写 -- 说明文字部分input-area */
  readonly?: boolean;

  list: ComponentUnifiedAttributeModel[];
}

export interface ResponseGetComponentModel {
  list: ComponentUnifiedAttributeModel[];
}
