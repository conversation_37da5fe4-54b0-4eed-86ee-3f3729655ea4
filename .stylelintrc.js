/**
 * 导出一个配置对象，用于设置Stylelint的配置规则
 * 这个配置对象定义了代码的样式规则、插件以及一些特定规则的配置
 */
module.exports = {
  /**
   * 继承其他配置，以确保代码风格的一致性和规范性
   * 包括标准配置、合理顺序配置、与Prettier兼容的配置以及Vue推荐配置
   */
  extends: [
    'stylelint-config-standard',
    'stylelint-config-rational-order',
    'stylelint-config-prettier',
    'stylelint-config-recommended-vue',
  ],

  // 设置默认的错误严重程度为警告，以提醒开发者潜在的样式问题
  defaultSeverity: 'warning',

  // 使用stylelint-order插件，以强制规则的特定顺序
  plugins: ['stylelint-order'],

  /**
   * 定义具体的样式规则，包括但不限于：
   * 1. 不允许出现未知的@规则，但插件相关的@规则除外
   * 2. 在规则之前要求有一个空行，但某些情况除外
   * 3. 不允许出现未知的伪类选择器，但'deep'伪类除外
   */
  rules: {
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: ['plugin'],
      },
    ],
    'rule-empty-line-before': [
      'always',
      {
        except: ['after-single-line-comment', 'first-nested'],
      },
    ],
    'selector-pseudo-class-no-unknown': [
      true,
      {
        ignorePseudoClasses: ['deep'],
      },
    ],
  },
};
