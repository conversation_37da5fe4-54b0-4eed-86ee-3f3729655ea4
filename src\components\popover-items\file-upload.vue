<template>
  <div class="file-demo-tip-card">
    <div class="file-demo-tip-title">文件上传</div>
    <div class="file-demo-tip-desc">
      文件上传功能用于收集答题者的作品、简历、图片、文件等，可以帮您轻松收集文件数据。
    </div>
    <div class="file-demo-tip-example">
      <div class="file-demo-tip-input demo-first"></div>
      <div class="file-demo-tip-upload">
        <icon-upload :style="{ color: '#94BFFF', fontSize: '20px' }" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .file-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .file-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .file-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .file-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .file-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 4px;
  }
  .file-demo-tip-upload {
    width: 100%;
    min-height: 50px;
    border: 1.5px solid #94bfff;
    border-radius: 6px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
