export class IOUtils {
  /**
   * 异步复制文本到剪贴板（兼容模式）
   */
  public static fallbackCopyTextToClipboardAsync = (
    text: string
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      document.body.appendChild(textarea);
      textarea.select();
      try {
        const success = document.execCommand('copy');
        resolve(success);
      } catch (e) {
        console.error('execCommand 复制失败:', e);
        resolve(false);
      } finally {
        document.body.removeChild(textarea);
      }
    });
  };

  /**
   * 主复制方法：优先使用 Clipboard API，失败后回退
   */
  public static CopyToClipboard = async (text: string): Promise<boolean> => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (err) {
      console.error('剪贴板 API 复制失败:', err);
      return this.fallbackCopyTextToClipboardAsync(text);
    }
  };
}
