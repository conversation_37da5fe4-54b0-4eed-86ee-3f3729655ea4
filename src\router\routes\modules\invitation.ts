import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const Invitation: AppRouteRecordRaw = {
  path: '/invitation',
  name: 'invitation',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '邀请票订单管理',
    icon: 'icon-settings',
    requiresAuth: true,
    order: 1,
  },
  children: [
    {
      path: 'invitation-create',
      name: 'InvitationCreate',
      component: () => import('@/views/invitation/invitation-create/index.vue'),
      meta: {
        hideInMenu: true,
        locale: '创建活动',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'invitation-list',
      name: 'InvitationList',
      component: () => import('@/views/invitation/invitation-list/index.vue'),
      meta: {
        locale: '邀请票发放',
        requiresAuth: true,
        roles: ['*'],
      },
    },
    {
      path: 'invitation-order-list',
      name: 'InvitationOrderList',
      component: () =>
        import('@/views/invitation/invitation-order-list/index.vue'),
      meta: {
        locale: '邀请票订单',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default Invitation;
