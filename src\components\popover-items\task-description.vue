<template>
  <div class="task-demo-tip-card">
    <div class="task-demo-tip-title">任务描述</div>
    <div class="task-demo-tip-desc">
      任务描述支持灵活配置任务要求，是作为规则展示的文本，在这里面可以写明角色报名时需完成的任务条件，如：发布官方照片x张；现场照片x张、集赞满足xx个、转发满足xx个等；<br />
      同时，漫星配备了多种模板可供快速选择。
    </div>
    <div class="task-demo-tip-example">
      <div class="task-demo-tip-input demo-first"></div>
      <textarea
        class="task-demo-tip-textarea"
        placeholder="规则说明：&#10;1.xxxx"
        disabled
      />
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .task-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .task-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .task-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .task-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .task-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
  .task-demo-tip-textarea {
    width: 100%;
    min-height: 70px;
    border: none;
    border-radius: 6px;
    background: #fff;
    resize: none;
    padding: 8px 12px;
    font-size: 10px;
    color: #94bfff;
    box-sizing: border-box;
    outline: none;
    border: 1px solid #94bfff;
  }
  .task-demo-tip-textarea:disabled {
    color: #94BFFF;
    background: #fff;
  }
</style>
