import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const ACTIVITY: AppRouteRecordRaw = {
  path: '/activity',
  name: 'activity',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '活动管理',
    icon: 'icon-settings',
    requiresAuth: true,
    order: 2,
  },
  children: [
    {
      path: 'activity-create',
      name: 'ActivityCreate',
      component: () => import('@/views/activity/activity-create/index.vue'),
      meta: {
        // hideInMenu: true,
        locale: '活动管理&创建',
        requiresAuth: true,
        roles: ['*'],
      },
    },
  ],
};

export default ACTIVITY;
