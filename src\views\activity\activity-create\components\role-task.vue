<template>
  <div class="role-task">
    <div class="content">
      <div v-if="formData.length == 0" class="add-button">
        <a-button
          type="primary"
          size="large"
          style="width: 100%; height: 54px; font-size: 24px"
          @click="addRoleClick"
        >
          <template #icon>
            <icon-plus />
          </template>
          创建角色
        </a-button>
      </div>
      <div v-else class="body">
        <div class="role-list">
          <div
            v-for="(item, index) in formData"
            :key="index"
            class="role-item"
            :class="index == currentRoleIndex ? 'currentRole' : ''"
            @click="roleChange(index)"
          >
            <div class="role-name">{{ item.id }}</div>
            <div v-if="formData.length > 1" @click.stop="roleItemRemove(index)">
              <icon-close size="20" />
            </div>
          </div>
          <div v-if="formData.length < 8" class="role-add">
            <a-button
              type="primary"
              size="large"
              style="
                width: 100%;
                height: 46px;
                font-size: 16px;
                border-radius: 4px;
              "
              @click="addRoleClick"
            >
              <template #icon>
                <icon-plus />
              </template>
              创建新角色({{ formData.length }}/8)
            </a-button>
          </div>
        </div>
        <a-divider type="dashed">以下为角色&任务设置</a-divider>
        <div class="role-task-body">
          <div class="components">
            <a-affix offset-top="80">
              <div class="components-body">
                <div class="components-base components-item">
                  <div class="components-title">基础信息</div>
                  <BaseInfo
                    :used-module="formData[currentRoleIndex].baseInfo"
                    @item-add="addBaseItem"
                  />
                  <Common @item-add="addBaseItem" />
                </div>
                <div class="components-task components-item">
                  <div class="components-title">任务信息</div>
                  <TaskInfo
                    :used-module="formData[currentRoleIndex].taskInfo"
                    @item-add="addTaskItem"
                  />
                  <UserUpload @item-add="addTaskItem" />
                  <Common @item-add="addTaskItem" />
                </div>
              </div>
            </a-affix>
          </div>
          <!--     角色&任务等 主面板     -->
          <div class="form">
            <!-- 面板标题 角色名称 -->
            <div class="title">
              <div class="text">
                <div>
                  <div v-if="roleNameEditVisible">
                    <a-input
                      ref="roleNameEditRef"
                      v-model="formData[currentRoleIndex].id"
                      show-word-limit
                      :max-length="8"
                      @blur="roleNameEditVisible = false"
                    />
                  </div>
                  <div v-else>
                    {{ formData[currentRoleIndex].id }}
                  </div>
                </div>
                <div @click="roleNameEdit">
                  <icon-edit />
                </div>
              </div>
              <div>
                <a-button type="text" @click="copyRole">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制角色
                </a-button>
              </div>
            </div>
            <a-divider></a-divider>
            <!-- 基础信息编辑 -->
            <div class="base-info">
              <div class="base-info-title title-block">基本信息</div>
              <div ref="baseInfoItemListRef" class="base-info-form">
                <div
                  v-for="(item, index) in formData[currentRoleIndex].baseInfo"
                  :key="item.uuid"
                  class="form-item base-info-group-item"
                  :class="{
                    'is-current':
                      currentItem.type == EnumRoleContentType.BASE &&
                      index == currentItem.index,
                  }"
                  @click="itemGroupClicked(index, EnumRoleContentType.BASE)"
                >
                  <ModuleGroup
                    :group-entity="item.entity"
                    :current-flag="index == currentItem.index"
                  />
                </div>
              </div>
            </div>
            <a-divider></a-divider>

            <div class="task-info base-info">
              <div class="task-info-title title-block">
                <div class="title">任务要求</div>
                <div
                  v-if="formData[currentRoleIndex].taskInfo.length > 0"
                  class="copy-block"
                >
                  <a-button type="text" @click="handleCopyTaskInfo">
                    <template #icon>
                      <icon-copy />
                    </template>
                    复制任务
                  </a-button>
                </div>
              </div>
              <div class="task-platform-block">
                <div
                  v-for="(tItem, tIndex) in formData[currentRoleIndex].taskInfo"
                  :key="tIndex"
                  class="form-item-platform task-info-platform-item"
                  :class="{
                    'is-current':
                      currentItem.type == EnumRoleContentType.TASK &&
                      index == currentItem.index,
                  }"
                  @click="platformItemClicked(index, EnumRoleContentType.TASK)"
                >
                  <a-button>{{ tItem.title }}123</a-button>
                </div>
              </div>
              <div
                ref="taskInfoItemListRef"
                class="task-info-form base-info-form"
              >
                <div
                  v-for="(item, index) in formData[currentRoleIndex].taskInfo"
                  :key="item.uuid"
                  class="form-item task-info-group-item"
                  :class="{
                    'is-current':
                      currentItem.type == EnumRoleContentType.TASK &&
                      index == currentItem.index,
                  }"
                  @click="itemGroupClicked(index, EnumRoleContentType.TASK)"
                >
                  <ModuleGroup
                    :group-entity="item.entity"
                    :current-flag="index == currentItem.index"
                  />
                </div>
              </div>
            </div>
            <!---->
            <div
              v-if="formData[currentRoleIndex].taskInfo.length > 0"
              class="task"
            >
              <div class="text">
                <div>任务要求</div>
              </div>
              <div>
                <a-button type="text">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制任务
                </a-button>
              </div>
            </div>
            <div
              v-if="formData[currentRoleIndex].taskInfo.length > 0"
              class="button"
            >
              <div class="copy">
                <a-button type="outline">
                  <template #icon>
                    <icon-copy />
                  </template>
                  复制该任务
                </a-button>
              </div>
              <div class="add">
                <a-button type="primary">
                  <template #icon>
                    <icon-plus />
                  </template>
                  新增任务平台
                </a-button>
              </div>
            </div>
            <div
              v-if="formData[currentRoleIndex].taskInfo.length === 0"
              style="width: 100%"
            >
              <a-button type="primary" style="width: 100%; border-radius: 4px">
                添加任务平台（可选）
              </a-button>
            </div>
          </div>
          <!--     角色&任务等 设置面板     -->
          <div class="setting">
            <a-affix offset-top="80">
              <div class="setting-body">
                <div class="setting-frame">
                  <div class="setting-title">组件设置</div>
                  <SettingGroup :group-entity="currentGroupModule" />
                </div>
              </div>
            </a-affix>
            <div></div>
          </div>
        </div>
      </div>
    </div>
    <a-space>
      <a-button type="secondary" @click="goPrev"> 上一步</a-button>
      <a-button type="primary" @click="onNextClick"> 下一步</a-button>
    </a-space>

    <!--  添加角色面板  -->
    <a-modal
      v-model:visible="addRolModalVisible"
      :hide-title="true"
      :footer="false"
      modal-class="role-modal-body"
    >
      <div class="template">
        <div class="title">从模板开始快速创建</div>
        <div class="tags" style="cursor: pointer">
          <a-tag
            v-for="(roleId, index) in roleList"
            :key="index"
            @click="addRole(roleId)"
          >
            {{ roleId }}
          </a-tag>
        </div>
      </div>
      <div class="custom">
        <div class="title">自定义角色名称</div>
        <div class="input">
          <a-input
            v-model="roleName"
            max-length="8"
            show-word-limit
            placeholder="请输入角色名称"
          ></a-input>
        </div>
      </div>
      <div class="button">
        <a-button type="primary" style="width: 100%" @click="addRole()">
          确认
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed, nextTick, onMounted, reactive, ref } from 'vue';
  import { Message, Modal } from '@arco-design/web-vue';
  import ActivityInput from '@/views/activity/activity-create/components/customer/activity-input.vue';
  import ActivityRadio from '@/views/activity/activity-create/components/customer/activity-radio.vue';
  import ActivityCheckbox from '@/views/activity/activity-create/components/customer/activity-checkbox.vue';

  import Sortable from 'sortablejs';

  import ModuleGroup from '@/components/module/group/module-group.vue';
  import SettingGroup from '@/components/module/group/setting-group.vue';
  import Common from '@/components/module/theme-tool/common.vue';
  import BaseInfo from '@/components/module/theme-tool/base-info.vue';
  import TaskInfo from '@/components/module/theme-tool/task-info.vue';
  import UserUpload from '@/components/module/theme-tool/user-upload.vue';
  import { GroupFactory } from '@/components/module/factory/group-factory';
  import { EnumRoleContentType } from '@/components/module/enum/enum-role-content-type';
  import useRoleStore from '@/store/modules/role';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
  import { ResponseGetModuleItemModel } from '@/api/module/type';
  import { IFormData } from '@/views/activity/activity-create/type/model-role-task';

  const { roleList } = useRoleStore();

  const emits = defineEmits(['changeStep']);

  const formData = ref<IFormData[]>([
    {
      id: 'Cos',
      baseInfo: [],
      taskInfo: [],
    },
  ]);

  /** 当前选中角色 的角标 */
  const currentRoleIndex = ref(0);

  /**
   * 角色切换 command
   * @param index {number} formData 列表中的角标
   */
  const roleChange = (index: number) => {
    currentRoleIndex.value = index;
  };

  /**
   * 角色删除 command
   * @param index {number} formData 列表中的角标
   */
  const roleItemRemove = (index: number) => {
    // 判断角色是否多于一个
    if (formData.value.length <= 1) {
      Message.error('请至少保留一个角色');
    } else {
      Modal.confirm({
        title: '确定删除该角色吗？',
        content: '删除后，该角色下的信息将被删除，且无法恢复!',
        okText: '确定',
        cancelText: '取消',
        simple: true,
        onOk: () => {
          // 删除指定位置
          formData.value.splice(index, 1);
          // 判断当前位置是否超过数组长度
          const finalIndex = formData.value.length - 1;
          if (currentRoleIndex.value > finalIndex) {
            currentRoleIndex.value = index;
          } else {
            currentRoleIndex.value = finalIndex;
          }
        },
      });
    }
  };

  /** 创建角色面板 显示控制 */
  const addRolModalVisible = ref(false);
  /**
   * 添加角色 command -- 显示 创建角色面板
   */
  const addRoleClick = () => {
    addRolModalVisible.value = true;
  };
  /** 角色名称编辑 -- 页面编辑 -> 编辑状态控制 */
  const roleNameEditVisible = ref(false);
  /** 角色名称编辑 -- 页面编辑 -> 模块实例 */
  const roleNameEditRef = ref();
  /** 角色名称 */
  const roleName = ref('');

  /**
   * 添加角色 function
   * @param item {ITemplateRoleItem | undefined} 模版信息
   */
  const addRole = async (item?: string) => {
    // todo 开启加载。。。

    await GroupFactory.getInherentGroupModule()
      .then((res) => {
        const roleDate: IFormData = {
          id: '',
          baseInfo: [...res],
          // todo  添加task默认
          taskInfo: [],
        };
        roleDate.id = item ?? roleName.value;

        formData.value.push(roleDate);
        addRolModalVisible.value = false;
        roleName.value = '';
        currentRoleIndex.value = formData.value.length - 1;
      })
      .finally(() => {
        // todo 关闭加载。。。
      });
  };

  /**
   * 角色名称编辑 function
   */
  const roleNameEdit = () => {
    roleNameEditVisible.value = true;
    nextTick(() => {
      roleNameEditRef.value.focus();
    });
  };

  /**
   * 角色复制 function
   */
  const copyRole = () => {
    if (
      formData.value.length === ActivitySettingLimitValue.activityRoleMaxCount
    ) {
      Modal.error({
        title: '角色数量已达上限',
        content: '最多只能创建8个角色',
        okText: '确定',
        simple: true,
      });
      return;
    }
    formData.value.push({
      id: `${formData.value[currentRoleIndex.value].id}`,
      baseInfo: formData.value[currentRoleIndex.value].baseInfo,
      taskInfo: formData.value[currentRoleIndex.value].taskInfo,
    });
    currentRoleIndex.value = formData.value.length - 1;
    roleNameEdit();
  };

  interface ICurrentItemModule {
    /**  当前模块所属显示区域类型：base、task */
    type: EnumRoleContentType;
    /**  当前所属显示区域模块索引 */
    index: number;
  }
  /** 当前选中项 */
  const currentItem = reactive<ICurrentItemModule>({
    type: EnumRoleContentType.BASE,
    index: 0,
  });

  /**  当前选中模块 */
  const currentGroupModule = computed(() => {
    switch (currentItem.type) {
      case EnumRoleContentType.BASE:
        return (
          formData.value[currentRoleIndex.value].baseInfo[currentItem.index]
            ?.entity ?? undefined
        );
      case EnumRoleContentType.TASK:
        return (
          formData.value[currentRoleIndex.value].taskInfo[currentItem.index]
            ?.entity ?? undefined
        );
      default:
        return undefined;
    }
  });

  /**
   * 切换当前选中模块
   * @param index {number} 区域角标
   * @param type  {EnumRoleContentType} 区域类型
   */
  const itemGroupClicked = (index: number, type: EnumRoleContentType) => {
    currentItem.index = index;
    currentItem.type = type;
  };

  /**
   * 添加基础信息项
   * @param attr {ResponseGetModuleItemModel} 组模块属性
   */
  const addBaseItem = (attr: ResponseGetModuleItemModel) => {
    formData.value[currentRoleIndex.value].baseInfo.push(
      GroupFactory.getGroupModuleByAttribute(attr)
    );
    currentItem.type = EnumRoleContentType.BASE;
    currentItem.index =
      formData.value[currentRoleIndex.value].baseInfo.length - 1;
  };

  /**
   * 添加任务信息项
   * @param attr {ResponseGetModuleItemModel} 组模块属性
   */
  const addTaskItem = (attr: ResponseGetModuleItemModel) => {
    const md = GroupFactory.getGroupModuleByAttribute(
      attr,
      EnumRoleContentType.TASK
    );
    formData.value[currentRoleIndex.value].taskInfo.push(md);
    currentItem.type = EnumRoleContentType.TASK;
    currentItem.index =
      formData.value[currentRoleIndex.value].taskInfo.length - 1;
  };

  const onNextClick = async () => {
    emits('changeStep', 'submit', { ...formData.value });
  };
  const goPrev = () => {
    emits('changeStep', 'backward');
  };

  const baseInfoItemListRef = ref();
  const taskInfoItemListRef = ref();

  onMounted(async () => {
    // todo 开启加载。。。
    // 拖动排序  具体参数可查看官网文档
    // eslint-disable-next-line no-new
    new Sortable(baseInfoItemListRef.value, {
      sort: true,
      animation: 150,
      handle: '.group-space-title',
      draggable: '.form-item',
      dataIdAttr: 'data-id',

      onChoose(evt: Sortable.SortableEvent) {
        currentItem.type = EnumRoleContentType.BASE;
        currentItem.index = evt.oldIndex ?? 0;
      },
      // 拖拽完成后更新数据的位置
      async onEnd({ oldIndex, newIndex }: any) {
        const arr = [...formData.value[currentRoleIndex.value].baseInfo];
        const old = formData.value[currentRoleIndex.value].baseInfo[oldIndex];
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, old);
        formData.value[currentRoleIndex.value].baseInfo = arr;
        currentItem.index = newIndex;
        currentItem.type = EnumRoleContentType.BASE;
      },
    });

    // eslint-disable-next-line no-new
    new Sortable(taskInfoItemListRef.value, {
      sort: true,
      animation: 150,
      handle: '.group-space-title',
      draggable: '.form-item',
      dataIdAttr: 'data-id',

      onChoose(evt: Sortable.SortableEvent) {
        currentItem.type = EnumRoleContentType.TASK;
        currentItem.index = evt.oldIndex ?? 0;
      },
      // 拖拽完成后更新数据的位置
      async onEnd({ oldIndex, newIndex }: any) {
        const arr = [...formData.value[currentRoleIndex.value].taskInfo];
        const old = formData.value[currentRoleIndex.value].taskInfo[oldIndex];
        arr.splice(oldIndex, 1);
        arr.splice(newIndex, 0, old);
        formData.value[currentRoleIndex.value].taskInfo = arr;
        currentItem.index = newIndex;
        currentItem.type = EnumRoleContentType.TASK;
      },
    });

    formData.value[currentRoleIndex.value].baseInfo = [];
    await GroupFactory.getInherentGroupModule().then((res) => {
      formData.value[currentRoleIndex.value].baseInfo.push(...res);
    });
    formData.value[currentRoleIndex.value].taskInfo = [];
    // todo 任务模块默认

    // todo 关闭加载。。。
  });
</script>

<style scoped lang="less">
  @import './role-task.scss';
</style>
