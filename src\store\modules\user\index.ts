import { defineStore } from 'pinia';
import {
  login as userLogin,
  logout as userLogout,
  getUserInfo,
  LoginData,
} from '@/api/user';
import {setToken, clearToken, clearMerchant} from '@/utils/auth';
import { removeRouteListener } from '@/utils/route-listener';
import {UserState, UserStore_AuthInfoModel, UserStore_UserInfoModel, UserStoreDataInfoModel} from './types';
import useAppStore from '../app';

const useUserStore = defineStore('user', {
  state: ():UserStoreDataInfoModel => ({
    // 用户信息
    user: {
      proprietorId: '',
      proprietorName: '',
      mobile: '',
      createTime: '',
      merchantId: '',
      principalName: '',
      merchantRoleKey:''
    },
  }),

  getters: {
    userInfo(state: UserStoreDataInfoModel): UserStoreDataInfoModel {
      return { ...state };
    },
  },

  actions: {
    // Set user's information
    setInfo(partial: Partial<UserStoreDataInfoModel>) {
      this.$patch(partial);
    },

    // Reset user's information
    resetInfo() {
      this.$reset();
    },

    // Get user's information
    // async info() {
    //   const res = await getUserInfo();
    //
    //   //this.setInfo(res.data);
    // },

    // Login
    // async login(loginForm: LoginData) {
    //   try {
    //     const res = await userLogin(loginForm);
    //     setToken(res.data.token);
    //   } catch (err) {
    //     clearToken();
    //     throw err;
    //   }
    // },
    logoutCallBack() {
      const appStore = useAppStore();
      this.resetInfo();
      clearToken();
      clearMerchant()
      removeRouteListener();
      appStore.clearServerMenu();
    },
    // Logout
    async logout() {
      this.logoutCallBack();
    },
  },
});

export default useUserStore;
