<template>
  <div class="test-page-center">
    <a-space direction="vertical" :size="10">
      <FormItems v-for="item in items" :key="item.name" :name="item.name">
        <template #content>
          <a-button>{{ item.label }}</a-button>
        </template>
      </FormItems>
    </a-space>
  </div>
</template>

<script setup lang="ts">
  import FormItems from '@/components/popover-items/index.vue';
  import { ref } from 'vue';

  const items = ref([
    { name: undefined, label: '单选' },
    { name: 'CheckboxSelect', label: '多选' },
    { name: 'TextInput', label: '文本' },
    { name: 'FileUpload', label: '文件上传' },
    { name: 'TaskDescription', label: '任务描述' },
    { name: 'CopyText', label: '复制文案' },
    { name: 'OfficialImage', label: '官方图片' },
    { name: 'TaskUpload', label: '任务截图上传' },
    { name: 'PhotoUpload', label: '正片上传' },
    { name: 'VideoUpload', label: '视频上传' },
    { name: 'LinkUpload', label: '链接上传' },
    { name: 'CostumeNote', label: '着装登记' },
  ]);
</script>

<style lang="less" scoped>
  .test-page-center {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
  }
</style>
