import { defineStore } from 'pinia';
import {
  StoreFileTypeStateItemModel,
  StoreFileTypeStateModel,
} from '@/components/module/store/type';
import { ValidateUtils } from '@/utils/validate';
import { FileTypeTemplateData } from '@/components/module/store/template';

const useFileTypeStore = defineStore('fileType', {
  state: (): StoreFileTypeStateModel => ({
    fileTypes: [] as StoreFileTypeStateItemModel[],
  }),

  getters: {
    fileTypesList(): StoreFileTypeStateItemModel[] {
      return this.fileTypes;
    },
  },
  actions: {
    setInfo(partial: Partial<StoreFileTypeStateItemModel[]>) {
      this.$patch({ fileTypes: partial });
    },

    /**
     * 获取模组信息
     */
    async getFileTypes() {
      if (ValidateUtils.isNullOrEmpty(this.fileTypes)) {
        const localComponent = localStorage.getItem('fileType');
        if (ValidateUtils.isNullOrEmpty(localComponent)) {
          // const res = await ModuleApi.getModules();
          // const arr = res.data.map((item) =>
          //   JSON.parse(item!)
          // ) as unknown as AttributeModuleGroup[];
          // this.setInfo(arr);
          // localStorage.setItem('module', JSON.stringify(res.data));

          localStorage.setItem(
            'fileType',
            JSON.stringify(FileTypeTemplateData)
          );
          this.setInfo(FileTypeTemplateData);
        } else {
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          this.setInfo(JSON.parse(localComponent!));
        }
      }
    },
  },
});

export default useFileTypeStore;
