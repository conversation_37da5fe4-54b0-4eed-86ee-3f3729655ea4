export class EnumUtils {
  /**
   * 获取枚举模块格式类型标签
   * @param key
   * @param map
   */
  public static getEnumModuleFormatLabel<T>(
    key: T,
    map: Map<T, string>
  ): string | undefined {
    return map.get(key);
  }

  /**
   * 获取枚举类型列表
   * @param map
   */
  public static getEnumModuleFormatOptionList<T>(map: Map<T, string>): {
    itemKey: T;
    itemLabel: string;
  }[] {
    return Array.from(map.entries()).map(([itemKey, itemLabel]) => {
      return {
        itemKey,
        itemLabel,
      };
    });
  }
}
