<template>
  <div class="task-upload-demo-tip-card">
    <div class="task-upload-demo-tip-title">任务截图上传</div>
    <div class="task-upload-demo-tip-desc">
      任务截图上传用于高效收集用户提交的截图（如活动打卡、任务完成证明等）支持自定义命名及上传数量限制，便于统一管理与审核。
    </div>
    <div class="task-upload-demo-tip-example">
      <div class="task-upload-demo-tip-input demo-first"></div>
      <div class="task-upload-demo-tip-imgs">
        <div class="task-upload-demo-tip-img img-selected">
          <div class="task-upload-demo-tip-img-plus">+</div>
          <div class="task-upload-demo-tip-img-bar"></div>
        </div>
        <div class="task-upload-demo-tip-img">
          <div class="task-upload-demo-tip-img-plus">+</div>
          <div class="task-upload-demo-tip-img-bar"></div>
        </div>
        <div class="task-upload-demo-tip-img">
          <div class="task-upload-demo-tip-img-plus">+</div>
          <div class="task-upload-demo-tip-img-bar"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped lang="less">
  .task-upload-demo-tip-card {
    border-radius: 12px;
    margin-bottom: 0px;
    width: 240px;
  }
  .task-upload-demo-tip-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #000;
  }
  .task-upload-demo-tip-desc {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .task-upload-demo-tip-example {
    background: #e8f3ff;
    border-radius: 8px;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  .task-upload-demo-tip-input {
    width: 84%;
    padding: 0 8px;
    height: 20px;
    background: #6aa1ff;
    border-radius: 6px;
    margin-bottom: 4px;
  }
  .task-upload-demo-tip-imgs {
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%;
    justify-content: center;
    background: #e8f3ff;
    border-radius: 12px;
  }
  .task-upload-demo-tip-img {
    width: 68px;
    height: 93px;
    background: #e8f3ff;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    overflow: hidden;
  }
  .img-selected {
    border: 2.5px solid #4080ff;
    box-sizing: border-box;
  }
  .task-upload-demo-tip-img-plus {
    width: 68px;
    height: 68px;
    background-color: #ffffff;
    border-radius: 10px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 38px;
    color: #94bfff;
    font-weight: 600;
  }
  .task-upload-demo-tip-img-bar {
    width: 80%;
    height: 15px;
    background: #c6e0ff;
    border-radius: 6px;
    margin-bottom: 6px;
  }
</style>
