<script setup lang="ts">
  import {
    computed,
    defineProps,
    nextTick,
    onMounted,
    PropType,
    ref,
  } from 'vue';
  import { ICustomComponent } from '@/components/module/type/interface-custom-module';
  import { ServiceEnumModuleFormatType } from '@/components/module/service/service-enum-module-format-type';
  import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
  import { RequestOption } from '@arco-design/web-vue/es/upload/interfaces';
  import { EnumComponentFormatType } from '@/components/module/enum/enum-component-format-type';
  import { EnumRoleContentType } from '@/components/module/enum/enum-role-content-type';
  import { ActivitySettingLimitValue } from '@/components/module/dp/type-default-limit-value';
  import { StoreFileTypeStateItemModel } from '@/components/module/store/type';
  import useFileTypeStore from '@/components/module/store/file-type';
  import { ValidateUtils } from '@/utils/validate';
  import { ConvertUtils } from '@/utils/convert';
  import { UploadApi } from '@/api/upload/api';

  // 父组件传参
  const props = defineProps({
    /** 模块实体 */
    item: {
      type: Object as PropType<ICustomComponent>,
      required: true,
    },
    /** 当前组，组内参数是否可修改 */
    enabled: {
      type: Boolean,
      required: true,
      default: false,
    },

    space: {
      type: Object as PropType<EnumRoleContentType>,
      required: true,
      default: () => {
        return EnumRoleContentType.BASE;
      },
    },
  });

  const merchantShow = computed(
    () => props.item?.getComponentType() === EnumComponentFormatType.UploadFile
  );

  // 计算属性简化访问
  const moduleAttribute = computed(() =>
    props.item?.getComponentUnifiedAttribute()
  );

  /** 格式 */
  const formatAttr = computed({
    get: () => moduleAttribute.value?.format,
    set: (value) => {
      props.item?.setComponentFormatType(value);
    },
  });

  const lengthLimit = computed(() =>
    ValidateUtils.isValuePositiveNumber(moduleAttribute.value?.length)
  );
  /** 要求上传数量 */
  const lengthAttr = computed({
    get: () => moduleAttribute.value?.length,
    set: (value) => {
      if (!value) {
        value = 0;
      }
      props.item?.setComponentLength(-1);
      nextTick(() => {
        props.item?.setComponentLength(value);
        if (lengthLimit.value) {
          props.item?.setComponentMin(value);
          props.item?.setComponentMax(value);
        }
      });
    },
  });

  /** 最少上传限制 */
  const minAttr = computed({
    get: () => moduleAttribute.value?.min,
    set: (value) => {
      const positiveNumber = ValidateUtils.isValuePositiveNumber(value);
      props.item?.setComponentExpandAttribute(
        'min',
        positiveNumber ? value : -1
      );
      nextTick(() => {
        props.item?.setComponentMin(positiveNumber ? value : 0);
      });
    },
  });
  /** 最大上传限制 */
  const maxAttr = computed({
    get: () => moduleAttribute.value?.max,
    set: (value) => {
      const positiveNumber = ValidateUtils.isValuePositiveNumber(value);
      props.item?.setComponentExpandAttribute(
        'max',
        positiveNumber ? value : -1
      );

      nextTick(() => {
        props.item?.setComponentMax(
          positiveNumber
            ? value
            : ActivitySettingLimitValue.activityModuleUploadFileCountMax
        );
      });
    },
  });
  /** 是否开启图片原图上传。为true则显示勾选且不能取消，为false或者undefined则隐藏 */
  const originalImageAttr = computed(
    () => moduleAttribute.value?.originalImage
  );
  const hasLimitValue = computed(() => {
    return (
      (lengthAttr.value != null && lengthAttr.value > 0) ||
      (maxAttr.value != null && maxAttr.value > 0) ||
      (minAttr.value != null && minAttr.value > 0)
    );
  });

  /** 是否开启文件重命名。 */
  const renamedAttr = computed({
    get: () => moduleAttribute.value?.rename && hasLimitValue.value,
    set: (value) => {
      props.item?.setComponentRename(!!value);
    },
  });
  /** 文件最大大小 */
  const fileMaxSizeAttr = computed({
    get: () => moduleAttribute.value?.fileMaxSize,
    set: (value) => {
      if (!ValidateUtils.isValuePositiveNumber(value)) {
        value = 1;
        props.item?.setComponentExpandAttribute('fileMaxSize', -1);
      }
      nextTick(() => {
        props.item?.setComponentFileMaxSize(value);
      });
    },
  });
  /** 示例图片 */
  const exampleImagesAttr = computed({
    get: () => moduleAttribute.value?.exampleImages,
    set: (value) => {
      props.item?.setComponentExpandAttribute('exampleImages', value);
    },
  });
  /** 本地-示例图片 */
  const fileList = ref<string[] | undefined>(
    moduleAttribute.value?.exampleImages
  );

  /** 文件类型 */
  const fileTypesAttr = computed({
    get: () => moduleAttribute.value?.fileTypes,
    set: (value) => {
      props.item?.setComponentExpandAttribute('fileTypes', value);
    },
  });
  /** 本地-文件类型 */
  const localFileTypesAttr = ref<string[] | undefined>([]);
  /** 文件类型限制开关 */
  const fileTypeLimitFlag = ref(ValidateUtils.isNotEmpty(fileTypesAttr.value));

  /**
   * 文件类型选择值改变
   * @param value
   */
  const handleFileTypeChanged = (value: any) => {
    fileTypesAttr.value = value;
  };

  const defaultURL = 'https://manxing-myxq.oss-cn-shanghai.aliyuncs.com';
  const defaultFileURL = 'DTest/';
  /** 上传示例 */
  const uploadFile = async (option: RequestOption) => {
    const { onProgress, onError, onSuccess, fileItem } = option;
    const fileName = fileItem.name;
    const fileRef = fileItem.file;

    if (!fileRef || !fileName) {
      alert('未找到文件');
      return;
    }

    const res = await UploadApi.GetALiOssUploadInfo();
    const resData = JSON.parse(res.data);
    const ossFormData = new FormData();
    ossFormData.append('key', defaultFileURL + fileName);
    ossFormData.append('policy', resData.policy);
    ossFormData.append(
      'x-oss-signature-version',
      resData.x_oss_signature_version
    );
    ossFormData.append('x-oss-credential', resData.x_oss_credential);
    ossFormData.append('x-oss-date', resData.x_oss_date);
    ossFormData.append('x-oss-signature', resData.signature);
    ossFormData.append('x-oss-security-token', resData.security_token);
    ossFormData.append('success_action_status', '200');
    ossFormData.append('file', fileRef);

    const xhr = new XMLHttpRequest();
    xhr.upload.onprogress = (e) => {
      if (e.lengthComputable) {
        let percent = 0;
        if (e.total > 0) {
          // 0 ~ 1
          percent = e.loaded / e.total;
        }
        onProgress(percent, e);
      }
    };
    xhr.onload = () => {
      if (xhr.status === 200 || xhr.status === 204) {
        console.log('✅ 上传成功');
        onSuccess(xhr.response);
        const dURL = `${defaultURL}\\${defaultFileURL}${fileName}`;
        if (!fileList.value) {
          fileList.value = [];
        }
        fileList.value.push(dURL);
        exampleImagesAttr.value = fileList.value;
      } else {
        console.error('❌ 上传失败:', xhr.responseText);
        onError(xhr.responseText);
      }
    };
    xhr.onerror = () => {
      console.error('🌐 网络错误');
      onError(xhr.responseText);
    };
    xhr.open('POST', defaultURL, true);
    xhr.send(ossFormData);
  };

  /** 文件类型树 */
  const fileTypeTreeData = ref<StoreFileTypeStateItemModel[]>([]);

  /**
   * 文件类型限制开关值改变
   * @param e
   */
  const handleChangedFileTypeLimit = (e: boolean) => {
    if (e) {
      localFileTypesAttr.value = fileTypesAttr.value ?? [];
    } else {
      localFileTypesAttr.value = [];
    }
  };

  onMounted(async () => {
    const uFileTypeStore = useFileTypeStore();
    await uFileTypeStore.getFileTypes().then(() => {
      fileTypeTreeData.value = uFileTypeStore.fileTypesList;
    });
  });
</script>

<template>
  <div class="form-format-content">
    <a-form layout="vertical">
      <!-- 格式选择 -->
      <a-form-item v-if="formatAttr" label="格式选择">
        <a-select v-model="formatAttr" :disabled="!props.enabled">
          <a-option
            v-for="(
              optionItem, optionIndex
            ) in ServiceEnumModuleFormatType.GetUploadFormatTypeOptionList()"
            :key="optionIndex"
            :value="optionItem.itemKey"
            :label="optionItem.itemLabel"
          />
        </a-select>
      </a-form-item>

      <!-- 上传文件类型限制下拉 -->
      <a-form-item v-if="merchantShow" label="上传文件类型">
        <a-select
          v-model="fileTypeLimitFlag"
          :disabled="!props.enabled"
          @change="handleChangedFileTypeLimit"
        >
          <a-option :value="false">不限格式</a-option>
          <a-option :value="true">限制格式</a-option>
        </a-select>
      </a-form-item>

      <!-- 上传文件格式 -->
      <a-form-item
        v-if="merchantShow && fileTypeLimitFlag"
        label="上传文件格式"
      >
        <a-tree-select
          v-model="localFileTypesAttr"
          placeholder="请选择文件格式限制"
          allow-search
          allow-clear
          tree-checkable
          :disabled="!props.enabled"
          :max-tag-count="
            ActivitySettingLimitValue.activityModuleUploadFileTypeShowCountMax
          "
          :data="fileTypeTreeData"
          @change="handleFileTypeChanged"
        />
      </a-form-item>

      <!-- 单个文件上传上限 -->
      <a-form-item
        v-if="merchantShow && fileMaxSizeAttr != null"
        label="单个文件上传上限(MB)"
      >
        <a-input-number
          v-model="fileMaxSizeAttr"
          :disabled="!props.enabled"
          :max="ActivitySettingLimitValue.activityModuleUploadFileSizeMax"
          :min="1"
          :precision="0"
        />
      </a-form-item>
      <!-- 上传需求数量 -->
      <a-form-item v-if="merchantShow && lengthAttr != null" label="上传数量">
        <a-input-number
          v-model="lengthAttr"
          :disabled="!props.enabled"
          :max="ActivitySettingLimitValue.activityModuleUploadFileCountMax"
          :min="-1"
          :precision="0"
        />
      </a-form-item>
      <!-- 最少上传 -->
      <a-form-item v-if="merchantShow && minAttr != null" label="最少上传">
        <a-input-number
          v-model="minAttr"
          :disabled="!props.enabled"
          :max="ActivitySettingLimitValue.activityModuleUploadFileCountMax"
          :min="0"
          :precision="0"
        />
      </a-form-item>
      <!-- 最多上传 -->
      <a-form-item v-if="merchantShow && maxAttr != null" label="最多上传">
        <a-input-number
          v-model="maxAttr"
          :disabled="!props.enabled"
          :max="ActivitySettingLimitValue.activityModuleUploadFileCountMax"
          :min="0"
          :precision="0"
        />
      </a-form-item>

      <!-- 图片命名 -->
      <a-form-item v-if="renamedAttr !== undefined">
        <a-checkbox
          v-model="renamedAttr"
          :disabled="!props.enabled || !hasLimitValue"
        >
          <div class="title">图片命名</div>
        </a-checkbox>
      </a-form-item>

      <!-- 要求用户上传原图 -->
      <a-form-item v-if="originalImageAttr !== undefined">
        <a-checkbox :model-value="true" disabled>
          <div class="title">要求用户上传原图</div>
        </a-checkbox>
      </a-form-item>

      <!-- 上传示例图 -->
      <a-form-item
        v-if="props.space !== EnumRoleContentType.TASK && exampleImagesAttr"
      >
        <div class="template-block">
          <div class="title">上传示例图</div>
          <a-upload
            list-type="picture-card"
            action="/"
            :default-file-list="fileList"
            :custom-request="uploadFile"
            image-preview
            multiple
            :limit="ActivitySettingLimitValue.activityModuleUploadFileCountMax"
          >
            <template #upload-button>
              <div class="arco-upload-list-item">
                <div class="arco-upload-picture-card">
                  <div class="arco-upload-picture-card-text">
                    <IconUpload size="20" />
                    <div
                      style="
                        margin-top: 5px;
                        font-weight: 400;
                        font-size: 12px;
                        line-height: 20px;
                      "
                    >
                      添加图片</div
                    >
                  </div>
                </div>
              </div>
            </template>
          </a-upload>
        </div>
      </a-form-item>
    </a-form>
  </div>
</template>

<style scoped lang="less"></style>
