export class ConvertUtils {
  /**
   * 深拷贝数据
   * @param obj - 要拷贝的对象
   * @constructor
   */
  public static DeepCopy(obj: any): any {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * 尝试将传入值解析为 JSON 并验证是否符合指定类型 T。
   *
   * @param value 要解析的值（字符串或对象）
   * @param validatorT 可选的类型校验函数，用于判断是否为 T 类型
   * @returns 如果解析成功并符合 T 类型，则返回该对象；否则返回 undefined
   */
  public static convertJson2Object<T>(
    value: unknown,
    validatorT?: (item: unknown) => boolean
  ): T | undefined {
    // 如果已经是对象，直接使用
    if (typeof value === 'object' && value !== null) {
      // 如果没有传校验器，直接返回 as T
      if (!validatorT) {
        return value as T;
      }
      // 否则进行校验
      return validatorT(value) ? (value as T) : undefined;
    }

    // 如果是字符串，尝试解析为 JSON
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        if (!validatorT) {
          return parsed as T;
        }
        return validatorT(parsed) ? (parsed as T) : undefined;
      } catch (e) {
        return undefined;
      }
    }

    // 其他情况无法解析为对象
    return undefined;
  }

  /**
   * 尝试将传入的 JSON 字符串解析为对象，并判断其是否是一个数组。
   *
   * @param value 要解析的 JSON 字符串
   * @returns 如果解析成功且为数组，返回 true；否则返回 false
   */
  public static convertJson2Array<T>(value: unknown): T[] {
    return this.convertJson2Object(value, (item) => Array.isArray(item)) ?? [];
  }
}
