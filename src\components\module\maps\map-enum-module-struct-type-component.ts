import { DefineComponent } from 'vue';
import ActivityInputText from '@/components/module/theme-show/activity-input-text.vue';
import ActivitySingleMultiple from '@/components/module/theme-show/activity-single-multiple.vue';
import ActivityDateTime from '@/components/module/theme-show/activity-date-time.vue';
import ActivityUploadImage from '@/components/module/theme-show/activity-upload-image.vue';
import { EnumComponentStructType } from '@/components/module/enum/enum-component-struct-type';
import InputText from '@/components/module/theme-setting/input-text.vue';
import SingleMultiple from '@/components/module/theme-setting/single-multiple.vue';
import DateTime from '@/components/module/theme-setting/date-time.vue';
import UploadFile from '@/components/module/theme-setting/upload-file.vue';

export const MapEnumModuleStructTypeComponent = {
  MapOfShowComponent: new Map<EnumComponentStructType, DefineComponent>([
    [EnumComponentStructType.TEXT, ActivityInputText as DefineComponent],
    [EnumComponentStructType.TEXTAREA, ActivityInputText as DefineComponent],
    [EnumComponentStructType.DATETIME, ActivityDateTime as DefineComponent],
    [
      EnumComponentStructType.CHECKBOX,
      ActivitySingleMultiple as DefineComponent,
    ],
    [EnumComponentStructType.RADIO, ActivitySingleMultiple as DefineComponent],
    [EnumComponentStructType.UPLOAD, ActivityUploadImage as DefineComponent],
  ]),

  MapOfSettingComponent: new Map<EnumComponentStructType, DefineComponent>([
    [EnumComponentStructType.TEXT, InputText as DefineComponent],
    [EnumComponentStructType.TEXTAREA, InputText as DefineComponent],
    [EnumComponentStructType.DATETIME, DateTime as DefineComponent],
    [EnumComponentStructType.CHECKBOX, SingleMultiple as DefineComponent],
    [EnumComponentStructType.RADIO, SingleMultiple as DefineComponent],
    [EnumComponentStructType.UPLOAD, UploadFile as DefineComponent],
  ]),
};
