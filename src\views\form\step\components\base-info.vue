<template>
  <a-form
    ref="formRef"
    :model="formData"
    class="form"
    :label-col-props="{ span: 6 }"
    :wrapper-col-props="{ span: 18 }"
  >
    <a-form-item
      field="productName"
      :label="$t('stepForm.step.productName')"
      :rules="[{ required: true, message: $t('stepForm.step.productName') }]"
    >
      <a-input
        v-model="formData.productName"
        :placeholder="$t('stepForm.step.productName.placeholder')"
      />
    </a-form-item>
    <a-form-item
      field="couponsName"
      :label="$t('stepForm.step.couponsName')"
      :rules="[{ required: true, message: $t('stepForm.step.couponsName') }]"
    >
      <a-input
        v-model="formData.couponsName"
        :placeholder="$t('stepForm.step.couponsName.placeholder')"
      />
    </a-form-item>
    <a-form-item
      field="uploadInformation"
      :label="$t('stepForm.step.uploadInformation')"
      label-align="left"
      auto-label-width
      :label-col-style="{
        position: 'relative',
        padding: '0px',
        left: '-16px',
      }"
      :rules="[
        {
          required: true,
          message: $t('stepForm.step.uploadInformation'),
          validator: (value, cb) => {
            if (!formData.uploadedFile) {
              cb($t('stepForm.upload.required'));
            }
            cb();
          },
        },
      ]"
    >
      <div class="upload-section">
        <a-button type="outline" style="margin-bottom: 8px">
          <icon-download />
          {{ $t('stepForm.upload.template.download') }}
        </a-button>
        <a-upload
          :auto-upload="false"
          accept=".xlsx,.xls,.csv"
          @change="handleFileChange"
        >
          <template #upload-button>
            <div class="upload-dragger">
              <div class="upload-excel-icon">
                <img
                  src="/src/assets/images/excel.svg"
                  alt="excel"
                  style="height: 48px; margin-bottom: 14px"
                />
              </div>
              <div class="upload-tip">
                <span style="color: #165dff"
                  >{{ $t('stepForm.upload.drag.tip')
                  }}<a-text
                    style="
                      border: 1px solid #165dff;
                      padding: 5px;
                      border-radius: 5px;
                    "
                  >
                    {{ $t('stepForm.upload.drag.tip.button') }}
                  </a-text></span
                >
              </div>
            </div>
          </template>
        </a-upload>
        <div class="upload-suggest">
          <span>{{ $t('stepForm.upload.suggest') }}</span
          ><br />
          <span>{{ $t('stepForm.upload.suggest.down') }}</span>
        </div>
        <div class="upload-error" v-if="formData.analysis">
          <div v-if="formData.analysisState"
            ><span style="color: #23c343">
              {{
                $t('stepForm.upload.success')
              }}&nbsp;<icon-check-circle-fill /> </span
            ><br />
            <div style="color: #4e5969; margin-top: 5px"
              >已解析成功{{ formData.analysisSuccessSum }}条记录，共发{{
                formData.analysisSuccessSum
              }}张券码，失败{{ formData.analysisFailSum }}条</div
            >
          </div>
          <span v-else style="color: #f53f3f"
            >{{ $t('stepForm.upload.failed') }}&nbsp;<icon-close-circle-fill
          /></span>
          <a-table
            row-key="id"
            style="margin-top: 10px"
            :loading="loading"
            :pagination="{
              current: userPagination.current,
              pageSize: userPagination.pageSize,
              total: userPagination.total,
              showTotal: true,
              showJumper: false,
              showPageSize: true,
              pageSizeOptions: [10, 20, 50, 100],
              onPageSizeChange: onUserPageSizeChange,
              size: 'small',
            }"
            :columns="userColumns"
            :data="renderExamineData"
            :bordered="false"
            :size="size"
            @page-change="onUserPageChange"
          >
          </a-table>
          <a-button
            type="primary"
            style="margin-top: 20px"
            @click="visible = !visible"
            >发放二维码</a-button
          >
          <!-- <a-button type="primary" @click="exportExcelOrCsv('excel')"
            >导出数据</a-button
          > -->
        </div>
        <a-modal
          :visible="visible"
          :footer="false"
          hide-title
          :width="520"
          @ok="handleOk"
          @cancel="handleCancel"
        >
          <div>
            <div
              style="
                display: flex;
                gap: 8px;
                justify-content: center;
                align-items: center;
                margin-bottom: 20px;
              "
            >
              <icon-exclamation-circle
                style="color: #faad14; font-size: 20px"
              />
              <span style="font-size: 18px; font-weight: 600; color: #222">
                是否确认发放券码
              </span>
            </div>
            <div style="color: #222; font-size: 15px; text-align: left">
              发放券码后对应电话号码的漫星账号将会收到对应日期的门票券码，此操作无法撤回！
            </div>
            <div style="font-size: 14px; text-align: left; margin-bottom: 32px">
              您可以在<span style="font-weight: 600"
                >订单管理-邀请票订单详情</span
              >内查看用户的券码状态或作废其券码。
            </div>
            <div style="display: flex; justify-content: center; gap: 16px">
              <a-button
                style="
                  min-width: 88px;
                  height: 38px;
                  border-radius: 6px;
                  font-size: 16px;
                "
                @click="handleCancel"
                >取消</a-button
              >
              <a-button
                type="primary"
                style="
                  min-width: 88px;
                  height: 38px;
                  border-radius: 6px;
                  font-size: 16px;
                "
                @click="handleOk"
                >确定</a-button
              >
            </div>
          </div>
        </a-modal>
      </div>
    </a-form-item>
  </a-form>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import * as XLSX from 'xlsx';

  // modal 控制
  const visible = ref(false);
  const handleOk = () => {
    visible.value = false;
  };
  const handleCancel = () => {
    visible.value = false;
  };

  const formRef = ref<any>();
  const formData = ref<any>({
    activityName: '',
    channelType: '',
    promotionTime: [],
    promoteLink: 'https://arco.design',
    productName: '',
    couponsName: '',
    uploadInformation: '',
    uploadedFile: null,
    analysis: false,
    analysisState: false,
    analysisSum: 0,
    analysisSuccessSum: 0,
    analysisFailSum: 0,
  });

  // 表格相关
  const loading = ref(false);
  const renderExamineData = ref<any[]>([]);
  const userPagination = reactive({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const size = ref<'mini' | 'small' | 'medium' | 'large'>('small');
  const userColumns = computed(() => [
    { title: '用户CN', dataIndex: 'userCN' },
    { title: '手机号', dataIndex: 'phoneNumber' },
    { title: '日期', dataIndex: 'date' },
  ]);

  // 翻页处理函数
  const onUserPageChange = (current: number) => {
    userPagination.current = current;
  };

  const onUserPageSizeChange = (pageSize: number) => {
    userPagination.pageSize = pageSize;
    userPagination.current = 1; // 重置到第一页
  };

  // 导出Excel
  const exportExcelOrCsv = (type: string) => {
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(renderExamineData.value);
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
    XLSX.writeFile(wb, `导出数据.${type === 'csv' ? 'csv' : 'xlsx'}`);
  };

  // 文件上传处理方法
  const importCommon = (fileItem: any) => {
    const { file } = fileItem;

    if (!file) {
      console.log('没有找到文件');
      return;
    }

    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        if (!e.target?.result) {
          console.log('文件读取失败');
          return;
        }

        const data = new Uint8Array(e.target.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];

        // 生成json数据渲染到页面
        const jsonData = XLSX.utils.sheet_to_json(firstSheet);
        console.log('导入数据:', jsonData);

        // 计算解析数据统计
        const validData = jsonData.filter(
          (item: any) => item.userCN && item.phoneNumber && item.date
        );

        formData.value.analysisSum = jsonData.length;
        formData.value.analysisSuccessSum = validData.length;
        formData.value.analysisFailSum = jsonData.length - validData.length;

        // 将新数据添加到现有数据中
        renderExamineData.value = [...jsonData, ...renderExamineData.value];

        // 更新分页信息
        userPagination.total = renderExamineData.value.length;
        userPagination.current = 1; // 重置到第一页

        console.log('数据处理完成，总条数:', userPagination.total);
        formData.value.analysisState = true;
      } catch (error) {
        console.log('文件解析失败:', error);
      }
    };

    reader.readAsArrayBuffer(file);
  };
  const handleFileChange = (fileList: any[], fileItem: any) => {
    console.log('文件变化:', fileList, fileItem);
    formData.value.analysis = false;

    // 更新表单验证状态
    formData.value.uploadedFile = fileList.length > 0 ? fileList[0] : null;
    formRef.value?.validateField('uploadInformation');

    // 如果有文件，处理文件数据
    if (fileItem && fileItem.file && fileList.length > 0) {
      importCommon(fileItem);
      formData.value.analysis = true;
    }
  };
</script>

<style scoped lang="less">
  .container {
    padding: 20px;
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 64px 0;
    background-color: var(--color-bg-2);
  }

  .form {
    width: 800px;
  }

  .form-content {
    padding: 8px 50px 0 30px;
  }

  .upload-section {
    font-weight: 500;
    margin-bottom: 24px;
  }

  .upload-dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background: #fafafa;
    min-width: 400px;
    min-height: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
  }

  .upload-dragger:hover {
    background-color: #f5f6f7;
    transition: background-color 0.3s ease;
  }

  .upload-excel-icon {
    margin-bottom: 8px;
  }

  .upload-tip {
    font-size: 16px;
  }

  .upload-suggest {
    font-size: 14px;
    line-height: 20px;
    color: #999;
    margin: 10px 0;
  }

  .upload-error {
    font-size: 12px;
    color: #f53f3f;
  }

  :deep(.arco-upload-drag) {
    height: 200px;
  }
  :deep(.arco-table-container) {
    max-height: 370px;
    overflow: auto;
  }
</style>
