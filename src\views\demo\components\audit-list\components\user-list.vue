<template>
  <div class="user-list">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>

    <div v-else-if="users.length === 0" class="empty-container">
      <a-empty description="暂无数据" />
    </div>

    <div v-else>
      <div
        v-for="user in users"
        :key="user.id"
        class="user-item"
        :class="{ 'user-item-active': user.id === activeUserId }"
        @click="activeUserId = user.id"
      >
        <!-- 用户头像 -->
        <div class="user-avatar">
          <a-avatar v-if="user.avatar" :src="user.avatar" :size="26" />
          <a-avatar v-else :size="26" class="default-avatar">
            {{ user.name.charAt(0) }}
          </a-avatar>
        </div>

        <!-- 用户信息 -->
        <div class="user-info">
          <div class="user-name">{{ user.name }}</div>
        </div>

        <!-- 操作按钮 -->
        <div class="user-actions">
          <a-button
            :type="getActionButtonType(user)"
            :class="getActionButtonClass(user)"
            size="small"
          >
            {{ getActionLabel(user) }}
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import type { User } from '../types';

  // Props
  interface Props {
    users: User[];
    loading: boolean;
  }

  const props = defineProps<Props>();

  // Emits
  interface Emits {
    (e: 'action', action: string, user: User): void;
  }

  const emit = defineEmits<Emits>();

  // 响应式数据
  const activeUserId = ref('');

  // 方法
  const handleAction = (user: User) => {
    let action = '';

    emit('action', action, user);
  };

  const getActionButtonType = (
    user: User
  ): 'primary' | 'secondary' | 'outline' | 'dashed' | 'text' => {
    // 所有按钮都使用 primary 类型，通过 class 控制样式
    return 'primary';
  };

  const getActionButtonClass = (user: User) => {
    const baseClass = 'action-button';
    const label = user.actionLabel || '';

    // 根据按钮标签内容设置不同样式
    switch (label) {
      case '太长的...':
        return `${baseClass} danger-button`; // 红色/橙色系
      case '太长的角色名':
        return `${baseClass} blue-button`; // 蓝色系
      case '摄影':
        return `${baseClass} primary-button`; // 蓝色系
      case 'COSER':
        return `${baseClass} light-blue-button`; // 浅蓝色系
      case 'LOLITA':
        return `${baseClass} orange-button`; // 橙色系
      case '汉服':
        return `${baseClass} yellow-button`; // 黄色系
      case '三坑':
        return `${baseClass} red-button`; // 红色系
      case '兽装':
        return `${baseClass} pink-button`; // 粉色系
      default:
        // 其他情况根据状态设置
        switch (user.status) {
          case 'pending':
            return `${baseClass} gray-button`;
          case 'approved':
            return `${baseClass} success-button`;
          case 'rejected':
            return `${baseClass} danger-button`;
          default:
            return `${baseClass} default-button`;
        }
    }
  };

  const getActionLabel = (user: User) => {
    // 如果有自定义的操作标签，使用它
    if (user.actionLabel) {
      return user.actionLabel;
    }

    // 否则根据状态返回默认标签
    switch (user.status) {
      case 'pending':
        return '审批';
      case 'approved':
        return '已通过';
      case 'rejected':
        return '已拒绝';
      default:
        return '查看';
    }
  };
</script>

<script lang="ts">
  export default {
    name: 'UserList',
  };
</script>

<style lang="less" scoped>
  .user-list {
    height: calc(100% - 180px);
    overflow: auto;
    margin: 0 0 56px 0;
    padding: 0 12px;

    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .loading-container,
  .empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .user-item {
    display: flex;
    align-items: center;
    background-color: #f2f3f5;
    padding: 5px 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .user-item-active,
  .user-item:hover {
    background-color: #e8f3ff;
  }

  .user-item:last-child {
    border-bottom: none;
  }

  .user-avatar {
    margin-right: 12px;
  }

  .default-avatar {
    background: #e5e6eb;
    color: #86909c;
  }

  .user-info {
    flex: 1;
    min-width: 0;
  }

  .user-name {
    font-size: 14px;
    color: #4e5969;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .user-actions {
    margin-left: 12px;
  }

  .action-button {
    min-width: 40px;
    border-radius: 2px;
    font-size: 12px;
    font-weight: 400;
    border: none;
    padding: 4px 8px;
    height: 24px;
    line-height: 16px;
  }

  /* 根据图片重新设计的按钮样式 */
  .danger-button {
    background: #ff7d95 !important;
    border-color: #ff7d95 !important;
    color: white !important;
  }

  .blue-button {
    background: #4080ff !important;
    border-color: #4080ff !important;
    color: white !important;
  }

  .primary-button {
    background: #4080ff !important;
    border-color: #4080ff !important;
    color: white !important;
  }

  .light-blue-button {
    background: #7cb3ff !important;
    border-color: #7cb3ff !important;
    color: white !important;
  }

  .orange-button {
    background: #ff9a2e !important;
    border-color: #ff9a2e !important;
    color: white !important;
  }

  .yellow-button {
    background: #f7ba1e !important;
    border-color: #f7ba1e !important;
    color: white !important;
  }

  .red-button {
    background: #f53f3f !important;
    border-color: #f53f3f !important;
    color: white !important;
  }

  .pink-button {
    background: #ffb8c5 !important;
    border-color: #ffb8c5 !important;
    color: white !important;
  }

  .gray-button {
    background: #86909c !important;
    border-color: #86909c !important;
    color: white !important;
  }

  .success-button {
    background: #00b894 !important;
    border-color: #00b894 !important;
    color: white !important;
  }

  .default-button {
    background: #e5e6eb !important;
    border-color: #e5e6eb !important;
    color: #4e5969 !important;
  }

  /* 悬停效果 */
  .user-item:hover {
    .danger-button {
      background: #ff5571 !important;
      border-color: #ff5571 !important;
    }

    .blue-button,
    .primary-button {
      background: #3366ff !important;
      border-color: #3366ff !important;
    }

    .light-blue-button {
      background: #5599ff !important;
      border-color: #5599ff !important;
    }

    .orange-button {
      background: #ff8800 !important;
      border-color: #ff8800 !important;
    }

    .yellow-button {
      background: #f5a623 !important;
      border-color: #f5a623 !important;
    }

    .red-button {
      background: #d91e1e !important;
      border-color: #d91e1e !important;
    }

    .pink-button {
      background: #ff99aa !important;
      border-color: #ff99aa !important;
    }

    .gray-button {
      background: #6b7785 !important;
      border-color: #6b7785 !important;
    }

    .success-button {
      background: #00a085 !important;
      border-color: #00a085 !important;
    }
  }

  /* 移除之前的样式 */
  :deep(.arco-btn) {
    transition: all 0.2s ease;
  }
</style>
