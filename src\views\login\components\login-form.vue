<template>
  <div class="login-form-wrapper">
    <div class="login-form-title">{{ $t('login.form.title') }}</div>
    <div class="login-form-sub-title">{{ $t('login.form.title') }}</div>
    <div class="login-form-error-msg">{{ errorMessage }}</div>
    <a-form
      ref="loginFormRef"
      :model="loginFormData"
      class="login-form"
      layout="vertical"
      :rules="rules"
    >
      <a-form-item field="mobile" hide-label>
        <a-input v-model="loginFormData.mobile" placeholder="请输入手机号">
          <template #prefix>
            <icon-mobile />
          </template>
        </a-input>
      </a-form-item>
      <a-form-item field="code" hide-label>
        <a-input
          v-model="loginFormData.code"
          placeholder="请输入验证码"
          allow-clear
        >
          <template #prepend> +86 </template>
        </a-input>
        <a-button
          v-if="!sms.disabled"
          type="primary"
          style="margin-left: 10px"
          @click="handleGetSMSCode"
          >获取验证码</a-button
        >
        <a-button v-else disabled>{{ sms.count }} 秒后重新发送</a-button>
      </a-form-item>
      <a-space :size="16" direction="vertical">
        <a-button type="primary" html-type="submit" long @click="handleLogin">
          {{ $t('login.form.login') }}
        </a-button>
      </a-space>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { Message } from '@arco-design/web-vue';
  import { ValidatedError } from '@arco-design/web-vue/es/form/interface';
  import { useI18n } from 'vue-i18n';
  import { useStorage } from '@vueuse/core';
  import { useUserStore } from '@/store';
  import useLoading from '@/hooks/loading';
  import type { LoginData } from '@/api/user';
  import { LoginAPI } from '@/api/auth';
  import { ValidateUtils } from '@/utils/validate';
  import { RegularExpression, RegularValidate } from '@/utils/reg-validate';
  import { Request_MobileCodeLoginParamsModel } from '@/types/api-type/auth';
  import { setToken } from '@/utils/auth';

  const router = useRouter();
  const { t } = useI18n();
  const errorMessage = ref('');
  const { loading, setLoading } = useLoading();
  const userStore = useUserStore();

  const loginFormRef = ref();

  const emit = defineEmits(['next']);

  const loginFormData = reactive<Request_MobileCodeLoginParamsModel>({
    mobile: '',
    code: '',
    device: 'web',
  });

  const rules = {
    mobile: [
      {
        required: true,
        message: t('请输入手机号'),
        trigger: ['change', 'blur'],
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: '手机号格式不正确',
        trigger: ['change', 'blur'],
      },
    ],
    code: [
      {
        required: true,
        message: '请输入验证码',
        trigger: ['blur'],
      },
    ],
  };

  const handleGetSMSCode = () => {
    loginFormRef.value
      .validateField('mobile', (disValid: boolean) => {
        if (!disValid) {
          LoginAPI.GetSMSVerificationCode(loginFormData.mobile).then(
            (res: any) => {
              timerHandler();
            }
          );
        }
      })
      .finally(() => {
        sms.disabled = false;
      });
  };

  // 短信计时器
  const sms = reactive({
    disabled: false,
    total: 60,
    count: 0,
  });

  // 计时器处理器
  const timerHandler = () => {
    sms.count = sms.total;
    sms.disabled = true;

    const timer = setInterval(() => {
      if (sms.count > 1 && sms.count <= sms.total) {
        sms.count--;
      } else {
        sms.disabled = false;
        clearInterval(timer);
      }
    }, 1000);
  };

  /**
   * 登录命令
   */
  const handleLogin = async () => {
    await loginFormRef.value.validate((valid: boolean) => {
      if (valid) {
      }
    });

    LoginAPI.UseMobileLogin(loginFormData).then((res) => {
      setToken(res.data.token);
      // localStorage.setItem('mobile', loginFormData.mobile)
      if (res.data.merchantNumber === 0) {
        // router.push({ path: '/merchantInfo' })
        Message.warning('请先添加商户信息');
      } else {
        emit('next', {
          mobile: loginFormData.mobile,
        });
      }
    });
  };
</script>

<style lang="less" scoped>
  .login-form {
    &-wrapper {
      width: 320px;
    }

    &-title {
      color: var(--color-text-1);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;
    }

    &-sub-title {
      color: var(--color-text-3);
      font-size: 16px;
      line-height: 24px;
    }

    &-error-msg {
      height: 32px;
      color: rgb(var(--red-6));
      line-height: 32px;
    }

    &-password-actions {
      display: flex;
      justify-content: space-between;
    }

    &-register-btn {
      color: var(--color-text-3) !important;
    }
  }
</style>
