import { EnumFileUploadStatue } from '@/components/module/enum/show-component-attribute-enum';

/**
 * 显示组件-上传文件进度模型
 */
export type ShowComponentUploadProgressModel = {
  // 是否上传成功
  status: EnumFileUploadStatue;
  // 上传binary进度
  percent: number;
};

/**
 * 显示组件-上传文件 项目模型
 */
export interface ShowComponentUploadFileItemModel {
  /**
   * 重命名
   */
  label: string;
  /**
   * 文件地址
   */
  url: string;
  /**
   * 上传状态与进度
   */
  progress: ShowComponentUploadProgressModel;
}
