export interface Response_MerchantValidGoods{
    goodsId:string;
    goodsName:string;
}




export interface Response_InvitationExcelInfoModel{
    /**
     * 商户邀请票id
     */
    merchantInvitationId:string;
    /**
     * 识别总条数
     */
    analysisTotalCount:number;
    /**
     * 识别成功条数
     */
    analysisSuccessCount:number;
    /**
     * 识别失败条数
     */
    analysisFailCount:number;

    /**
     * 预发放卷码数，每天/每人/每spu一张
     */
    provideCouponCount:number;

    excelDTOS:ExcelItemModel[];
}

export interface ExcelItemModel{
    /**
     * CN
     */
    cn:string;
    /**
     * 手机号码
     */
    mobile:string;
    /**
     * 报名日期 - excel文字版
     */
    signUpDate: string;
    /**
     * 实名-姓名
     */
    name?:string;
    /**
     * 实名-身份证
     */
    idCard?:string;
    /**
     * 有效时间
     */
    analysisSuccessDateList:string[];
}



export interface Request_SubmitInvitationTicketModel{
    /**
     * 邀请票次序id
     */
    merchantInvitationId:string;
    /**
     * 商品id
     */
    goodsId:string;
    /**
     * 卷名称
     */
    goodsItemName:string;
}